import { supabase } from '../config/db.js';
import { emailUtils } from '../utils/emailUtils.js';
import { logger } from '../utils/logger.js';

const handleServiceError = (error, message) => {
  logger.error(`${message}:`, error);
  throw new Error(message);
};

export const offerService = {
  async getOffers(filters = {}, page = 1, limit = 10) {
    try {
      const start = (page - 1) * limit;
      const end = start + limit - 1;

      let query = supabase
        .from('offers')
        .select(`
          *,
          candidate:users!user_id(full_name, email),
          job:job_posts!job_post_id(title, department)
        `, { count: 'exact' })
        .eq('flag_deleted', false);

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) query = query.eq(key, value);
      });

      const { data: offers, error, count } = await query
        .order('created_at', { ascending: false })
        .range(start, end);

      if (error) throw error;

      return {
        offers,
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      handleServiceError(error, "Failed to retrieve offers");
    }
  },

  async generateOffer(offerData) {
    try {
      // Generate offer letter using AI
      const offerLetter = await aiIntegrator.generateOfferLetter({
        candidate: offerData.user_id,
        job: offerData.job_post_id,
        salary: offerData.salary,
        benefits: offerData.benefits
      });

      // Store offer letter in Supabase storage
      const { data: storedLetter, error: storageError } = await supabase.storage
        .from("offer-letters")
        .upload(
          `${offerData.user_id}/${offerData.job_post_id}/offer-letter.pdf`,
          offerLetter,
          { contentType: "application/pdf" }
        );

      if (storageError) throw storageError;

      // Create offer in Supabase
      const { data: offer, error } = await supabase
        .from('offers')
        .insert([{
          user_id: offerData.user_id,
          job_post_id: offerData.job_post_id,
          salary: offerData.salary,
          benefits: offerData.benefits,
          offer_letter_url: storedLetter.path,
          status: 'pending',
          flag_active: true,
          flag_deleted: false
        }])
        .select(`
          *,
          candidate:users!user_id(full_name, email),
          job:job_posts!job_post_id(title)
        `)
        .single();

      if (error) throw error;

      // Send notification email
      await emailUtils.sendEmail({
        to: offer.candidate.email,
        subject: "New Job Offer",
        template: "offer-letter",
        data: {
          candidateName: offer.candidate.full_name,
          jobTitle: offer.job.title,
          offerUrl: storedLetter.path
        }
      });

      return { success: true, offer };
    } catch (error) {
      handleServiceError(error, "Failed to generate offer");
    }
  },

  async updateOfferStatus(id, status) {
    try {
      const { data: offer, error } = await supabase
        .from('offers')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('flag_deleted', false)
        .select(`
          *,
          candidate:users!user_id(full_name, email),
          job:job_posts!job_post_id(title)
        `)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: "Offer not found", status: 404 };
        }
        throw error;
      }

      // Send notification based on status
      if (status === "accepted") {
        await emailUtils.sendEmail({
          to: offer.candidate.email,
          subject: "Offer Accepted",
          template: "offer-accepted",
          data: {
            candidateName: offer.candidate.full_name,
            jobTitle: offer.job.title
          }
        });
      }

      return { success: true, offer };
    } catch (error) {
      handleServiceError(error, "Failed to update offer status");
    }
  }
};