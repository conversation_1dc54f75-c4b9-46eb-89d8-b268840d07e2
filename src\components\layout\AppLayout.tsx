import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { useAuth } from "@/components/auth/AuthProvider";
import { Toaster } from "@/components/ui/sonner";
import { useLocation } from "react-router-dom";

interface AppLayoutProps {
  children: React.ReactNode;
}

export default function AppLayout({ children }: AppLayoutProps) {
  const { user } = useAuth();
  const location = useLocation();
  const isLoginPage = location.pathname === '/login';

  // Don't show sidebar on login page or when user is not authenticated
  if (isLoginPage || !user) {
    return (
      <>
        <Toaster />
        <main className="min-h-screen">{children}</main>
      </>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <main className="flex-1 overflow-y-auto">
          <div className="lg:hidden">
            <SidebarTrigger />
          </div>
          <div className="p-4 md:p-6">{children}</div>
        </main>
      </div>
      <Toaster />
    </SidebarProvider>
  );
}
