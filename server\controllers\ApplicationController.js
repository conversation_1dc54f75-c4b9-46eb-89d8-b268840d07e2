import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { applicationService } from "../services/applicationService.js";

export const applicationController = {
  async getApplications(req, res) {
    try {
      const result = await applicationService.getApplications(req.query);
      logger.info("Applications retrieved successfully");
      return responseHandler.success(res, result);
    } catch (err) {
      logger.error("Failed to get applications:", err);
      return responseHandler.error(res, "Failed to retrieve applications");
    }
  },

  async getApplicationById(req, res) {
    try {
      const result = await applicationService.getApplicationById(req.params.id);
      
      if (!result.success) {
        logger.info(`Application not found: ${req.params.id}`);
        return responseHandler.error(res, result.message, 404);
      }

      logger.info(`Application retrieved: ${req.params.id}`);
      return responseHandler.success(res, { application: result.application });
    } catch (err) {
      logger.error("Failed to get application:", err);
      return responseHandler.error(res, "Failed to retrieve application");
    }
  },

  async createApplication(req, res) {
    try {
      const result = await applicationService.createApplication({
        ...req.body,
        user_id: req.user._id,
        created_by: req.user._id
      });
      
      if (!result.success) {
        logger.info(`Application creation failed: ${result.message}`);
        return responseHandler.badRequest(res, result.message);
      }

      logger.info(`New application created for job: ${req.body.job_post_id}`);
      return responseHandler.success(res, { application: result.application }, 201);
    } catch (err) {
      logger.error("Failed to create application:", err);
      return responseHandler.error(res, "Failed to create application");
    }
  },

  async updateApplicationStatus(req, res) {
    try {
      const result = await applicationService.updateApplicationStatus(
        req.params.id,
        req.body.status,
        req.user._id
      );
      
      if (!result.success) {
        logger.info(`Application status update failed: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Application status updated: ${req.params.id}`);
      return responseHandler.success(res, { application: result.application });
    } catch (err) {
      logger.error("Failed to update application status:", err);
      return responseHandler.error(res, "Failed to update application status");
    }
  }
};