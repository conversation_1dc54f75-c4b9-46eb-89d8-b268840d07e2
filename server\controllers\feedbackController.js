import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { feedbackService } from "../services/feedbackService.js";

export const feedbackController = {
  async getFeedback(req, res) {
    try {
      const result = await feedbackService.getFeedback(req.params.interviewId);

      if (!result.success) {
        logger.info(`Failed to get feedback: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Feedback retrieved for interview: ${req.params.interviewId}`);
      return responseHandler.success(res, { feedback: result.feedback });
    } catch (err) {
      logger.error("Failed to get feedback:", err);
      return responseHandler.error(res, "Failed to retrieve feedback");
    }
  },

  async submitFeedback(req, res) {
    try {
      const result = await feedbackService.submitFeedback(req.params.interviewId, req.body);

      if (!result.success) {
        logger.info(`Failed to submit feedback: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Feedback submitted for interview: ${req.params.interviewId}`);
      return responseHandler.success(res, { feedback: result.feedback }, 201);
    } catch (err) {
      logger.error("Failed to submit feedback:", err);
      return responseHandler.error(res, "Failed to submit feedback");
    }
  },
};
