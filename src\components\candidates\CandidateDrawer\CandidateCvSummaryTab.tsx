import React from "react";
import { Card, CardContent } from "@/components/ui/card";

interface CandidateCvSummaryTabProps {
  candidateDetail: any;
}

const CandidateCvSummaryTab: React.FC<CandidateCvSummaryTabProps> = ({ candidateDetail }) => {
  const suitableSummary = candidateDetail.suitability_summary;
  const unsuitableSummary = candidateDetail.unsuitability_summary;
  return (
    <div>
      <Card className="border border-gray-100 shadow-sm mb-4">
        <CardContent className="p-4">
          <h3 className="text-base font-medium mb-3">Suitability Summary</h3>
          <div className="text-sm text-gray-600">
            {suitableSummary ? (
              <p>{suitableSummary}</p>
            ) : (
              <p className="italic text-gray-500">No suitability summary available.</p>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-100 shadow-sm">
        <CardContent className="p-4">
          <h3 className="text-base font-medium mb-3">Unsuitability Summary</h3>
          <div className="text-sm text-gray-600">
            {unsuitableSummary ? (
              <p>{unsuitableSummary}</p>
            ) : (
              <p className="italic text-gray-500">No unsuitability summary available.</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CandidateCvSummaryTab;
