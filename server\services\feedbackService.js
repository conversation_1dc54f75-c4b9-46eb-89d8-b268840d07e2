import { supabase } from "../config/db.js";
import { logger } from "../utils/logger.js";
import { emailUtils } from "../utils/emailUtils.js";

export const feedbackService = {
  async getFeedback(interviewId) {
    try {
      // Step 1: Fetch the interview to get feedback_id
      const { data: interview, error: interviewError } = await supabase
        .from("interviews")
        .select("feedback_id, scheduled_date")
        .eq("id", interviewId)
        .single();

        if (interviewError) throw interviewError;

      if (!interview?.feedback_id) {
        return { success: false, message: "No feedback submitted yet", status: 404 };
      }

      // Step 2: Fetch the feedback using feedback_id
      const { data: feedback, error: feedbackError } = await supabase
        .from("feedbacks")
        .select(
          `
        id,
        reviewer_id,
        overall_recommendation,
        overall_comments,
        overall_score,
        stage,
        users:reviewer_id(full_name),
        feedback_criteria(
          criteria_id,
          title,
          score,
          notes
        )
      `
        )
        .eq("id", interview.feedback_id)
        .eq("flag_deleted", false)
        .single();

      if (feedbackError) throw feedbackError;

      if (!feedback) {
        return { success: false, message: "Feedback not found", status: 404 };
      }

      // Format response
      const formatted = {
        overallRecommendation: feedback.overall_recommendation,
        criteria: feedback.feedback_criteria.map(c => ({
          id: c.criteria_id,
          title: c.title,
          score: c.score,
          notes: c.notes,
        })),
        overallComments: feedback.overall_comments,
        interviewer: feedback.users?.full_name || "N/A",
        interviewDate: interview.scheduled_date, 
        stage: feedback.stage,
        overall_score: feedback.overall_score?.toString() ?? null,
      };

      return { success: true, feedback: formatted };
    } catch (error) {
      logger.error("Failed to get feedback:", error);
      throw error;
    }
  },
  async submitFeedback(interviewId, feedbackData) {
    try {
      // 1. Check if interview exists
      const { data: interview, error: interviewError } = await supabase
        .from("interviews")
        .select("*")
        .eq("id", interviewId)
        .single();

      if (interviewError) {
        if (interviewError.code === "PGRST116") {
          return { success: false, message: "Interview not found", status: 404 };
        }
        throw interviewError;
      }

      // 2. Destructure feedbackData
      const {
        reviewer_id,
        interview_id,
        candidate_id,
        stage,
        overall_recommendation,
        overall_comments,
        overall_score,
        criteria,
      } = feedbackData;

      // 3. Insert into feedbacks (exclude criteria from this insert)
      const { data: feedback, error: feedbackError } = await supabase
        .from("feedbacks")
        .insert([
          {
            reviewer_id,
            interview_id,
            candidate_id,
            stage,
            overall_recommendation,
            overall_comments,
            overall_score,
            flag_active: true,
            flag_deleted: false,
          },
        ])
        .select(`*`)
        .single();

      if (feedbackError) throw feedbackError;

      // 4. Insert feedback criteria separately
      const criteriaPayload = criteria.map(item => ({
        feedback_id: feedback.id,
        criteria_id: item.id,
        title: item.title,
        score: item.score,
        notes: item.notes,
      }));

      const { error: criteriaError } = await supabase
        .from("feedback_criteria")
        .insert(criteriaPayload);

      if (criteriaError) throw criteriaError;

      // 5. Update interview status
      const { error: updateError } = await supabase
        .from("interviews")
        .update({
          feedback_id: feedback.id,
          updated_at: new Date().toISOString(),
        })
        .eq("id", interviewId);

      if (updateError) throw updateError;
      return { success: true, feedback };
    } catch (error) {
      logger.error("Failed to submit feedback:", error);
      throw error;
    }
  },
};
