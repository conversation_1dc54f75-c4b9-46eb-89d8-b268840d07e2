import React, { createContext, useContext, useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from '@/components/ui/sonner';
import { API_CONFIG } from '@/utils/Constant';

const API_URL = API_CONFIG.BASE_URL;
const SESSION_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds


export type UserRole = 'recruiter' | 'manager' | 'interviewer' | 'hr';

interface User {
  id: string;
  email: string;
  full_name: string;
  role_id: string;
  language_preference: string;
  flag_active: boolean;
  flag_deleted: boolean;
  role: {
    name: UserRole;
  };
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string, role: string) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();
  const location = useLocation();

  const handleSessionExpired = async () => {
    // Clear user data and token
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('tokenCreationTime');
    setUser(null);

    // Show expiration message
    toast.error('Your session has expired. Please log in again.');

    // Redirect to login
    navigate('/login');
  };

  const startSession = () => {
    localStorage.setItem('tokenCreationTime', Date.now().toString());
  };

  useEffect(() => {
    // Check if user is already logged in from localStorage
    const checkAuth = () => {
      try {
        const token = localStorage.getItem('token');
        const storedUser = localStorage.getItem('user');
        const tokenCreationTime = localStorage.getItem('tokenCreationTime');
        
        if (token && storedUser && tokenCreationTime) {
          const currentTime = Date.now();
          if (currentTime - parseInt(tokenCreationTime) > SESSION_DURATION) {
            // Session expired
            handleSessionExpired();
          } else {
            setUser(JSON.parse(storedUser));
          }
        }
      } catch (error) {
        console.error("Error checking authentication:", error);
        handleSessionExpired();
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, []);

  // Set up session expiration check
  useEffect(() => {
    if (!user) return;

    const tokenCreationTime = localStorage.getItem('tokenCreationTime');
    if (tokenCreationTime) {
      const currentTime = Date.now();
      const timeUntilExpiration = SESSION_DURATION - (currentTime - parseInt(tokenCreationTime));
      const sessionTimeout = setTimeout(handleSessionExpired, timeUntilExpiration);

      return () => clearTimeout(sessionTimeout);
    }
  }, [user]);

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Login failed');
      }

      if (result.data?.token) {
        localStorage.setItem('token', result.data.token);
        localStorage.setItem('user', JSON.stringify(result.data.user));
        setUser(result.data.user);
        startSession(); // Start new session
        toast.success('Login successful!');
        
        // Redirect based on role
        navigate(`/dashboard/${result.data.user.role.name}`);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      toast.error('Login failed: ' + (error as Error).message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, name: string, role: string) => {
    try {
      const response = await fetch(`${API_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          full_name: name,
          role: role.toLowerCase()
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Registration failed');
      }

      // After successful registration, automatically sign in
      return signIn(email, password);
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    handleSessionExpired();
    toast.info('You have been logged out');
  };

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
