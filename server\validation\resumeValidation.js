import { z } from 'zod';

export const resumeValidation = {
  uploadSchema: z.object({
    body: z.object({
      user_id: z.string().uuid('Invalid user ID'),
      file: z.any()
        .refine(file => file?.size <= 5000000, 'File size must be less than 5MB')
        .refine(
          file => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file?.mimetype),
          'Only PDF and Word documents are allowed'
        )
    })
  }),

  parseSchema: z.object({
    body: z.object({
      resume_id: z.string().uuid('Invalid resume ID'),
      file_url: z.string().url('Invalid file URL'),
      parse_options: z.object({
        extract_contact: z.boolean().default(true),
        extract_education: z.boolean().default(true),
        extract_experience: z.boolean().default(true),
        extract_skills: z.boolean().default(true)
      }).optional()
    })
  })
};