
// Jobs data model (mimics a database table)

export interface Job {
  id: number;
  title: string;
  location: string;
  status: "active" | "draft" | "closed" | "published" | "on hold" | "private";
  applicants?: number;
  datePosted?: string;
  department?: string;
  salary?: string;
  employmentType?: "full-time" | "part-time" | "contract";
  workType?: "remote" | "on-site" | "hybrid";
  description?: string;
  responsibilities?: string[];
  requirements?: string[];
  skills?: string[];
}

// Mock jobs data
export const jobs: Job[] = [
  {
    id: 1,
    title: "Senior Software Engineer",
    location: "New York",
    status: "active",
    applicants: 12,
    datePosted: "2023-05-01",
    department: "Engineering",
    salary: "$120,000 - $150,000",
    employmentType: "full-time",
    workType: "hybrid",
    description: "We are looking for a Senior Software Engineer to join our growing team. The ideal candidate will have strong experience in full-stack development and will be responsible for building high-performance web applications.",
    responsibilities: [
      "Design and implement new features and functionality",
      "Write clean, maintainable, and efficient code",
      "Collaborate with cross-functional teams",
      "Mentor junior developers",
      "Participate in code reviews and technical discussions"
    ],
    requirements: [
      "5+ years of experience in software development",
      "Strong proficiency in JavaScript/TypeScript and React",
      "Experience with backend technologies such as Node.js",
      "Experience with databases and API design",
      "Bachelor's degree in Computer Science or related field"
    ],
    skills: ["React", "TypeScript", "Node.js", "API Design", "SQL"]
  },
  {
    id: 2,
    title: "Data Scientist",
    location: "San Francisco",
    status: "active",
    applicants: 8,
    datePosted: "2023-04-28",
    department: "Data",
    salary: "$130,000 - $160,000",
    employmentType: "full-time",
    workType: "remote",
    description: "We are seeking a Data Scientist to help us derive insights from our data. The ideal candidate will have strong analytical skills and experience with machine learning.",
    responsibilities: [
      "Develop machine learning models",
      "Extract insights from large datasets",
      "Create data visualizations",
      "Collaborate with product and engineering teams",
      "Present findings to stakeholders"
    ],
    requirements: [
      "3+ years of experience in data science",
      "Strong proficiency in Python and data analysis libraries",
      "Experience with machine learning frameworks",
      "Knowledge of SQL and database concepts",
      "Master's degree in Statistics, Computer Science, or related field"
    ],
    skills: ["Python", "Machine Learning", "Data Analysis", "SQL", "Statistics"]
  },
  {
    id: 3,
    title: "UX Designer",
    location: "Remote",
    status: "draft",
    applicants: 0,
    datePosted: "2023-05-05",
    department: "Design",
    salary: "$90,000 - $120,000",
    employmentType: "full-time",
    workType: "remote",
    description: "We are looking for a UX Designer to create intuitive and engaging user experiences. The ideal candidate will have a strong portfolio demonstrating their design process and problem-solving abilities.",
    responsibilities: [
      "Create wireframes and prototypes",
      "Conduct user research",
      "Collaborate with product managers and developers",
      "Design intuitive user interfaces",
      "Test and iterate on designs"
    ],
    requirements: [
      "3+ years of experience in UX design",
      "Proficiency in design tools like Figma or Sketch",
      "Experience with user research methodologies",
      "Strong portfolio showcasing your work",
      "Bachelor's degree in Design or related field"
    ],
    skills: ["Figma", "User Research", "Wireframing", "Prototyping", "UI Design"]
  },
  {
    id: 4,
    title: "Product Manager",
    location: "Boston",
    status: "closed",
    applicants: 25,
    datePosted: "2023-03-15",
    department: "Product",
    salary: "$110,000 - $140,000",
    employmentType: "full-time",
    workType: "on-site",
    description: "We are seeking a Product Manager to drive the development of our software products. The ideal candidate will have experience in product development and strong communication skills.",
    responsibilities: [
      "Define product vision and strategy",
      "Create product roadmaps",
      "Work with engineering teams on implementation",
      "Gather and prioritize requirements",
      "Analyze market trends and competition"
    ],
    requirements: [
      "4+ years of experience in product management",
      "Experience with agile development methodologies",
      "Strong communication and leadership skills",
      "Technical background or understanding of software development",
      "Bachelor's degree in Business, Computer Science, or related field"
    ],
    skills: ["Product Strategy", "Agile", "User Stories", "Roadmapping", "Market Analysis"]
  },
  {
    id: 5,
    title: "Backend Developer",
    location: "Chicago",
    status: "active",
    applicants: 15,
    datePosted: "2023-04-20",
    department: "Engineering",
    salary: "$100,000 - $130,000",
    employmentType: "full-time",
    workType: "hybrid",
    description: "We are looking for a Backend Developer to build and maintain our server-side applications. The ideal candidate will have experience with API development and database design.",
    responsibilities: [
      "Design and implement APIs",
      "Optimize database queries",
      "Implement security and data protection measures",
      "Integrate with third-party services",
      "Write automated tests"
    ],
    requirements: [
      "3+ years of experience in backend development",
      "Proficiency in languages such as Python, Java, or Node.js",
      "Experience with database design and optimization",
      "Knowledge of RESTful API design principles",
      "Bachelor's degree in Computer Science or related field"
    ],
    skills: ["Python", "Java", "API Design", "SQL", "Database Design"]
  },
  {
    id: 6,
    title: "Data Analyst",
    location: "Seattle",
    status: "active",
    applicants: 10,
    datePosted: "2023-04-10",
    department: "Data",
    salary: "$85,000 - $110,000",
    employmentType: "full-time",
    workType: "on-site",
    description: "We are seeking a Data Analyst to help us make data-driven decisions. The ideal candidate will have strong analytical skills and experience with data visualization tools.",
    responsibilities: [
      "Analyze data and identify trends",
      "Create reports and dashboards",
      "Collaborate with cross-functional teams",
      "Present findings to stakeholders",
      "Support data-driven decision making"
    ],
    requirements: [
      "2+ years of experience in data analysis",
      "Proficiency in SQL and Excel",
      "Experience with data visualization tools like Tableau",
      "Strong analytical and problem-solving skills",
      "Bachelor's degree in Statistics, Mathematics, or related field"
    ],
    skills: ["SQL", "Excel", "Tableau", "Data Analysis", "Reporting"]
  }
];

// Helper function to get a job by ID
export const getJobById = (id: number): Job | undefined => {
  return jobs.find(job => job.id === id);
};

// Helper function to get jobs by status
export const getJobsByStatus = (status: Job['status']): Job[] => {
  return jobs.filter(job => job.status === status);
};

// Helper function to get active jobs
export const getActiveJobs = (): Job[] => {
  return jobs.filter(job => job.status === 'active' || job.status === 'published');
};
