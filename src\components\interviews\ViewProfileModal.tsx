import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface CandidateProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  job_id: string;
  source: string;
  status: string;
  can_status: string;
  created_at: string;
  created_by: string;
  resume_url: string;
  updated_at: string;
  flag_active: boolean;
  flag_deleted: boolean;
}

interface ViewProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidate?: CandidateProfile;
}

const ViewProfileModal = ({ isOpen, onClose, candidate }: ViewProfileModalProps) => {
  if (!candidate) return null;
  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white p-0">
        <DialogHeader className="p-6 bg-[#9b87f5]/10 border-b">
          <div className="flex flex-col md:flex-row justify-between items-start gap-4">
            <div className="flex items-center gap-4">
              <Avatar className="h-14 w-14">
                <AvatarFallback>
                  {candidate.name
                    .split(" ")
                    .map(n => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <DialogTitle className="text-2xl font-semibold text-[#1A1F2C]">
                  {candidate.name}
                </DialogTitle>
                <p className="text-[#7E69AB] mt-1">
                  {candidate.job_id ? `Job ID: ${candidate.job_id}` : ""}
                </p>
              </div>
            </div>
            <Badge className="bg-[#E5DEFF] text-[#6E59A5] px-3 py-1 text-sm font-medium">
              {candidate.status.charAt(0).toUpperCase() + candidate.status.slice(1)}
            </Badge>
          </div>
        </DialogHeader>

        <div className="p-6 space-y-6">
          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-semibold text-[#1A1F2C] mb-4">Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-[#F1F0FB] p-4 rounded-md">
                <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Email</h4>
                <p className="text-[#1A1F2C] font-medium">{candidate.email}</p>
              </div>
              <div className="bg-[#F1F0FB] p-4 rounded-md">
                <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Phone</h4>
                <p className="text-[#1A1F2C] font-medium">{candidate.phone}</p>
              </div>
              <div className="bg-[#F1F0FB] p-4 rounded-md">
                <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Source</h4>
                <p className="text-[#1A1F2C] font-medium">{candidate.source || "Not specified"}</p>
              </div>
              <div className="bg-[#F1F0FB] p-4 rounded-md">
                <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Status</h4>
                <p className="text-[#1A1F2C] font-medium">{candidate.can_status}</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Application Details */}
          <div>
            <h3 className="text-lg font-semibold text-[#1A1F2C] mb-4">Application Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-[#F1F0FB] p-4 rounded-md">
                <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Applied Date</h4>
                <p className="text-[#1A1F2C] font-medium">
                  {new Date(candidate.created_at).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>
              <div className="bg-[#F1F0FB] p-4 rounded-md">
                <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Last Updated</h4>
                <p className="text-[#1A1F2C] font-medium">
                  {new Date(candidate.updated_at).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Resume Link */}
          {candidate.resume_url && (
            <div className="flex justify-end mt-6">
              <a
                href={candidate.resume_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-[#9b87f5] text-white hover:bg-[#7E69AB] h-10 px-4 py-2"
              >
                View Resume
              </a>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewProfileModal;
