import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { FileText, Mail, Phone, MapPin, Info } from "lucide-react";
import React from "react";
import { CandidateStatus } from "@/hooks/useCandidates";

interface CandidateBasicInfoProps {
  candidateDetail: any;
  actionLoading: boolean;
  onResumeDownload: () => void;
  onAccept: () => Promise<void>;
  onDecline: () => Promise<void>;
  setActiveTab: (tab: string) => void;
}

const getInitials = (name: string) => {
  return name
    .split(" ")
    .map((n: string) => n[0])
    .join("")
    .toUpperCase();
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case CandidateStatus.Applied:
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
          Applied
        </Badge>
      );
    case CandidateStatus.Screening:
      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-50">
          Screening
        </Badge>
      );
    case CandidateStatus.Interview:
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50">
          Interview
        </Badge>
      );
    case CandidateStatus.Offer:
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
          Offer
        </Badge>
      );
    case CandidateStatus.Hired:
      return (
        <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
          Hired
        </Badge>
      );
    case CandidateStatus.Rejected:
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50">
          Rejected
        </Badge>
      );
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

const CandidateBasicInfo: React.FC<CandidateBasicInfoProps> = ({
  candidateDetail,
  actionLoading,
  onResumeDownload,
  onAccept,
  onDecline,
  setActiveTab
}) => {
  return (
    <div className="w-full md:w-1/3 pr-0 md:pr-6 border-0 md:border-r border-gray-200">
      <div className="flex flex-col items-center text-center mb-4">
        <Avatar className="h-20 w-20 mb-2 border-2 border-primary/10">
          <AvatarFallback className="text-xl bg-primary/5 text-primary">
            {getInitials(candidateDetail.name)}
          </AvatarFallback>
        </Avatar>
        <div className="text-2xl mb-1 font-bold">{candidateDetail.name}</div>
        <p className="text-gray-600 mb-1">{candidateDetail.jobTitle}</p>
        <div className="mb-3">{getStatusBadge(candidateDetail.status)}</div>
        <div className="text-sm text-gray-600 flex flex-col gap-2 w-full">
          <div className="flex items-center justify-center gap-2">
            <Mail className="h-4 w-4 text-gray-500" />
            <span>{candidateDetail.email}</span>
          </div>
          <div className="flex items-center justify-center gap-2">
            <Phone className="h-4 w-4 text-gray-500" />
            <span>{candidateDetail.phone}</span>
          </div>
          <div className="flex items-center justify-center gap-2">
            <MapPin className="h-4 w-4 text-gray-500" />
            <span>{candidateDetail.location}</span>
          </div>
        </div>
      </div>
      <div className="mt-6">
        <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
          Applicant Info
        </h3>
        <div className="space-y-3 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Applied On</span>
            <span className="font-medium">{formatDate(candidateDetail.appliedDate)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Source</span>
            <span className="font-medium">{candidateDetail.source}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Last Updated</span>
            <span className="font-medium">{formatDate(candidateDetail.lastUpdated)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Match Score</span>
            <span className="font-medium text-green-600">
              {candidateDetail.matchScore}%
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setActiveTab("score-summary")}
                className="ml-1 h-4 w-4 text-gray-800"
              >
                <Info className="h-4 w-4" />
              </Button>
            </span>
          </div>
        </div>
      </div>
      <div className="mt-6 space-y-2">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={onResumeDownload}
        >
          <FileText className="w-4 h-4 mr-2" />
          Download Resume
        </Button>
        {candidateDetail.status === CandidateStatus.Applied && (
          <div className="flex justify-center gap-2 mt-4 p-3 rounded-md bg-slate-50">
            <Button
              className="bg-blue-500 text-white hover:bg-blue-600"
              size="sm"
              disabled={actionLoading}
              onClick={onAccept}
            >
              {actionLoading ? "Approving..." : "Approve"}
            </Button>
            <Button
              className="bg-red-500 text-white hover:bg-red-600"
              size="sm"
              disabled={actionLoading}
              onClick={onDecline}
            >
              {actionLoading ? "Declining..." : "Decline"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CandidateBasicInfo;