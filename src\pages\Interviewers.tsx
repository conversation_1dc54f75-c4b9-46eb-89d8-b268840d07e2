import { useState } from "react";
import { Interviewer<PERSON><PERSON> } from "@/components/interviewers/InterviewerCard";
import { Interviewer<PERSON>rawer } from "@/components/interviewers/InterviewerDrawer";
import { AddInterviewerDrawer } from "@/components/interviewers/AddInterviewerDrawer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Filter, UserPlus, Trash2, AlertTriangle } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import {
  useInterviewers,
  InterviewerDetail,
  BackendInterviewerDetail,
} from "@/hooks/useInterviewers"; // Import the hook and types
import { Loader2 } from "lucide-react"; // For loading indicator
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialog<PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog";

export default function Interviewers() {
  const {
    interviewers: fetchedInterviewers,
    loading,
    error,
    addInterviewer,
    deleteInterviewer,
  } = useInterviewers();

  const [selectedInterviewer, setSelectedInterviewer] = useState<InterviewerDetail | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isAddDrawerOpen, setIsAddDrawerOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [interviewerToDelete, setInterviewerToDelete] = useState<InterviewerDetail | null>(null);

  const handleViewDetails = (interviewer: InterviewerDetail) => {
    setSelectedInterviewer(interviewer);
    setIsDrawerOpen(true);
  };

  const handleAddInterviewer = async (newInterviewerData: Partial<BackendInterviewerDetail>) => {
    const result = await addInterviewer(newInterviewerData);
    if (result?.success) {
      setIsAddDrawerOpen(false);
    }
  };

  const handleDeleteClick = (interviewer: InterviewerDetail) => {
    setInterviewerToDelete(interviewer);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!interviewerToDelete) return;

    const result = await deleteInterviewer(interviewerToDelete.id);
    if (result?.success) {
      setDeleteDialogOpen(false);
      setInterviewerToDelete(null);
      // The hook's fetchInterviewers will update the list
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setInterviewerToDelete(null);
  };

  const handleInterviewerUpdate = (updatedInterviewer: InterviewerDetail) => {
    setSelectedInterviewer(updatedInterviewer);
    // Also update the interviewers list to reflect the change immediately
    // This ensures that the card view also updates without a full re-fetch of all interviewers
    // from the backend, which fetchInterviewers() would eventually do.
    const updatedList = fetchedInterviewers.map(interviewer =>
      interviewer.id === updatedInterviewer.id ? updatedInterviewer : interviewer
    );
    // Note: fetchedInterviewers is from useInterviewers hook, it will eventually re-fetch and update.
    // For immediate UI update, we can optimistically update the list here if needed,
    // but for now, rely on setSelectedInterviewer to update the drawer content.
    // The fetchInterviewers() call in useInterviewers.ts on successful update will refresh the main list.
  };

  const filteredInterviewers = fetchedInterviewers.filter(interviewer => {
    const matchesSearch =
      interviewer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interviewer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interviewer.department.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDepartment =
      departmentFilter === "all" || interviewer.department === departmentFilter;

    return matchesSearch && matchesDepartment;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2 text-muted-foreground">Loading interviewers...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-96 text-destructive">
        <p>Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Interviewers</h1>
          <p className="text-muted-foreground">Manage and organize your interview panel</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsAddDrawerOpen(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Interviewer
          </Button>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search interviewers..."
            className="pl-8"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
          <SelectTrigger className="w-full md:w-[180px]">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <SelectValue placeholder="Department" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            <SelectItem value="Engineering">Engineering</SelectItem>
            <SelectItem value="Product">Product</SelectItem>
            <SelectItem value="Design">Design</SelectItem>
            <SelectItem value="Marketing">Marketing</SelectItem>
            <SelectItem value="HR">HR</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Grid of InterviewerCards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {filteredInterviewers.map(interviewer => (
          <div key={interviewer.id} className="relative group">
            <InterviewerCard interviewer={interviewer} onViewDetails={handleViewDetails} />
            <Button
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={e => {
                e.stopPropagation();
                handleDeleteClick(interviewer);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>

      {/* Drawer for viewing interviewer details */}
      <InterviewerDrawer
        isOpen={isDrawerOpen}
        onOpenChange={setIsDrawerOpen}
        interviewer={selectedInterviewer}
        onUpdate={handleInterviewerUpdate}
        onDelete={handleDeleteClick}
      />

      {/* Drawer for adding new interviewer */}
      <AddInterviewerDrawer
        isOpen={isAddDrawerOpen}
        onOpenChange={setIsAddDrawerOpen}
        onSave={handleAddInterviewer}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Interviewer
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete <strong>{interviewerToDelete?.name}</strong>? This
              action cannot be undone and will permanently remove the interviewer from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Interviewer
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
