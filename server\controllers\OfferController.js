import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { offerService } from "../services/offerService.js";

export const offerController = {
  async getOffers(req, res) {
    try {
      const { page = 1, limit = 10, ...filters } = req.query;
      const result = await offerService.getOffers(filters, page, limit);
      logger.info("Offers retrieved successfully");
      return responseHandler.success(res, result);
    } catch (err) {
      logger.error("Failed to get offers:", err);
      return responseHandler.error(res, "Failed to retrieve offers");
    }
  },

  async generateOffer(req, res) {
    try {
      const result = await offerService.generateOffer({
        ...req.body,
        created_by: req.user._id
      });
      
      if (!result.success) {
        logger.info(`Offer generation failed: ${result.message}`);
        return responseHandler.badRequest(res, result.message);
      }

      logger.info(`Offer generated successfully for job: ${req.body.job_post_id}`);
      return responseHandler.success(res, { offer: result.offer }, 201);
    } catch (err) {
      logger.error("Failed to generate offer:", err);
      return responseHandler.error(res, "Failed to generate offer");
    }
  },

  async updateOfferStatus(req, res) {
    try {
      const result = await offerService.updateOfferStatus(
        req.params.id,
        req.body.status
      );
      
      if (!result.success) {
        logger.info(`Offer status update failed: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Offer status updated: ${req.params.id}`);
      return responseHandler.success(res, { offer: result.offer });
    } catch (err) {
      logger.error("Failed to update offer status:", err);
      return responseHandler.error(res, "Failed to update offer status");
    }
  }
};