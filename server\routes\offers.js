import express from 'express';
import { asyncHandler } from '../middleware/asyncHandler.js';
import { offerController } from '../controllers/OfferController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/offers
 * @desc    Get all offers with pagination
 * @access  Private
 */
router.get('/',
  authenticate,
  asyncHandler(offerController.getOffers)
);

/**
 * @route   POST /api/offers/generate
 * @desc    Generate a new offer
 * @access  Private/Recruiter
 */
router.post('/generate',
  authenticate,
  authorize(['admin', 'recruiter']),
  async<PERSON><PERSON><PERSON>(offerController.generateOffer)
);

/**
 * @route   PUT /api/offers/:id/status
 * @desc    Update offer status
 * @access  Private/Recruiter
 */
router.put('/:id/status',
  authenticate,
  authorize(['admin', 'recruiter']),
  async<PERSON><PERSON><PERSON>(offerController.updateOfferStatus)
);

export default router;