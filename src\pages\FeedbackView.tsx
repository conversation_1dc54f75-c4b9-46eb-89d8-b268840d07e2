import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "@/components/ui/sonner";
import FeedbackPage from "@/components/feedback/FeedbackPage";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useInterviews } from "@/hooks/useInterviews";

const FeedbackView = () => {
  const { interviewId } = useParams();
  const navigate = useNavigate();
  const { getInterviewById } = useInterviews();
  const [interview, setInterview] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchInterview = async () => {
      if (!interviewId) return;
      setLoading(true);
      const fetchedInterview = await getInterviewById(interviewId);
      setInterview(fetchedInterview);
      setLoading(false);
    };

    fetchInterview();
  }, [interviewId, getInterviewById]);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={() => navigate(-1)} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Loading...</h2>
        </div>
      </div>
    );
  }

  if (!interview) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={() => navigate(-1)} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Interview Not Found</h2>
          <p className="text-muted-foreground">The interview you are looking for does not exist.</p>
          <Button onClick={() => navigate("/interviews")} className="mt-4">
            Go to Interviews
          </Button>
        </div>
      </div>
    );
  }

  const handleClose = () => {
    navigate("/interviews");
  };

  const transformInterview = (interview: any) => {
    if (!interview) return null;

    const getFormattedTimeRange = (time: string, duration: number) => {
      const [hour, minute] = time.split(":").map(Number);
      const start = new Date();
      start.setHours(hour, minute, 0);

      const end = new Date(start.getTime() + duration * 60000);

      const formatTime = (date: Date) =>
        date.toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        });

      return `${formatTime(start)} - ${formatTime(end)}`;
    };

    return {
      id: interview.id,
      candidateId: interview.candidate_id ?? null,
      candidateName: interview.candidate?.name ?? "N/A",
      position: interview.candidate?.job?.title ?? "N/A",
      interviewer: interview.interviewer?.full_name ?? "N/A",
      interviewerId: interview.interviewer?.id ?? null,
      date: interview.scheduled_date,
      time: getFormattedTimeRange(interview.scheduled_time, interview.duration),
      type: interview.stage ?? "N/A",
      status: interview.status ?? "N/A",
    };
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={() => navigate(-1)} className="mr-2">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Interviews
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6">
          <FeedbackPage interview={transformInterview(interview)} onClose={handleClose} isFullPage={true} />
        </div>
      </div>
    </div>
  );
};

export default FeedbackView;
