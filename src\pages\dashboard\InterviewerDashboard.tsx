import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { StatsCard } from '@/components/dashboard/StatsCard';
import { ActivityItem } from '@/components/dashboard/ActivityItem';
import { Calendar, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { useAuth } from '@/components/auth/AuthProvider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

const InterviewerDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Interviewer Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.full_name || 'Interviewer'}!
          </p>
        </div>
        <div className="flex gap-2">
          <Button size="sm">
            <Calendar size={16} className="mr-2" />
            View Full Schedule
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard 
          title="Upcoming Interviews" 
          value={loading ? "..." : "8"}
          description="3 this week"
          icon={<Calendar size={16} />}
        />
        <StatsCard 
          title="Pending Feedback" 
          value={loading ? "..." : "2"}
          trend="up"
          trendValue="Due today"
          icon={<AlertCircle size={16} />}
        />
        <StatsCard 
          title="Completed Interviews" 
          value={loading ? "..." : "24"}
          description="This month"
          icon={<CheckCircle size={16} />}
        />
        <StatsCard 
          title="Average Duration" 
          value={loading ? "..." : "45 min"}
          icon={<Clock size={16} />}
        />
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Today's Interviews</CardTitle>
            <CardDescription>Your scheduled interviews for today</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {loading ? (
              <div className="p-6 space-y-2">
                <div className="h-16 bg-muted animate-pulse rounded-md" />
                <div className="h-16 bg-muted animate-pulse rounded-md" />
              </div>
            ) : (
              <div>
                <div className="p-4 border-b flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <h3 className="font-medium">Sarah Johnson</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">Senior Frontend Engineer</Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">Technical</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">10:30 AM - 11:30 AM (in 2 hours)</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">View Resume</Button>
                    <Button size="sm">Join Meeting</Button>
                  </div>
                </div>
                
                <div className="p-4 border-b flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <h3 className="font-medium">Miguel Sanchez</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">Backend Developer</Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">System Design</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">2:00 PM - 3:00 PM (in 5 hours)</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">View Resume</Button>
                    <Button size="sm">Join Meeting</Button>
                  </div>
                </div>
                
                <div className="p-4 flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <h3 className="font-medium">Lisa Chen</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">DevOps Engineer</Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">Technical</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">4:30 PM - 5:30 PM (in 8 hours)</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">View Resume</Button>
                    <Button size="sm">Join Meeting</Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        
        <div className="grid gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Pending Feedback</CardTitle>
              <CardDescription>Submit feedback for these candidates</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-6 space-y-2">
                  <div className="h-16 bg-muted animate-pulse rounded-md" />
                  <div className="h-16 bg-muted animate-pulse rounded-md" />
                </div>
              ) : (
                <div>
                  <div className="p-4 border-b">
                    <h3 className="font-medium">David Park</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">Frontend Developer</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">Interviewed yesterday</p>
                    <div className="mt-3">
                      <Button size="sm" className="w-full">Submit Feedback</Button>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium">Jessica Miller</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">Product Manager</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">Interviewed 2 days ago</p>
                    <div className="mt-3">
                      <Button size="sm" className="w-full">Submit Feedback</Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Your latest interview activities</CardDescription>
            </CardHeader>
            <CardContent className="px-2">
              {loading ? (
                <div className="space-y-2">
                  <div className="h-12 bg-muted animate-pulse rounded-md" />
                  <div className="h-12 bg-muted animate-pulse rounded-md" />
                  <div className="h-12 bg-muted animate-pulse rounded-md" />
                </div>
              ) : (
                <div className="space-y-0">
                  <ActivityItem
                    avatarFallback="FB"
                    title={<>You submitted feedback for <strong>Frank Bennett</strong></>}
                    description="Senior Backend Developer - Recommended to hire"
                    timestamp="Yesterday"
                    status="success"
                  />
                  <ActivityItem
                    avatarFallback="AK"
                    title={<>Interview rescheduled with <strong>Adam Kim</strong></>}
                    description="UX Designer - Now on Friday at 1:00 PM"
                    timestamp="2 days ago"
                    status="warning"
                  />
                  <ActivityItem
                    avatarFallback="ML"
                    title={<>You submitted feedback for <strong>Maria Lopez</strong></>}
                    description="Data Scientist - Recommended to proceed"
                    timestamp="3 days ago"
                    status="success"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Schedule</CardTitle>
            <CardDescription>Your interview schedule for the next 7 days</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                <div className="h-8 bg-muted animate-pulse rounded-md w-1/3" />
                <div className="h-20 bg-muted animate-pulse rounded-md" />
                <div className="h-8 bg-muted animate-pulse rounded-md w-1/3" />
                <div className="h-20 bg-muted animate-pulse rounded-md" />
              </div>
            ) : (
              <div className="space-y-6">
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-2">Tomorrow, May 15</h3>
                  <div className="space-y-2">
                    <div className="bg-muted p-3 rounded-md flex flex-col md:flex-row md:items-center justify-between gap-2">
                      <div>
                        <p className="font-medium">9:00 AM - 10:00 AM</p>
                        <p className="text-sm">James Wilson - DevOps Engineer</p>
                      </div>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 md:self-start">Technical</Badge>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-2">Thursday, May 16</h3>
                  <div className="space-y-2">
                    <div className="bg-muted p-3 rounded-md flex flex-col md:flex-row md:items-center justify-between gap-2">
                      <div>
                        <p className="font-medium">11:00 AM - 12:00 PM</p>
                        <p className="text-sm">Emily Davis - Product Designer</p>
                      </div>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 md:self-start">Portfolio Review</Badge>
                    </div>
                    <div className="bg-muted p-3 rounded-md flex flex-col md:flex-row md:items-center justify-between gap-2">
                      <div>
                        <p className="font-medium">3:30 PM - 4:30 PM</p>
                        <p className="text-sm">Carlos Rodriguez - Full Stack Developer</p>
                      </div>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 md:self-start">Technical</Badge>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">View Full Calendar</Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default InterviewerDashboard;
