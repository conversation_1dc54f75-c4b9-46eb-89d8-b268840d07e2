
import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import ScoreSelection from './ScoreSelection';
import RecommendationButtons from './RecommendationButtons';

interface FeedbackCriteria {
  id: string;
  title: string;
  description: string;
  score: number;
  notes: string;
}

interface FeedbackFormProps {
  criteria: FeedbackCriteria[];
  onCriteriaChange: (id: string, field: string, value: any) => void;
  overallRecommendation: string;
  onRecommendationChange: (value: string) => void;
  overallComments: string;
  onOverallCommentsChange: (value: string) => void;
}

const FeedbackForm = ({
  criteria,
  onCriteriaChange,
  overallRecommendation,
  onRecommendationChange,
  overallComments,
  onOverallCommentsChange
}: FeedbackFormProps) => {
  return (
    <>
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Your Overall Recommendation</h3>
        <RecommendationButtons 
          value={overallRecommendation} 
          onChange={onRecommendationChange}
        />
      </div>

      <div className="space-y-8">
        {criteria.map((item) => (
          <div key={item.id} className="p-6 bg-white rounded-md shadow-sm border">
            <h3 className="text-lg font-medium mb-2">{item.title}</h3>
            <p className="text-sm text-muted-foreground mb-4">{item.description}</p>
            
            <div className="mb-4">
              <ScoreSelection 
                value={item.score} 
                onChange={(value) => onCriteriaChange(item.id, 'score', value)}
              />
            </div>
            
            <div>
              <Label htmlFor={`notes-${item.id}`} className="mb-2 block text-sm">
                Note
              </Label>
              <Textarea
                id={`notes-${item.id}`}
                placeholder="Add your comments..."
                value={item.notes}
                onChange={(e) => onCriteriaChange(item.id, 'notes', e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-8">
        <h3 className="text-lg font-medium mb-4">Overall Comments</h3>
        <Textarea
          placeholder="Add your overall comments about the candidate..."
          value={overallComments}
          onChange={(e) => onOverallCommentsChange(e.target.value)}
          className="min-h-[150px]"
        />
      </div>
    </>
  );
};

export default FeedbackForm;
