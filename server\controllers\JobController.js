import { logger } from '../utils/logger.js';
import { responseHandler } from '../utils/responseHandler.js';
import { jobService } from '../services/jobService.js';
import { candidateService } from '../services/candidateService.js';

export const jobController = {
  async getJobs(req, res) {
    try {
      const { page = 1, limit = 10, ...filters } = req.query;
      const result = await jobService.getJobs({ page, limit, ...filters });
      logger.info('Jobs retrieved successfully');
      return responseHandler.success(res, result);
    } catch (err) {
      logger.error('Failed to get jobs:', err);
      return responseHandler.error(res, 'Failed to retrieve jobs');
    }
  },

  async getJobById(req, res) {
    try {
      const result = await jobService.getJobById(req.params.id);
      
      if (!result.success) {
        logger.info(`Job not found: ${req.params.id}`);
        return responseHandler.notFound(res, 'Job not found');
      }

      logger.info(`Job retrieved: ${req.params.id}`);
      return responseHandler.success(res, { job: result.job });
    } catch (err) {
      logger.error('Failed to get job:', err);
      return responseHandler.error(res, 'Failed to retrieve job');
    }
  },

  async createJob(req, res) {
    try {
      // Add user IDs to job data
      const jobData = {
        ...req.body,
        created_by: req.user.id,
        updated_by: req.user.id
      };

      const result = await jobService.createJob(jobData);

      res.status(201).json(result);
    } catch (error) {
      logger.error('Failed to create job:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to create job'
      });
    }
  },

  async updateJob(req, res) {
    try {
      const result = await jobService.updateJob(req.params.id, {
        ...req.body,
        updated_by: req.user.id
      });
      
      if (!result.success) {
        logger.info(`Failed to update job: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Job updated: ${req.params.id}`);
      return responseHandler.success(res, { job: result.job });
    } catch (err) {
      logger.error('Failed to update job:', err);
      return responseHandler.error(res, 'Failed to update job');
    }
  },

  async deleteJob(req, res) {
    try {
      const result = await jobService.deleteJob(req.params.id, req.user.id);
      
      if (!result.success) {
        logger.info(`Failed to delete job: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Job deleted: ${req.params.id}`);
      return responseHandler.success(res, { message: 'Job deleted successfully' });
    } catch (err) {
      logger.error('Failed to delete job:', err);
      return responseHandler.error(res, 'Failed to delete job');
    }
  },

  async postToPlatform(req, res) {
    try {
      const result = await jobService.postToPlatform(req.params.id, req.body);
      
      if (!result.success) {
        logger.info(`Failed to post job to platform: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`Job posted to platform: ${req.params.id}`);
      return responseHandler.success(res, { jobBoardPost: result.jobBoardPost });
    } catch (err) {
      logger.error('Failed to post job to platform:', err);
      return responseHandler.error(res, 'Failed to post job to platform');
    }
  },

  async getJobCandidates(req, res) {
    try {
      const { id } = req.params;
      let { page, limit } = req.query;
      page = parseInt(page) || 1;
      limit = parseInt(limit) || 10;
      if (!id) {
        logger.info('Job ID is required to get candidates');
        return responseHandler.error(res, 'Job ID is required', 400);
      }

      const result = await candidateService.getCandidates(page, limit, { job_id: id });
      logger.info('Job candidates retrieved successfully');
      return responseHandler.success(res, result);
    } catch (err) {
      logger.error('Failed to get job candidates:', err);
      return responseHandler.error(res, 'Failed to retrieve job candidates');
    }
  }
};