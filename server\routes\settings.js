import express from "express";
import { 
  getScreeningCriteria,
  createScreeningCriteria
} from "../controllers/SettingsController.js";
import { asyncHandler } from "../middleware/asyncHandler.js";
import { authenticate } from "../middleware/authMiddleware.js";
import { settingsValidation } from "../validation/settingsValidation.js";

const router = express.Router();

router.use(authenticate);

router.get("/screening-criteria/:jobId", asyncHandler(getScreeningCriteria));
router.post("/screening-criteria", [...settingsValidation.createCriteria], asyncHandler(createScreeningCriteria));

export default router;