import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface CandidateProfileTabProps {
  candidateDetail: any;
}

const CandidateProfileTab: React.FC<CandidateProfileTabProps> = ({ candidateDetail }) => {
  return (
    <>
      <Card className="border border-gray-100 shadow-sm">
        <CardContent className="p-4">
          <h3 className="text-base font-medium mb-3">Personal Information</h3>
          <div className="space-y-4 text-sm">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-500 mb-1">Full Name</p>
                <p className="font-medium">{candidateDetail.name}</p>
              </div>
              <div>
                <p className="text-gray-500 mb-1">Email</p>
                <p className="font-medium">{candidateDetail.email}</p>
              </div>
              <div>
                <p className="text-gray-500 mb-1">Phone</p>
                <p className="font-medium">{candidateDetail.phone}</p>
              </div>
              <div>
                <p className="text-gray-500 mb-1">Location</p>
                <p className="font-medium">{candidateDetail.location}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-100 shadow-sm">
        <CardContent className="p-4">
          <h3 className="text-base font-medium mb-3">Skills</h3>
          <div className="flex flex-wrap gap-1">
            {candidateDetail.skills.map((skill: string, index: number) => (
              <Badge key={index} variant="secondary">
                {skill}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-100 shadow-sm">
        <CardContent className="p-4">
          <h3 className="text-base font-medium mb-3">Education</h3>
          <div className="space-y-3">
            {candidateDetail.education.map((edu: string, index: number) => (
              <div
                key={index}
                className="pb-2 border-b border-gray-100 last:border-0"
              >
                <p className="font-medium">{edu}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default CandidateProfileTab; 