import jwt from 'jsonwebtoken';
import { supabase } from '../config/db.js';
import { logger } from '../utils/logger.js';
import { passwordUtils } from '../utils/passwordUtils.js';
import { jwtUtils } from '../utils/jwtUtils.js';

export const authService = {
  async login(email, password) {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          full_name,
          password_hash,
          role_id,
          language_preference,
          flag_active,
          flag_deleted,
          role:roles!role_id(name)
        `)
        .eq('email', email)
        .eq('flag_deleted', false)
        .single();

      if (error || !user || !(await passwordUtils.comparePassword(password, user.password_hash))) {
        return { success: false, message: 'Invalid credentials' };
      }

      // Use the jwtUtils to generate the token with the role name
      const token = jwtUtils.generateToken(user);

      return {
        success: true,
        token,
        user: this.sanitizeUser(user),
        expiresIn: 3600 // 1 hour in seconds
      };
    } catch (error) {
      logger.error('Lo<PERSON> failed:', error);
      throw error;
    }
  },

  // async register(userData) {
  //   try {
  //     const { email, password, full_name, role_id } = userData;

  //     // Check if user exists
  //     const { data: existingUser } = await supabase
  //       .from('users')
  //       .select('id')
  //       .eq('email', email)
  //       .eq('flag_deleted', false)
  //       .single();

  //     if (existingUser) {
  //       return { success: false, message: 'Email already registered' };
  //     }

  //     // Create new user
  //     const password_hash = await passwordUtils.hashPassword(password);
  //     const { data: user, error } = await supabase
  //       .from('users')
  //       .insert([{
  //         email,
  //         password_hash,
  //         full_name,
  //         role_id,
  //         language_preference: 'en',
  //         flag_active: true,
  //         flag_deleted: false
  //       }])
  //       .select(`
  //         id,
  //         email,
  //         full_name,
  //         role_id,
  //         language_preference,
  //         flag_active,
  //         flag_deleted,
  //         role:roles!role_id(name)
  //       `)
  //       .single();

  //     if (error) throw error;

  //     const token = jwt.sign(
  //       { 
  //         id: user.id, 
  //         role: user.role_id,
  //         exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour expiration
  //       },
  //       process.env.JWT_SECRET
  //     );

  //     return {
  //       success: true,
  //       token,
  //       user: this.sanitizeUser(user),
  //       expiresIn: 3600 // 1 hour in seconds
  //     };
  //   } catch (error) {
  //     logger.error('Registration failed:', error);
  //     throw error;
  //   }
  // },

  async register(userData) {
    try {
      const { email, password, full_name, role } = userData;

      // Check if user exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .eq('flag_deleted', false)
        .single();

      if (existingUser) {
        return { success: false, message: 'Email already registered' };
      }

      // Get role_id by role name
      const { data: roleData, error: roleError } = await supabase
        .from('roles')
        .select('id')
        .eq('name', role.toLowerCase())
        .eq('flag_deleted', false)
        .single();

      if (roleError || !roleData) {
        return { success: false, message: 'Invalid role specified' };
      }

      const role_id = roleData.id;
      const password_hash = await passwordUtils.hashPassword(password);

      const { data: user, error } = await supabase
        .from('users')
        .insert([{
          email,
          password_hash,
          full_name,
          role_id,
          language_preference: 'en',
          flag_active: true,
          flag_deleted: false
        }])
        .select(`
          id,
          email,
          full_name,
          role_id,
          language_preference,
          flag_active,
          flag_deleted,
          role:roles!role_id(name)
        `)
        .single();

      if (error) throw error;

      // Use the jwtUtils to generate the token with the role name
      const token = jwtUtils.generateToken(user);

      return {
        success: true,
        token,
        user: this.sanitizeUser(user),
        expiresIn: 3600
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  },

  async getCurrentUser(userId) {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          full_name,
          role_id,
          language_preference,
          flag_active,
          flag_deleted,
          role:roles!role_id(name)
        `)
        .eq('id', userId)
        .eq('flag_deleted', false)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'User not found' };
        }
        throw error;
      }

      return {
        success: true,
        user: this.sanitizeUser(user)
      };
    } catch (error) {
      logger.error('Failed to get current user:', error);
      throw error;
    }
  },

  sanitizeUser(user) {
    const { password_hash, ...sanitizedUser } = user;
    return sanitizedUser;
  }
};