import express from "express";
import { 
  getLanguages,
  updateUserLanguage
} from "../controllers/LanguageController.js";
import { asyncHandler } from "../middleware/asyncHandler.js";
import { authenticate } from "../middleware/authMiddleware.js";
import { languageValidation } from "../validation/languageValidation.js";

const router = express.Router();

router.use(authenticate);

router.get("/", asyncHandler(getLanguages));
router.put("/users/:id/language", [...languageValidation.updateLanguage], asyncHandler(updateUserLanguage));

export default router;