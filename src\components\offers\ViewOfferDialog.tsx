
import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Download, FileText } from "lucide-react";

export interface OfferDetails {
  offer_id?: string;
  candidate_id: string;
  candidate_name?: string;
  job_id: string;
  job_title?: string;
  recruiter_id: string;
  department: string;
  designation: string;
  employment_type: string;
  location: string;
  ctc_offered: number;
  fixed_component: number;
  variable_component: number;
  joining_bonus?: number;
  stock_options?: string;
  benefits?: string;
  expected_joining_date?: string;
  offer_valid_till?: string;
  offer_status?: string;
  created_at?: string;
  accepted_at?: string;
  declined_at?: string;
  decline_reason?: string;
}

interface ViewOfferDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  offerData: OfferDetails | null;
}

export function ViewOfferDialog({ open, onOpenChange, offerData }: ViewOfferDialogProps) {
  if (!offerData) {
    return null;
  }

  const handleDownload = () => {
    console.log("Downloading offer letter for:", offerData.offer_id);
    // Implementation for downloading the offer letter
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl overflow-y-auto max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Offer Details</span>
            {offerData.offer_status && (
              <Badge variant={
                offerData.offer_status === "Accepted" ? "secondary" : 
                offerData.offer_status === "Declined" ? "destructive" : 
                offerData.offer_status === "Sent" ? "default" : "outline"
              }>
                {offerData.offer_status}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="flex flex-col md:flex-row gap-4 items-start justify-between">
            <div>
              <h2 className="text-xl font-semibold">{offerData.candidate_name || offerData.candidate_id}</h2>
              <p className="text-muted-foreground">{offerData.job_title || offerData.designation}</p>
            </div>
            <Button variant="outline" onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download Offer
            </Button>
          </div>

          <Separator />

          <Card>
            <CardContent className="p-4 space-y-4">
              <h3 className="font-semibold text-base">Candidate & Job Information</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Candidate ID</p>
                  <p className="font-medium">{offerData.candidate_id}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Job ID</p>
                  <p className="font-medium">{offerData.job_id}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Department</p>
                  <p className="font-medium">{offerData.department}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Designation</p>
                  <p className="font-medium">{offerData.designation}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Employment Type</p>
                  <p className="font-medium">{offerData.employment_type}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Location</p>
                  <p className="font-medium">{offerData.location}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 space-y-4">
              <h3 className="font-semibold text-base">Compensation & Benefits</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">CTC Offered</p>
                  <p className="font-medium">${offerData.ctc_offered.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Fixed Component</p>
                  <p className="font-medium">${offerData.fixed_component.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Variable Component</p>
                  <p className="font-medium">${offerData.variable_component.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Joining Bonus</p>
                  <p className="font-medium">
                    {offerData.joining_bonus 
                      ? `$${offerData.joining_bonus.toLocaleString()}` 
                      : 'N/A'}
                  </p>
                </div>
                <div className="col-span-2">
                  <p className="text-muted-foreground">Stock Options</p>
                  <p className="font-medium">{offerData.stock_options || 'N/A'}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-muted-foreground">Benefits</p>
                  <p className="font-medium whitespace-pre-line">{offerData.benefits || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 space-y-4">
              <h3 className="font-semibold text-base">Joining & Validity</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Expected Joining Date</p>
                  <p className="font-medium">{offerData.expected_joining_date || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Offer Valid Till</p>
                  <p className="font-medium">{offerData.offer_valid_till || 'N/A'}</p>
                </div>
                {offerData.created_at && (
                  <div>
                    <p className="text-muted-foreground">Created On</p>
                    <p className="font-medium">{offerData.created_at}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
