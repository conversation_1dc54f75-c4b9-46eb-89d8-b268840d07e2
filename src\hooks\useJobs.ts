import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/components/auth/AuthProvider';
import { toast } from '@/components/ui/sonner';
import { API_CONFIG } from '@/utils/Constant';

export interface Job {
  id: string;
  title: string;
  description: string;
  requirements?: string[];
  responsibilities?: string[];
  department: string;
  location: string;
  office_location?: string;
  employment_type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status: 'draft' | 'published' | 'closed' | 'open';
  salary_min?: number;
  salary_max?: number;
  salary_frequency?: 'monthly' | 'yearly';
  tools_and_tech?: string;
  good_to_have?: string;
  experience_years?: number;
  skills?: string[];
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  flag_active: boolean;
  flag_deleted: boolean;
  creator_email?: string;
  creator?: {
    full_name: string;
    email: string;
  };
}

export interface CreateJobData {
  title: string;
  description: string;
  department: string;
  location: string;
  office_location?: string;
  employment_type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status?: 'draft' | 'published' | 'closed' | 'open';
  salary_min?: number;
  salary_max?: number;
  tools_and_tech?: string;
  good_to_have?: string;
  experience_years?: number;
  creator_email?: string;
}

export interface JobsResponse {
  success: boolean;
  message: string;
  data: {
    jobs: Job[];
    pagination: {
      total: number;
      page: number;
      pages: number;
    };
  };
}

export interface CreateJobResponse {
  success: boolean;
  message: string;
  n8nResponse: Job;
}

export const useJobs = () => {
  const { user } = useAuth();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    pages: 0
  });

  // Function to get the auth token (matching useInterviewers pattern)
  const getAuthToken = useCallback(() => {
    return localStorage.getItem('token');
  }, []);

  // Fetch jobs
  const fetchJobs = useCallback(async (page = 1, limit = 10, filters = {}) => {
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...filters
      });

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.JOBS}?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: JobsResponse = await response.json();
      setJobs(data.data.jobs || []);
      setPagination(data.data.pagination);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch jobs';
      setError(errorMessage);
      if (page > 1) {
        toast.error('Failed to fetch jobs');
      }
    } finally {
      setLoading(false);
    }
  }, [getAuthToken]);

  // Create job
  const createJob = useCallback(async (jobData: CreateJobData): Promise<CreateJobResponse | null> => {
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.JOBS}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(jobData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: CreateJobResponse = await response.json();
      
      if (result.success) {
        toast.success('Job created successfully!');
        // Refresh the jobs list
        await fetchJobs();
      } else {
        throw new Error(result.message || 'Failed to create job');
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create job';
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, fetchJobs]);

  // Update job
  const updateJob = useCallback(async (id: string, updateData: Partial<Job>): Promise<boolean> => {
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.JOBS}/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('Job updated successfully!');
        // Refresh the jobs list
        await fetchJobs();
        return true;
      } else {
        throw new Error(result.message || 'Failed to update job');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update job';
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, fetchJobs]);

  // Delete job
  const deleteJob = useCallback(async (id: string): Promise<boolean> => {
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.JOBS}/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('Job deleted successfully!');
        // Refresh the jobs list
        await fetchJobs();
        return true;
      } else {
        throw new Error(result.message || 'Failed to delete job');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete job';
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, fetchJobs]);

  // Get job by ID
  const getJobById = useCallback(async (id: string): Promise<Job | null> => {
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      return null;
    }

    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.JOBS}/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        return result.data?.job || null;
      } else {
        throw new Error(result.message || 'Job not found');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch job';
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    }
  }, [getAuthToken]);

  // Load jobs on mount
  useEffect(() => {
    const token = getAuthToken();
    if (token) {
      fetchJobs();
    }
  }, [fetchJobs, getAuthToken]);

  return {
    jobs,
    loading,
    error,
    pagination,
    fetchJobs,
    createJob,
    updateJob,
    deleteJob,
    getJobById
  };
}; 