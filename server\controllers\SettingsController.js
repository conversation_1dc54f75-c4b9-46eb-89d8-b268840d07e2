import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { settingsService } from "../services/settingsService.js";

export const getScreeningCriteria = async (req, res) => {
  try {
    const result = await settingsService.getScreeningCriteria(req.params.jobId);
    
    if (!result.success) {
      logger.info(`Screening criteria not found: ${req.params.jobId}`);
      return responseHandler.error(res, result.message, 404);
    }

    logger.info(`Screening criteria retrieved for job: ${req.params.jobId}`);
    return responseHandler.success(res, { criteria: result.criteria });
  } catch (err) {
    logger.error("Failed to get screening criteria:", err);
    return responseHandler.error(res, "Failed to retrieve screening criteria");
  }
};

export const createScreeningCriteria = async (req, res) => {
  try {
    const result = await settingsService.createScreeningCriteria({
      ...req.body,
      created_by: req.user._id
    });
    
    if (!result.success) {
      logger.info(`Screening criteria creation failed: ${result.message}`);
      return responseHandler.badRequest(res, result.message);
    }

    logger.info(`Screening criteria created for job: ${req.body.job_id}`);
    return responseHandler.success(res, { criteria: result.criteria }, 201);
  } catch (err) {
    logger.error("Failed to create screening criteria:", err);
    return responseHandler.error(res, "Failed to create screening criteria");
  }
};