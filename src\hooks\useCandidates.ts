import { useState } from "react";
import { toast } from "sonner";
import { USE_CANDIDATES_CONSTANTS, API_CONFIG } from "@/utils/Constant";

const { ERRORS, SUCCESS, TOAST, FORM_FIELDS, DATA_MAPPINGS } = USE_CANDIDATES_CONSTANTS;

export interface CreateCandidateData {
  name: string;
  email: string;
  phone?: string;
  jobId: string;
  resume: FileList;
  source: string;
}

export interface CreateCandidateResponse {
  success: boolean;
  message: string;
  data?: any;
}

export enum CandidateStatus {
  Applied = "applied",
  Screening = "screening",
  Interview = "interview",
  Offer = "offer",
  Rejected = "rejected",
  Hired = "hired",
}

export interface Candidate {
  id: string;
  name: string;
  jobTitle: string;
  jobId: string;
  location: string;
  status: CandidateStatus;
  matchScore: number;
  appliedDate: string;
  lastUpdated: string;
  skills: string[];
  email: string;
  phone: string;
  experience?: string;
  education?: string[];
  resumeUrl?: string;
  source?: string;
  notes?: string;
  photo?: string;
  interviews: any[];
  resume_url?: string;
  suitability_summary: string;
  unsuitability_summary: string;
  latest_stage: string;
}

export interface GetCandidatesResponse {
  candidates: Candidate[];
  pagination: {
    total: number;
    page: number;
    pages: number;
  };
}

export const useCandidates = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const getAuthToken = () => {
    return localStorage.getItem("token");
  };

  const createCandidate = async (
    candidateData: CreateCandidateData
  ): Promise<CreateCandidateResponse | null> => {
    setLoading(true);
    setError(null);
    setSuccess(false);
    const token = getAuthToken();
    if (!token) {
      setError(ERRORS.AUTH_TOKEN_NOT_FOUND);
      toast.error(TOAST.AUTH_ERROR);
      setLoading(false);
      return null;
    }
    try {
      const formData = new FormData();
      formData.append("name", candidateData.name);
      formData.append("email", candidateData.email);
      if (candidateData.phone) formData.append(FORM_FIELDS.PHONE_NUMBER, candidateData.phone);
      formData.append("jobId", candidateData.jobId);
      formData.append("source", candidateData.source);
      formData.append(FORM_FIELDS.CV, candidateData.resume[0]);

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CANDIDATES}`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      const result = await response.json();
      if (!response.ok || result.success === false) {
        throw new Error(result.message || ERRORS.FAILED_TO_ADD_CANDIDATE);
      }
      setSuccess(true);
      toast.success(TOAST.ADD_SUCCESS);
      return { success: true, message: SUCCESS.CANDIDATE_ADDED, data: result };
    } catch (err: any) {
      setError(err.message || ERRORS.FAILED_TO_ADD_CANDIDATE);
      toast.error(TOAST.ADD_ERROR, { description: err.message });
      return { success: false, message: err.message };
    } finally {
      setLoading(false);
    }
  };

  // Fetch candidates for a specific job
  const getCandidatesByJobId = async (
    jobId: string,
    page: number = 1,
    limit: number = 100
  ): Promise<GetCandidatesResponse | null> => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError(ERRORS.AUTH_TOKEN_NOT_FOUND);
      toast.error(TOAST.AUTH_ERROR);
      setLoading(false);
      return null;
    }
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.JOBS}/${jobId}/candidates?page=${page}&limit=${limit}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || ERRORS.FAILED_TO_FETCH_CANDIDATES);
      }
      // The backend returns { success, message, data: { candidates, pagination } }
      const data = result.data || {};
      return {
        candidates: (data.candidates || []).map((c: any) => ({
          ...c,
          status: c[DATA_MAPPINGS.STATUS], // map can_status to status for UI compatibility
          appliedDate: c[DATA_MAPPINGS.APPLIED_DATE], // map created_at to appliedDate for UI compatibility
        })),
        pagination: data.pagination || { total: 0, page: 1, pages: 1 },
      };
    } catch (err: any) {
      setError(err.message || ERRORS.FAILED_TO_FETCH_CANDIDATES);
      toast.error(TOAST.FETCH_ERROR, { description: err.message });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Fetch all candidates
  const fetchAllCandidates = async (
    page: number = 1,
    limit: number = 100
  ): Promise<GetCandidatesResponse | null> => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError(ERRORS.AUTH_TOKEN_NOT_FOUND);
      toast.error(TOAST.AUTH_ERROR);
      setLoading(false);
      return null;
    }
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CANDIDATES}?page=${page}&limit=${limit}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || ERRORS.FAILED_TO_FETCH_ALL_CANDIDATES);
      }
      const data = result.data || {};
      toast.success(TOAST.FETCH_ALL_SUCCESS);
      return {
        candidates: (data.candidates || []).map((c: any) => {
          let location = c.location || "";
          if (
            !location &&
            Array.isArray(c.candidate_score) &&
            c.candidate_score.length > 0 &&
            c.candidate_score[0].location
          ) {
            location = c.candidate_score[0].location;
          }
          return {
            id: c.id,
            name: c.name,
            jobTitle: c.job_post?.title || "",
            jobId: c.job_id || "",
            location,
            status: c[DATA_MAPPINGS.STATUS] || c.status || "applied",
            matchScore:
              Array.isArray(c.candidate_score) && c.candidate_score.length > 0
                ? c.candidate_score[0].score
                : 0,
            appliedDate: c[DATA_MAPPINGS.APPLIED_DATE] || c.created_at || "",
            lastUpdated: c.updated_at || "",
            skills: [], // Not present in backend, set as empty array
            email: c.email || "",
            phone: c.phone || "",
            experience: c.experience,
            education: c.education,
            resumeUrl: c.resume_url || "",
            source: c.source,
            notes: c.notes,
            photo: c.photo,
            interviews: [],
            latest_stage: c.latest_stage,
          };
        }),
        pagination: data.pagination || { total: 0, page: 1, pages: 1 },
      };
    } catch (err: any) {
      setError(err.message || ERRORS.FAILED_TO_FETCH_ALL_CANDIDATES);
      toast.error(TOAST.FETCH_ALL_ERROR, { description: err.message });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Fetch a single candidate by ID
  const getCandidateById = async (id: string): Promise<Candidate | null> => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError(ERRORS.AUTH_TOKEN_NOT_FOUND);
      toast.error(TOAST.AUTH_ERROR);
      setLoading(false);
      return null;
    }
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CANDIDATES}/${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || ERRORS.FAILED_TO_FETCH_CANDIDATE);
      }
      const c = result.data;
      // Parse skills and education from candidate_score if present
      let skills: string[] = [];
      let education: string[] = [];
      let location = c.location || "";
      if (Array.isArray(c.candidate_score) && c.candidate_score.length > 0) {
        try {
          skills = c.candidate_score[0].skills ? JSON.parse(c.candidate_score[0].skills) : [];
        } catch {
          skills = [];
        }
        try {
          const eduObj = c.candidate_score[0].education
            ? JSON.parse(c.candidate_score[0].education)
            : null;
          if (eduObj) {
            education = [
              `${eduObj.degree || ""}${
                eduObj.field_of_study ? " in " + eduObj.field_of_study : ""
              }${eduObj.institution ? ", " + eduObj.institution : ""}${
                eduObj.minor ? " (Minor: " + eduObj.minor + ")" : ""
              }`,
            ];
          }
        } catch {
          education = [];
        }
        if (!location && c.candidate_score[0].location) {
          location = c.candidate_score[0].location;
        }
      }
      toast.success(TOAST.FETCH_CANDIDATE_SUCCESS);
      return {
        id: c.id,
        name: c.name,
        jobTitle: c.job_post?.title || "",
        jobId: c.job_id || "",
        location,
        status: c[DATA_MAPPINGS.STATUS] || c.status || "applied",
        matchScore:
          Array.isArray(c.candidate_score) && c.candidate_score.length > 0
            ? c.candidate_score[0].score
            : 0,
        appliedDate: c[DATA_MAPPINGS.APPLIED_DATE] || c.created_at || "",
        lastUpdated: c.updated_at || "",
        skills,
        email: c.email || "",
        phone: c.phone || "",
        experience: c.experience || "",
        education,
        resumeUrl: c.resume_url || "",
        source: c.source || "",
        notes: c.notes,
        photo: c.photo,
        interviews: [], // You may want to fetch interviews separately or extend this
        suitability_summary: c.candidate_score[0].suitability_summary || 'No suitability summary available',
        unsuitability_summary: c.candidate_score[0].unsuitability_summary || 'No unsuitability summary available',
      };
    } catch (err: any) {
      setError(err.message || ERRORS.FAILED_TO_FETCH_CANDIDATE);
      toast.error(TOAST.FETCH_CANDIDATE_ERROR, { description: err.message });
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateCandidateStatus = async (id: string, status: CandidateStatus): Promise<boolean> => {
    setLoading(true);
    setError(null);
    setSuccess(false);
    const token = getAuthToken();
    if (!token) {
      setError(ERRORS.AUTH_TOKEN_NOT_FOUND);
      toast.error(TOAST.AUTH_ERROR);
      setLoading(false);
      return false;
    }
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CANDIDATES}/${id}/status`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ status }),
        }
      );

      const result = await response.json();
      if (!response.ok || result.success === false) {
        throw new Error(result.message || ERRORS.FAILED_TO_UPDATE_CANDIDATE_STATUS);
      }
      setSuccess(true);
      toast.success(TOAST.UPDATE_STATUS_SUCCESS);
      return true;
    } catch (err: any) {
      setError(err.message || ERRORS.FAILED_TO_UPDATE_CANDIDATE_STATUS);
      toast.error(TOAST.UPDATE_STATUS_ERROR, { description: err.message });
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    createCandidate,
    getCandidatesByJobId,
    fetchAllCandidates,
    getCandidateById,
    updateCandidateStatus,
    loading,
    error,
    success,
  };
};
