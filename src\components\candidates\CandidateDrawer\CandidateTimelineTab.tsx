import React from "react";
import { Card, CardContent } from "@/components/ui/card";

import { CandidateStatus } from "@/hooks/useCandidates";

interface CandidateTimelineTabProps {
  candidateDetail: any;
  getResultBadge: (result: string) => React.ReactNode;
}

const CandidateTimelineTab: React.FC<CandidateTimelineTabProps> = ({ candidateDetail, getResultBadge }) => {
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    return date.toLocaleDateString(undefined, options);
  };

  return (
    <Card className="border border-gray-100 shadow-sm">
      <CardContent className="p-4">
        <h3 className="text-base font-medium mb-4">Candidate Timeline</h3>
        <div className="relative border-l border-gray-200 ml-3">
          <div className="mb-6 relative">
            <div className="absolute -left-3 w-6 h-6 bg-blue-100 rounded-full border border-white flex items-center justify-center">
              <div className="h-2.5 w-2.5 bg-blue-500 rounded-full"></div>
            </div>
            <div className="ml-6">
              <h4 className="font-medium">Application Received</h4>
              <p className="text-sm text-gray-600">
                {formatDateTime(candidateDetail.appliedDate)}
              </p>
              <p className="mt-1 text-sm">
                Candidate applied for {candidateDetail.jobTitle} position
              </p>
            </div>
          </div>
          {candidateDetail.status !== CandidateStatus.Applied && (
            <div className="mb-6 relative">
              <div className="absolute -left-3 w-6 h-6 bg-purple-100 rounded-full border border-white flex items-center justify-center">
                <div className="h-2.5 w-2.5 bg-purple-500 rounded-full"></div>
              </div>
              <div className="ml-6">
                <h4 className="font-medium">Moved to Screening</h4>
                <p className="text-sm text-gray-600">
                  {formatDateTime(candidateDetail.lastUpdated)}
                </p>
                <p className="mt-1 text-sm">Resume screened by HR team</p>
              </div>
            </div>
          )}
          {candidateDetail.interviews.map((interview: any, index: number) => (
            <div key={index} className="mb-6 relative">
              <div className="absolute -left-3 w-6 h-6 bg-amber-100 rounded-full border border-white flex items-center justify-center">
                <div className="h-2.5 w-2.5 bg-amber-500 rounded-full"></div>
              </div>
              <div className="ml-6">
                <h4 className="font-medium">
                  {interview.title} Interview {getResultBadge(interview.result)}
                </h4>
                <p className="text-sm text-gray-600">
                  {formatDateTime(interview.date)}
                </p>
                <p className="mt-1 text-sm">
                  Interviewed by {interview.interviewer}
                </p>
              </div>
            </div>
          ))}
          {candidateDetail.status === CandidateStatus.Hired && (
            <div className="relative">
              <div className="absolute -left-3 w-6 h-6 bg-green-100 rounded-full border border-white flex items-center justify-center">
                <div className="h-2.5 w-2.5 bg-green-500 rounded-full"></div>
              </div>
              <div className="ml-6">
                <h4 className="font-medium">Candidate Hired</h4>
                <p className="text-sm text-gray-600">
                  {formatDateTime(candidateDetail.lastUpdated)}
                </p>
                <p className="mt-1 text-sm">
                  Offer accepted for {candidateDetail.jobTitle} position
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CandidateTimelineTab;