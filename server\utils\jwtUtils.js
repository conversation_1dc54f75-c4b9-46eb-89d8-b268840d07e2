import jwt from 'jsonwebtoken';
import { logger } from './logger.js';

export const jwtUtils = {
  generateToken(user) {
    try {
      return jwt.sign(
        {
          id: user.id,
          role: user.role.name,
          email: user.email
        },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );
    } catch (error) {
      logger.error('Token generation failed:', error);
      throw new Error('Failed to generate authentication token');
    }
  },

  verifyToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      logger.error('Token verification failed:', error);
      throw new Error('Invalid authentication token');
    }
  }
};