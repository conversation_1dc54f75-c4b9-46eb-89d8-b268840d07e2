
import React from 'react';
import { Star } from 'lucide-react';

interface ScoreSelectionProps {
  onChange: (score: number) => void;
  value: number;
  maxScore?: number;
}

const ScoreSelection = ({ onChange, value, maxScore = 5 }: ScoreSelectionProps) => {
  return (
    <div className="flex items-center gap-1">
      {[...Array(maxScore)].map((_, index) => {
        const starValue = index + 1;
        return (
          <button
            key={index}
            type="button"
            onClick={() => onChange(starValue)}
            className={`focus:outline-none ${
              starValue <= value
                ? 'text-amber-500'
                : 'text-gray-300'
            }`}
          >
            <Star className="h-6 w-6 fill-current" />
          </button>
        );
      })}
      {value > 0 && (
        <span className="text-sm text-muted-foreground ml-2">
          {value}/5
        </span>
      )}
    </div>
  );
};

export default ScoreSelection;
