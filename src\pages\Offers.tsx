import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import {
  FileText,
  FileCheck,
  Search,
  Filter,
  SlidersHorizontal,
  Download,
  MailIcon,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Sparkles,
  PlusCircle,
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { StatsCard } from "@/components/dashboard/StatsCard";
import { CreateOfferDrawer } from "@/components/offers/CreateOfferDrawer";
import { ViewOfferDialog, OfferDetails } from "@/components/offers/ViewOfferDialog";

// Mock data for the view functionality
const mockOffers: Record<string, OfferDetails> = {
  "1": {
    offer_id: "OFF-001",
    candidate_id: "CAND-001",
    candidate_name: "David Park",
    job_id: "JOB-121",
    job_title: "Senior Frontend Developer",
    recruiter_id: "REC-123",
    department: "Engineering",
    designation: "Senior Frontend Developer",
    employment_type: "Full-time",
    location: "Remote",
    ctc_offered: 135000,
    fixed_component: 100000,
    variable_component: 35000,
    joining_bonus: 10000,
    stock_options: "1000 RSUs vesting over 4 years",
    benefits:
      "Health insurance\nDental and vision coverage\nFlexible work hours\n$150 monthly WFH allowance",
    expected_joining_date: "2023-06-15",
    offer_valid_till: "2023-05-25",
    offer_status: "Draft",
    created_at: "2023-05-10",
  },
  "2": {
    offer_id: "OFF-002",
    candidate_id: "CAND-002",
    candidate_name: "Sarah Johnson",
    job_id: "JOB-122",
    job_title: "Product Designer",
    recruiter_id: "REC-123",
    department: "Design",
    designation: "Product Designer",
    employment_type: "Full-time",
    location: "Hybrid - New York",
    ctc_offered: 120000,
    fixed_component: 90000,
    variable_component: 30000,
    joining_bonus: 5000,
    stock_options: "500 RSUs vesting over 4 years",
    benefits: "Health insurance\n401k matching\nUnlimited PTO",
    expected_joining_date: "2023-06-01",
    offer_valid_till: "2023-05-20",
    offer_status: "Ready",
    created_at: "2023-05-07",
  },
  "3": {
    offer_id: "OFF-003",
    candidate_id: "CAND-003",
    candidate_name: "Michael Chen",
    job_id: "JOB-123",
    job_title: "DevOps Engineer",
    recruiter_id: "REC-123",
    department: "Engineering",
    designation: "DevOps Engineer",
    employment_type: "Full-time",
    location: "Remote",
    ctc_offered: 140000,
    fixed_component: 110000,
    variable_component: 30000,
    joining_bonus: 15000,
    stock_options: "800 RSUs vesting over 4 years",
    benefits:
      "Health insurance\nDental and vision coverage\nHome internet allowance\nEquipment budget",
    expected_joining_date: "2023-05-25",
    offer_valid_till: "2023-05-18",
    offer_status: "Ready",
    created_at: "2023-05-05",
  },
  "4": {
    offer_id: "OFF-004",
    candidate_id: "CAND-004",
    candidate_name: "Jennifer Lee",
    job_id: "JOB-124",
    job_title: "Backend Developer",
    recruiter_id: "REC-124",
    department: "Engineering",
    designation: "Backend Developer",
    employment_type: "Full-time",
    location: "Remote",
    ctc_offered: 130000,
    fixed_component: 95000,
    variable_component: 35000,
    benefits: "Health insurance\nDental and vision coverage\nFlexible work hours",
    expected_joining_date: "2023-06-01",
    offer_valid_till: "2023-05-17",
    offer_status: "Sent",
    created_at: "2023-05-10",
  },
};

// Status badge mapping
const getStatusBadge = (status: string) => {
  switch (status) {
    case "Draft":
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700">
          <Clock className="h-3 w-3 mr-1" />
          Draft
        </Badge>
      );
    case "Ready":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700">
          <CheckCircle className="h-3 w-3 mr-1" />
          Ready
        </Badge>
      );
    case "Sent":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700">
          <MailIcon className="h-3 w-3 mr-1" />
          Sent
        </Badge>
      );
    case "Accepted":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700">
          <CheckCircle className="h-3 w-3 mr-1" />
          Accepted
        </Badge>
      );
    case "Declined":
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700">
          <XCircle className="h-3 w-3 mr-1" />
          Declined
        </Badge>
      );
    default:
      return null;
  }
};

const Offers = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("pending");
  const [createOfferOpen, setCreateOfferOpen] = useState(false);
  const [viewOfferOpen, setViewOfferOpen] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<OfferDetails | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Keep using mockOffers for demo, but prepare for database integration
  const [offers, setOffers] = useState<Record<string, OfferDetails>>(mockOffers);
  const [isUsingMockData, setIsUsingMockData] = useState(true);

  // This function will be called when connecting to a real database
  const fetchOffersFromDatabase = async () => {
    try {
      setLoading(true);
      // When you connect to a database, replace this comment with actual API call
      // const { data, error } = await supabase.from('offers').select('*');

      // For now, simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 800));

      // When connected to database, transform the data and set it
      // const offersRecord: Record<string, OfferDetails> = {};
      // data.forEach((offer: OfferDetails) => {
      //   offersRecord[offer.offer_id] = offer;
      // });

      // setOffers(offersRecord);
      // setIsUsingMockData(false);

      // For now, just use mock data
      setOffers(mockOffers);
    } catch (error) {
      console.error("Error fetching offers:", error);
      toast.error("Failed to fetch offers", {
        description: "Using demo data instead",
      });
      // Fallback to mock data if API fails
      setOffers(mockOffers);
    } finally {
      setLoading(false);
    }
  };

  // Simulate initial data loading
  useEffect(() => {
    setLoading(true);
    // Simulate API call
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // Filter offers based on status and search query
  const getFilteredOffers = (status: string) => {
    return Object.entries(offers)
      .filter(([_, offer]) => {
        if (status === "pending") {
          return ["Draft", "Ready"].includes(offer.offer_status);
        } else if (status === "sent") {
          return offer.offer_status === "Sent";
        } else if (status === "accepted") {
          return offer.offer_status === "Accepted";
        } else if (status === "declined") {
          return offer.offer_status === "Declined";
        }
        return true;
      })
      .filter(([_, offer]) => {
        if (!searchQuery) return true;
        const query = searchQuery.toLowerCase();
        return (
          offer.candidate_name.toLowerCase().includes(query) ||
          offer.job_title.toLowerCase().includes(query)
        );
      });
  };

  // Calculate counts for each tab
  const pendingCount = getFilteredOffers("pending").length;
  const sentCount = getFilteredOffers("sent").length;
  const acceptedCount = getFilteredOffers("accepted").length;
  const declinedCount = getFilteredOffers("declined").length;

  // Action handlers that will work with both mock and real data
  const handleCreateOffer = (candidateId?: string, candidateName?: string) => {
    setCreateOfferOpen(true);
  };

  const handleGenerateOffer = (candidateId?: string, candidateName?: string) => {
    toast.success("Generate Offer", {
      description: candidateName
        ? `Generating offer for ${candidateName}`
        : "This would open the AI offer generation tool",
    });
  };

  const handleSendOffer = (id: string) => {
    if (isUsingMockData) {
      // Update mock data
      setOffers(prev => ({
        ...prev,
        [id]: { ...prev[id], offer_status: "Sent" },
      }));

      toast.success(`Offer sent successfully`, {
        description: "The offer has been sent to the candidate",
      });
    } else {
      // When connected to database, update the database
      // const { error } = await supabase.from('offers').update({ offer_status: 'Sent' }).eq('offer_id', id);

      toast.success(`Send Offer ${id}`, {
        description: "This would send the offer to the candidate",
      });
    }
  };

  const handleDownloadOffer = (id: string) => {
    toast.success(`Download Offer ${id}`, {
      description: "This would download the offer letter",
    });
  };

  const handleViewOffer = (id: string) => {
    const offerData = offers[id];
    if (offerData) {
      setSelectedOffer(offerData);
      setViewOfferOpen(true);
    } else {
      toast.error("Offer not found", {
        description: "Could not find the selected offer details.",
      });
    }
  };

  // Reusable offer card component
  const OfferCard = ({ id, offer }: { id: string; offer: OfferDetails }) => (
    <div className="p-4 flex flex-col md:flex-row md:items-center justify-between gap-4 hover:bg-slate-50 transition-colors duration-300">
      <div>
        <h3 className="font-medium">{offer.candidate_name}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="secondary">{offer.job_title}</Badge>
          {getStatusBadge(offer.offer_status || "")}
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Salary: ${offer.fixed_component.toLocaleString()} - ${offer.ctc_offered.toLocaleString()}
        </p>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" size="sm" className="w-24" onClick={() => handleViewOffer(id)}>
          <FileText className="h-4 w-4 mr-1" />
          View
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="w-32"
          onClick={() => handleGenerateOffer(id, offer.candidate_name)}
        >
          <Sparkles className="h-4 w-4 mr-1" />
          AI Generate
        </Button>
        {offer.offer_status === "Draft" || offer.offer_status === "Ready" ? (
          <Button
            size="sm"
            className="w-24"
            onClick={() =>
              offer.offer_status === "Draft"
                ? handleCreateOffer(id, offer.candidate_name)
                : handleSendOffer(id)
            }
          >
            {offer.offer_status === "Draft" ? (
              <>
                <FileText className="h-4 w-4 mr-1" />
                Create
              </>
            ) : (
              <>
                <MailIcon className="h-4 w-4 mr-1" />
                Send
              </>
            )}
          </Button>
        ) : (
          <Button size="sm" className="w-28" onClick={() => handleDownloadOffer(id)}>
            <Download className="h-4 w-4 mr-1" />
            Download
          </Button>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Offer Management</h1>
          <p className="text-muted-foreground">Create and manage candidate offers</p>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="Search offers by candidate or position..." className="pl-8" />
        </div>
        <div className="flex gap-2">
          <Select>
            <SelectTrigger className="w-[160px]">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <span>Job Title</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Positions</SelectItem>
              <SelectItem value="senior-frontend">Senior Frontend</SelectItem>
              <SelectItem value="product-designer">Product Designer</SelectItem>
              <SelectItem value="backend">Backend Developer</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="icon">
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs defaultValue="pending" onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="pending" className="flex gap-2">
            Pending
            <span className="flex items-center justify-center rounded-full bg-blue-100 text-blue-900 text-xs font-medium px-2 py-0.5">
              {pendingCount}
            </span>
          </TabsTrigger>
          <TabsTrigger value="sent" className="flex gap-2">
            Sent
            <span className="flex items-center justify-center rounded-full bg-purple-100 text-purple-900 text-xs font-medium px-2 py-0.5">
              {sentCount}
            </span>
          </TabsTrigger>
          <TabsTrigger value="accepted" className="flex gap-2">
            Accepted
            <span className="flex items-center justify-center rounded-full bg-green-100 text-green-900 text-xs font-medium px-2 py-0.5">
              {acceptedCount}
            </span>
          </TabsTrigger>
          <TabsTrigger value="declined" className="flex gap-2">
            Declined
            <span className="flex items-center justify-center rounded-full bg-red-100 text-red-900 text-xs font-medium px-2 py-0.5">
              {declinedCount}
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Pending Offers</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-6 space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-24 bg-muted animate-pulse rounded-md" />
                  ))}
                </div>
              ) : (
                <div className="divide-y">
                  {getFilteredOffers("pending").length > 0 ? (
                    getFilteredOffers("pending").map(([id, offer]) => (
                      <OfferCard key={id} id={id} offer={offer} />
                    ))
                  ) : (
                    <div className="p-6 text-center text-muted-foreground">
                      No pending offers found
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sent">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Sent Offers</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-6 space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-24 bg-muted animate-pulse rounded-md" />
                  ))}
                </div>
              ) : (
                <div className="divide-y">
                  {getFilteredOffers("sent").length > 0 ? (
                    getFilteredOffers("sent").map(([id, offer]) => (
                      <OfferCard key={id} id={id} offer={offer} />
                    ))
                  ) : (
                    <div className="p-6 text-center text-muted-foreground">
                      No sent offers found
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="accepted">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Accepted Offers</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-6 space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-24 bg-muted animate-pulse rounded-md" />
                  ))}
                </div>
              ) : (
                <div className="divide-y">
                  {getFilteredOffers("accepted").length > 0 ? (
                    getFilteredOffers("accepted").map(([id, offer]) => (
                      <OfferCard key={id} id={id} offer={offer} />
                    ))
                  ) : (
                    <div className="p-6 text-center text-muted-foreground">
                      No accepted offers found
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="declined">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Declined Offers</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-6 space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-24 bg-muted animate-pulse rounded-md" />
                  ))}
                </div>
              ) : (
                <div className="divide-y">
                  {getFilteredOffers("declined").length > 0 ? (
                    getFilteredOffers("declined").map(([id, offer]) => (
                      <OfferCard key={id} id={id} offer={offer} />
                    ))
                  ) : (
                    <div className="p-6 text-center text-muted-foreground">
                      No declined offers found
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Offer Drawer */}
      <CreateOfferDrawer open={createOfferOpen} onOpenChange={setCreateOfferOpen} />

      {/* View Offer Dialog */}
      <ViewOfferDialog
        open={viewOfferOpen}
        onOpenChange={setViewOfferOpen}
        offerData={selectedOffer}
      />
    </div>
  );
};

export default Offers;
