import { z } from 'zod';
import { logger } from '../utils/logger.js';

export const validateRequest = (schema) => {
  return async (req, res, next) => {
    try {
      await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params
      });
      next();
    } catch (error) {
      logger.error('Validation failed:', error);
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors
      });
    }
  };
};

// Common validation schemas
export const schemas = {
  job: z.object({
    body: z.object({
      title: z.string().min(3),
      description: z.string().min(10),
      requirements: z.array(z.string()),
      location: z.string(),
      salary_range: z.object({
        min: z.number(),
        max: z.number()
      })
    })
  }),

  offer: z.object({
    body: z.object({
      job_post_id: z.string().uuid(),
      candidate_id: z.string().uuid(),
      salary: z.number(),
      start_date: z.string().datetime(),
      expiry_date: z.string().datetime()
    })
  })
};