import { z } from 'zod';
import { validationMessages } from '../utils/messages.utils.js';
import { commonUtils } from '../utils/common.utils.js';

export const candidateValidation = {
  createCandidate: z.object({
    body: z.object({
      phoneNumber: z.string().min(10, validationMessages.PHONE_NUMBER_MIN).max(10, validationMessages.PHONE_NUMBER_MAX),
      jobId: z.string().uuid(validationMessages.INVALID_JOB_ID),
      name: z.string().min(1, validationMessages.NAME_REQUIRED),
      email: z.string().email().superRefine(
        async (value, ctx) => {
          // Check if email already exists
          const isEmailExists = await commonUtils.checkDataISExistByField('candidates', 'email', value);
          if (isEmailExists) {
            ctx.addIssue({
              code: 'custom',
              message: validationMessages.EMAIL_ALREADY_EXISTS,
            });
          }
        }
      ),
      source: z.string().min(1, validationMessages.SOURCE_REQUIRED),
    })
  }),
};