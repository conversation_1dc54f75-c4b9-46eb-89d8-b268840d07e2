import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import CandidateSummaryTab from "./CandidateSummaryTab";
import CandidateProfileTab from "./CandidateProfileTab";
import CandidateInterviewsTab from "./CandidateInterviewsTab";
import CandidateTimelineTab from "./CandidateTimelineTab";
import CandidateCvSummaryTab from "./CandidateCvSummaryTab";
import { Check, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface CandidateTabsProps {
  closeDrawer: () => void;
  candidateDetail: any;
  interviews: any[];
  interviewsLoading: boolean;
  interviewsError: string | null;
  setIsScheduleDrawerOpen: (open: boolean) => void;
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const toCamelCase = (str: string) =>
  str.replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/[_-]/g, ' ')
    .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());

const getResultBadge = (result: string) => {
  switch (result) {
    case "pass":
      return (
        <Badge className="bg-green-50 text-green-700 hover:bg-green-50">
          <Check className="w-3 h-3 mr-1" />
          Pass
        </Badge>
      );
    case "fail":
      return (
        <Badge className="bg-red-50 text-red-700 hover:bg-red-50">
          <X className="w-3 h-3 mr-1" />
          Reject
        </Badge>
      );
    default:
      return <Badge className="bg-gray-100 text-gray-700 hover:bg-gray-100">Pending</Badge>;
  }
};

const CandidateTabs: React.FC<CandidateTabsProps> = ({
  closeDrawer,
  candidateDetail,
  interviews,
  interviewsLoading,
  interviewsError,
  setIsScheduleDrawerOpen,
  activeTab,
  setActiveTab,
}) => {
  return (
    <div className="w-full md:w-2/3 pl-0 md:pl-6 mt-6 md:mt-0">
      <Tabs
        defaultValue="summary"
        className="w-full"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="w-full justify-start border-b rounded-none bg-transparent h-auto pb-0 mb-6 gap-4">
          <TabsTrigger
            value="profile"
            className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
          >
            Profile
          </TabsTrigger>
          <TabsTrigger
            value="interviews"
            className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
          >
            Interviews
          </TabsTrigger>
          <TabsTrigger
            value="timeline"
            className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
          >
            Timeline
          </TabsTrigger>
          <TabsTrigger
            value="summary"
            className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
          >
            Summary
          </TabsTrigger>
          <TabsTrigger
            value="score-summary"
            className="text-sm rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
          >
            Score Summary
          </TabsTrigger>
        </TabsList>
        <TabsContent value="summary" className="space-y-6 mt-0">
          <CandidateSummaryTab candidateDetail={candidateDetail} />
        </TabsContent>
        <TabsContent value="profile" className="space-y-6 mt-0">
          <CandidateProfileTab candidateDetail={candidateDetail} />
        </TabsContent>
        <TabsContent value="interviews" className="mt-0">
          <CandidateInterviewsTab
            closeDrawer={closeDrawer}
            candidateId={candidateDetail.id}
            interviews={interviews}
            interviewsLoading={interviewsLoading}
            interviewsError={interviewsError}
            setIsScheduleDrawerOpen={setIsScheduleDrawerOpen}
            toCamelCase={toCamelCase}
          />
        </TabsContent>
        <TabsContent value="timeline" className="mt-0">
          <CandidateTimelineTab
            candidateDetail={candidateDetail}
            getResultBadge={getResultBadge}
          />
        </TabsContent>
        <TabsContent value="score-summary" className="space-y-6 mt-0">
          <CandidateCvSummaryTab candidateDetail={candidateDetail} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CandidateTabs;