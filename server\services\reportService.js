import { supabase } from '../config/db.js';
import { logger } from '../utils/logger.js';

export const reportService = {
  async getRecruiterDashboard(recruiterId) {
    try {
      const today = new Date().toISOString();
      
      // Get active jobs count
      const { count: activeJobs, error: jobsError } = await supabase
        .from('job_posts')
        .select('*', { count: 'exact' })
        .eq('created_by', recruiterId)
        .eq('status', 'open')
        .eq('flag_deleted', false);

      if (jobsError) throw jobsError;

      // Get pending applications count
      const { count: pendingApplications, error: applicationsError } = await supabase
        .from('applications')
        .select('*', { count: 'exact' })
        .eq('recruiter_id', recruiterId)
        .eq('status', 'pending')
        .eq('flag_deleted', false);

      if (applicationsError) throw applicationsError;

      // Get upcoming interviews count
      const { count: upcomingInterviews, error: interviewsError } = await supabase
        .from('interviews')
        .select('*', { count: 'exact' })
        .eq('interviewer_id', recruiterId)
        .gte('scheduled_time', today)
        .eq('flag_deleted', false);

      if (interviewsError) throw interviewsError;

      return {
        activeJobs,
        pendingApplications,
        upcomingInterviews,
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('Failed to get recruiter dashboard:', error);
      throw error;
    }
  },

  async getTimeToHireReport(filters = {}) {
    try {
      const { startDate, endDate } = filters;
      let query = supabase.rpc('calculate_time_to_hire', {
        start_date: startDate ? new Date(startDate).toISOString() : null,
        end_date: endDate ? new Date(endDate).toISOString() : null
      });

      const { data: results, error } = await query;

      if (error) throw error;

      // Calculate summary statistics
      const summary = {
        totalDepartments: results.length,
        overallAverage: results.reduce((acc, curr) => acc + curr.average_time_to_hire, 0) / results.length
      };

      return {
        departments: results,
        summary
      };
    } catch (error) {
      logger.error('Failed to generate time to hire report:', error);
      throw error;
    }
  },

  async getPanelistFeedbackReport(filters = {}) {
    try {
      const { startDate, endDate, department } = filters;
      
      let query = supabase.rpc('get_panelist_feedback_stats', {
        start_date: startDate ? new Date(startDate).toISOString() : null,
        end_date: endDate ? new Date(endDate).toISOString() : null,
        dept: department
      });

      const { data: results, error } = await query;

      if (error) throw error;

      return {
        feedbackStats: results,
        summary: {
          totalInterviews: results.reduce((acc, curr) => acc + curr.total_interviews, 0),
          averageRating: results.reduce((acc, curr) => acc + curr.average_rating, 0) / results.length
        }
      };
    } catch (error) {
      logger.error('Failed to generate panelist feedback report:', error);
      throw error;
    }
  }
};