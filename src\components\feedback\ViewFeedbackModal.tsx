import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Star } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface Criterion {
  id: string;
  title: string;
  score: number;
  notes: string;
}

interface FeedbackData {
  overallRecommendation: string;
  criteria: Criterion[];
  overallComments: string;
  interviewer: string;
  interviewDate: string;
  overall_score: number;
  stage: string;
}

interface ViewFeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  feedback: FeedbackData | null;
}

const ViewFeedbackModal = ({ isOpen, onClose, feedback }: ViewFeedbackModalProps) => {
  if (!feedback) return null;

  const getRecommendationLabel = (recommendation: string): { label: string; color: string } => {
    const recommendations: Record<string, { label: string; color: string }> = {
      strong_hire: { label: "Strong Hire", color: "bg-green-500 text-white" },
      hire: { label: "Hire", color: "bg-emerald-500 text-white" },
      neutral: { label: "Neutral", color: "bg-amber-500 text-white" },
      no_hire: { label: "No Hire", color: "bg-red-500 text-white" },
      strong_no_hire: { label: "Strong No Hire", color: "bg-rose-600 text-white" },
    };

    return recommendations[recommendation] || { label: "Unknown", color: "bg-gray-500 text-white" };
  };

  const recommendation = getRecommendationLabel(feedback.overallRecommendation);

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-white p-0">
        <DialogHeader className="p-6 bg-[#9b87f5]/10 border-b">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <DialogTitle className="text-2xl font-semibold text-[#1A1F2C]">
                Interview Feedback
              </DialogTitle>
              <DialogDescription className="mt-1">
                Feedback submitted on {feedback.interviewDate}
              </DialogDescription>
            </div>
            <Badge className={`${recommendation.color} px-3 py-1 text-sm font-medium`}>
              {recommendation.label}
            </Badge>
          </div>
        </DialogHeader>

        <div className="p-6 space-y-6">
          {/* Overall Score Section */}
          <div className="flex items-center gap-4 mb-4">
            <div className="flex items-center">
              {[...Array(5)].map((_, index) => (
                <Star
                  key={index}
                  className={`h-6 w-6 ${
                    index < Math.round(feedback.overall_score)
                      ? "text-purple-500 fill-purple-500"
                      : "text-gray-300"
                  }`}
                />
              ))}
              <span className="ml-3 text-lg font-semibold text-purple-700">
                {feedback.overall_score}/5
              </span>
            </div>
            <span className="text-sm text-[#7E69AB] font-medium bg-[#F1F0FB] px-3 py-1 rounded-md">
              Overall Score
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-[#F1F0FB] p-4 rounded-md">
              <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Interviewer</h4>
              <p className="text-[#1A1F2C] font-medium">{feedback.interviewer}</p>
            </div>
            <div className="bg-[#F1F0FB] p-4 rounded-md">
              <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Stage</h4>
              <p className="text-[#1A1F2C] font-medium">{feedback.stage}</p>
            </div>
            <div className="bg-[#F1F0FB] p-4 rounded-md">
              <h4 className="text-sm font-medium text-[#7E69AB] mb-1">Date</h4>
              <p className="text-[#1A1F2C] font-medium">{feedback.interviewDate}</p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-[#1A1F2C] mb-4">Evaluation Criteria</h3>
            <div className="space-y-4">
              {feedback.criteria.map(criterion => (
                <div
                  key={criterion.id}
                  className="bg-white border border-gray-200 rounded-md p-4 shadow-sm"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-[#1A1F2C]">{criterion.title}</h4>
                    <div className="flex items-center">
                      {[...Array(5)].map((_, index) => (
                        <Star
                          key={index}
                          className={`h-4 w-4 ${
                            index < criterion.score
                              ? "text-amber-500 fill-amber-500"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                      <span className="ml-2 text-sm text-[#403E43]">{criterion.score}/5</span>
                    </div>
                  </div>
                  <p className="text-sm text-[#403E43]">{criterion.notes || "No notes provided"}</p>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-[#1A1F2C] mb-2">Overall Comments</h3>
            <div className="bg-white border border-gray-200 rounded-md p-4 shadow-sm">
              <p className="text-[#403E43]">
                {feedback.overallComments || "No overall comments provided"}
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewFeedbackModal;
