import jwt from 'jsonwebtoken';
import { logger } from './logger.js';

export const jwtUtils = {
  signToken(payload) {
    try {
      return jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
      });
    } catch (error) {
      logger.error('Token signing failed:', error);
      throw new Error('Failed to generate token');
    }
  },

  verifyToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      logger.error('Token verification failed:', error);
      throw new Error('Invalid token');
    }
  }
};