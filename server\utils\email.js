import nodemailer from 'nodemailer';
import handlebars from 'handlebars';
import fs from 'fs/promises';
import path from 'path';
import { logger } from './logger.js';

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
});

export const emailUtils = {
  async sendEmail(to, subject, template, context) {
    try {
      const templatePath = path.join(process.cwd(), 'templates', 'email', `${template}.hbs`);
      const templateContent = await fs.readFile(templatePath, 'utf-8');
      const compiledTemplate = handlebars.compile(templateContent);
      const html = compiledTemplate(context);

      await transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to,
        subject,
        html
      });

      logger.info(`Email sent successfully to ${to}`);
      return { success: true };
    } catch (error) {
      logger.error('Email sending failed:', error);
      throw new Error('Failed to send email');
    }
  }
};