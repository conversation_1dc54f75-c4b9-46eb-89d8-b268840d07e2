import express from 'express';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { candidateController } from '../controllers/candidateController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';
import { validateRequest } from '../middleware/validator.js';
import { candidateValidation } from '../validation/candidateValidation.js';
import upload from '../middleware/uploadMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/candidates
 * @desc    Get all candidates with pagination
 * @access  Public
 */
router.get('/',
  authenticate,
  authorize(['admin', 'recruiter']), 
  asyncHandler(candidateController.getCandidates)
);

/**
 * @route   GET /api/candidates/:id
 * @desc    Get candidate by ID
 * @access  Public
 */
router.get('/:id',
  authenticate,
  authorize(['admin', 'recruiter']), 
  async<PERSON>and<PERSON>(candidateController.getCandidateById)
);

/**
 * @route   POST /api/candidates
 * @desc    Create a new candidate
 * @access  Private/Admin
 */
router.post('/',
  authenticate,
  authorize(['admin', 'recruiter']),
  upload.single('cv'), // Handle resume file upload
  validateRequest(candidateValidation.createCandidate),
  asyncHandler(candidateController.createCandidate)
);

/**
 * @route   PUT /api/candidates/:id
 * @desc    Update candidate
 * @access  Private/Admin
 */
router.put('/:id',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(candidateController.updateCandidate)
);

/**
 * @route   PATCH /api/candidates/:id
 * @desc    Update candidate
 * @access  Private/Admin
 */
router.patch('/:id',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(candidateController.updateCandidate)
);

/**
 * @route   PATCH /api/candidates/:id/status
 * @desc    Update candidate status
 * @access  Private/Admin
 */
router.patch('/:id/status',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(candidateController.updateCandidateStatus)
);

/**
 * @route   DELETE /api/candidates/:id
 * @desc    Delete candidate
 * @access  Private/Admin
 */
router.delete('/:id',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(candidateController.deleteCandidate)
);

/**
 * @route   POST /api/candidates/:id/post-to-platform
 * @desc    Post candidate to external platforms
 * @access  Private/Admin
 */
router.post('/:id/post-to-platform',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(candidateController.postToPlatform)
);

export default router;