import { supabase } from '../config/db.js';
import { logger } from '../utils/logger.js';
import { emailUtils } from '../utils/emailUtils.js';

export const notificationService = {
  async getNotifications(userId, page = 1, limit = 10, filters = {}) {
    try {
      const start = (page - 1) * limit;
      const end = start + limit - 1;

      let query = supabase
        .from('notifications')
        .select(`
          *,
          creator:users!created_by(full_name, email)
        `, { count: 'exact' })
        .eq('user_id', userId)
        .eq('flag_deleted', false);

      // Apply any additional filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) query = query.eq(key, value);
      });

      // Add pagination and sorting
      const { data: notifications, error, count } = await query
        .order('created_at', { ascending: false })
        .range(start, end);

      if (error) throw error;

      return {
        notifications,
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to retrieve notifications:', error);
      throw error;
    }
  },

  async sendNotification(notificationData) {
    try {
      // Check if user exists
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('email, full_name')
        .eq('id', notificationData.user_id)
        .eq('flag_deleted', false)
        .single();

      if (userError) {
        if (userError.code === 'PGRST116') {
          return { success: false, message: 'User not found' };
        }
        throw userError;
      }

      // Create notification
      const { data: notification, error } = await supabase
        .from('notifications')
        .insert([{
          user_id: notificationData.user_id,
          type: notificationData.type,
          message: notificationData.message,
          created_by: notificationData.created_by,
          flag_active: true,
          flag_deleted: false
        }])
        .select(`
          *,
          creator:users!created_by(full_name, email)
        `)
        .single();

      if (error) throw error;

      // If notification type is email, send email
      if (notification.type === 'email') {
        try {
          await emailUtils.sendEmail({
            to: user.email,
            subject: 'New Notification',
            template: 'notification',
            data: {
              userName: user.full_name,
              message: notification.message
            }
          });
        } catch (emailError) {
          logger.error('Failed to send email notification:', emailError);
          // Continue execution even if email fails
        }
      }

      return { success: true, notification };
    } catch (error) {
      logger.error('Failed to send notification:', error);
      throw error;
    }
  }
};