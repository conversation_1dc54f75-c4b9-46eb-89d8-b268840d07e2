import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import { errorHandler } from "./middleware/errorHandler.js";
import { auditLogger } from "./middleware/auditLogger.js";
import { logger } from "./utils/logger.js";
import { supabase } from "./db/client.js";
import { initializeDatabase } from "./utils/database.js";
import routes from "./routes/index.js";

dotenv.config();

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Logging middleware
app.use(auditLogger);
await initializeDatabase();

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "ok",
    timestamp: new Date().toISOString(),
    database: "supabase",
  });
});

// Ping API for checking if server is running
app.get("/api/ping", (req, res) => {
  res.status(200).json({
    success: true,
    message: "Server is running!",
    timestamp: new Date().toISOString(),
    port: process.env.PORT || 5004,
  });
});

// Make supabase client available in requests
app.use((req, res, next) => {
  req.supabase = supabase;
  next();
});

// API Routes
routes(app);

// Error handling
app.use(errorHandler);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
  });
});

const PORT = process.env.PORT || 5004;
const HOST = '0.0.0.0';

const server = app.listen(PORT, HOST, () => {
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log(`🌐 Server accessible at http://${HOST}:${PORT}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`📡 Ping endpoint: http://localhost:${PORT}/api/ping`);
  console.log(`💚 Health check: http://localhost:${PORT}/health`);
  logger.info(`Server running on port ${PORT}`);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  logger.info("SIGTERM received. Shutting down gracefully");
  server.close(() => {
    logger.info("Process terminated");
  });
});

process.on("unhandledRejection", err => {
  logger.error("Unhandled Promise Rejection:", err);
});

process.on("uncaughtException", err => {
  logger.error("Uncaught Exception:", err);
  process.exit(1);
});
