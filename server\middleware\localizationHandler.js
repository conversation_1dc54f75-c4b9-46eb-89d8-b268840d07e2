import { logger } from '../utils/logger.js';
import { supabase } from '../config/db.js';

export const localizationHandler = async (req, res, next) => {
  try {
    if (req.user) {
      const { data: user, error } = await supabase
        .from('users')
        .select('language_preference')
        .eq('id', req.user.id)
        .single();

      if (error) throw error;
      req.language = user?.language_preference || 'en';
    } else {
      req.language = req.headers['accept-language']?.split(',')[0] || 'en';
    }
    
    next();
  } catch (error) {
    logger.error('Localization handling failed:', error);
    req.language = 'en';
    next();
  }
};