import { supabase } from '../config/db.js';
import { logger } from '../utils/logger.js';

const handleServiceError = (error, message) => {
  logger.error(`${message}:`, error);
  throw new Error(message);
};

export const resumeService = {
  async uploadResume(file, userId) {
    try {
      // Upload file to Supabase storage
      const { data: storedFile, error: storageError } = await supabase.storage
        .from('resumes')
        .upload(`${userId}/${file.originalname}`, file.buffer);

      if (storageError) throw storageError;

      // Create resume record in Supabase
      const { data: resume, error } = await supabase
        .from('resumes')
        .insert([{
          user_id: userId,
          file_path: storedFile.path,
          file_name: file.originalname,
          flag_active: true,
          flag_deleted: false
        }])
        .select(`
          *,
          candidate:users!user_id(full_name, email)
        `)
        .single();

      if (error) throw error;

      return { success: true, resume };
    } catch (error) {
      handleServiceError(error, 'Failed to upload resume');
    }
  },

  async getResumes(query) {
    try {
      const { page = 1, limit = 10, ...filters } = query;
      const start = (page - 1) * limit;
      const end = start + limit - 1;

      let dbQuery = supabase
        .from('resumes')
        .select(`
          *,
          candidate:users!user_id(full_name, email)
        `, { count: 'exact' })
        .eq('flag_deleted', false);

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) dbQuery = dbQuery.eq(key, value);
      });

      const { data: resumes, error, count } = await dbQuery
        .order('created_at', { ascending: false })
        .range(start, end);

      if (error) throw error;

      return {
        resumes,
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      handleServiceError(error, 'Failed to retrieve resumes');
    }
  },

  async getResumeById(id) {
    try {
      const { data: resume, error } = await supabase
        .from('resumes')
        .select(`
          *,
          candidate:users!user_id(full_name, email)
        `)
        .eq('id', id)
        .eq('flag_deleted', false)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Resume not found' };
        }
        throw error;
      }

      return { success: true, resume };
    } catch (error) {
      handleServiceError(error, 'Failed to retrieve resume');
    }
  },

  async deleteResume(id) {
    try {
      // Get resume details first
      const { data: resume, error: fetchError } = await supabase
        .from('resumes')
        .select('file_path')
        .eq('id', id)
        .eq('flag_deleted', false)
        .single();

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          return { success: false, message: 'Resume not found' };
        }
        throw fetchError;
      }

      // Delete file from Supabase storage
      const { error: storageError } = await supabase.storage
        .from('resumes')
        .remove([resume.file_path]);

      if (storageError) throw storageError;

      // Mark resume as deleted in database
      const { error: updateError } = await supabase
        .from('resumes')
        .update({ 
          flag_deleted: true,
          deleted_at: new Date().toISOString()
        })
        .eq('id', id);

      if (updateError) throw updateError;

      return { success: true };
    } catch (error) {
      handleServiceError(error, 'Failed to delete resume');
    }
  }
};