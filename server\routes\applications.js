import express from 'express';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { applicationController } from '../controllers/applicationController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/applications
 * @desc    Get all applications with pagination
 * @access  Private
 */
router.get('/',
  authenticate,
  asyncHandler(applicationController.getApplications)
);

/**
 * @route   GET /api/applications/:id
 * @desc    Get application by ID
 * @access  Private
 */
router.get('/:id',
  authenticate,
  asyncHandler(applicationController.getApplicationById)
);

/**
 * @route   POST /api/applications
 * @desc    Create a new application
 * @access  Private
 */
router.post('/',
  authenticate,
  asyncHandler(applicationController.createApplication)
);

/**
 * @route   PUT /api/applications/:id/status
 * @desc    Update application status
 * @access  Private
 */
router.put('/:id/status',
  authenticate,
  asyncHandler(applicationController.updateApplicationStatus)
);

export default router;