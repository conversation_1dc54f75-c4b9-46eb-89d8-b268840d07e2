
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, <PERSON><PERSON>Content } from '@/components/ui/tabs';

interface FeedbackTabsProps {
  children: React.ReactNode;
}

const FeedbackTabs = ({ children }: FeedbackTabsProps) => {
  return (
    <Tabs defaultValue="criteria" className="w-full">
      <TabsList className="mb-4 grid grid-cols-3 w-full max-w-md">
        <TabsTrigger value="criteria">Evaluation Criteria</TabsTrigger>
        <TabsTrigger value="comments">Comments</TabsTrigger>
        <TabsTrigger value="scorecard">Scorecard</TabsTrigger>
      </TabsList>
      {children}
    </Tabs>
  );
};

export default FeedbackTabs;
