import { validationResult } from 'express-validator';
import { responseHandler } from '../utils/responseHandler.js';
import { logger } from '../utils/logger.js';

export const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn('Validation failed:', errors.array());
    return responseHandler.badRequest(res, 'Validation failed', errors.array());
  }
  
  next();
};