import { supabase } from '../config/db.js';
import { logger } from '../utils/logger.js';

export const interviewerService = {
  async getInterviewers(query) {
    const { page = 1, limit = 10, ...filters } = query;
    const start = (page - 1) * limit;
    const end = start + limit - 1;

    try {
      let query = supabase
        .from('interviewers')
        .select('*, users!user_id(*)', { count: 'exact' })
        .eq('flag_deleted', false)
        .range(start, end);

      // Apply filters if any
      Object.entries(filters).forEach(([key, value]) => {
        if (value) query = query.eq(key, value);
      });

      const { data: interviewers, count, error } = await query;

      if (error) throw error;

      return {
        interviewers,
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to get interviewers:', error);
      throw error;
    }
  },

  async getInterviewerById(id) {
    try {
      const { data: interviewer, error } = await supabase
        .from('interviewers')
        .select('*, users!user_id(*)')
        .eq('id', id)
        .eq('flag_deleted', false)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Interviewer not found' };
        }
        throw error;
      }

      return { success: true, interviewer };
    } catch (error) {
      logger.error('Failed to get interviewer by id:', error);
      throw error;
    }
  },

  async createInterviewer(interviewerData, createdByUserId) {
    try {
      const { email, full_name: providedFullName } = interviewerData;

      // 1. Find existing user by email
      const { data: existingUser, error: findUserError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          full_name,
          role_id,
          role:roles!role_id(name)
        `)
        .eq('email', email)
        .eq('flag_deleted', false)
        .single();

      if (findUserError && findUserError.code !== 'PGRST116') {
        throw findUserError;
      }

      let targetUserId;
      let finalFullName;

      if (existingUser) {
        targetUserId = existingUser.id;
        finalFullName = existingUser.full_name;

        if (providedFullName && existingUser.full_name !== providedFullName) {
          const { error: updateFullNameError } = await supabase
            .from('users')
            .update({ full_name: providedFullName, updated_by: createdByUserId })
            .eq('id', existingUser.id);
          if (updateFullNameError) {
            logger.error(`Failed to update existing user's full_name:`, updateFullNameError);
            throw updateFullNameError;
          }
          finalFullName = providedFullName;
        }
      } else {
        return { success: false, message: 'User with this email does not exist. Please create a user account first.' };
      }

      const { data: existingInterviewer, error: existingInterviewerError } = await supabase
        .from('interviewers')
        .select('id')
        .eq('user_id', targetUserId)
        .single();

      if (existingInterviewerError && existingInterviewerError.code !== 'PGRST116') {
        throw existingInterviewerError;
      }
      if (existingInterviewer) {
        return { success: false, message: 'This user is already registered as an interviewer.' };
      }

      const dbData = {
        user_id: targetUserId,
        full_name: finalFullName,
        email: email,
        ...interviewerData,
        education_degree: interviewerData.education_degree,
        education_institution: interviewerData.education_institution,
        education_year: interviewerData.education_year,
        work_history_position: interviewerData.work_history_position,
        work_history_company: interviewerData.work_history_company,
        work_history_duration: interviewerData.work_history_duration,
        created_by: createdByUserId,
        flag_active: true
      };

      delete dbData.user_id;
      delete dbData.full_name;
      delete dbData.email;

      dbData.user_id = targetUserId;
      dbData.full_name = finalFullName;
      dbData.email = email;

      const { data: interviewer, error } = await supabase
        .from('interviewers')
        .insert([dbData])
        .select('*, users!user_id(*)')
        .single();

      if (error) throw error;

      return { success: true, interviewer };
    } catch (error) {
      logger.error('Failed to create interviewer:', error);
      throw error;
    }
  },

  async updateInterviewer(id, updateData) {
    try {
      if (updateData.email) {
        const { data: existingUser } = await supabase
          .from('users')
          .select('id')
          .eq('email', updateData.email)
          .neq('id', id)
          .single();

        if (existingUser) {
          return { success: false, message: 'Email already in use' };
        }
      }

      const dbData = {
        ...updateData,
        education_degree: updateData.education_degree,
        education_institution: updateData.education_institution,
        education_year: updateData.education_year,
        work_history_position: updateData.work_history_position,
        work_history_company: updateData.work_history_company,
        work_history_duration: updateData.work_history_duration,
        updated_at: new Date().toISOString()
      };

      const { data: interviewer, error } = await supabase
        .from('interviewers')
        .update(dbData)
        .eq('id', id)
        .eq('flag_deleted', false)
        .select('*, users!user_id(*)')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Interviewer not found', status: 404 };
        }
        throw error;
      }

      return { success: true, interviewer };
    } catch (error) {
      logger.error('Failed to update interviewer:', error);
      throw error;
    }
  },

  async deleteInterviewer(id) {
    try {
      const { data: interviewer, error } = await supabase
        .from('interviewers')
        .update({
          flag_deleted: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('flag_deleted', false)
        .select('*, users!user_id(*)')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Interviewer not found', status: 404 };
        }
        throw error;
      }

      return { success: true };
    } catch (error) {
      logger.error('Failed to delete interviewer:', error);
      throw error;
    }
  },

  async updateAvailability(id, availabilityData) {
    try {
      const { data: interviewer, error } = await supabase
        .from('interviewers')
        .update({
          is_available: availabilityData.is_available,
          preferred_time_slots: availabilityData.preferred_time_slots,
          max_interviews_per_day: availabilityData.max_interviews_per_day,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('flag_deleted', false)
        .select('*, users!user_id(*)')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Interviewer not found' };
        }
        throw error;
      }

      return { success: true, interviewer };
    } catch (error) {
      logger.error('Failed to update interviewer availability:', error);
      throw error;
    }
  }
}; 