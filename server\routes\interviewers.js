import express from 'express';
import { asyncHandler } from '../middleware/asyncHandler.js';
import { interviewer<PERSON>ontroller } from '../controllers/interviewerController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/interviewers
 * @desc    Get all interviewers with pagination
 * @access  Private/Admin, HR, Recruiter
 */
router.get('/', 
  authenticate, 
  authorize(['admin', 'hr', 'recruiter']), 
  async<PERSON>andler(interviewerController.getInterviewers)
);

/**
 * @route   GET /api/interviewers/:id
 * @desc    Get interviewer by ID
 * @access  Private/Admin, HR, Recruiter
 */
router.get('/:id', 
  authenticate, 
  authorize(['admin', 'hr', 'recruiter']), 
  asyncHandler(interviewerController.getInterviewerById)
);

/**
 * @route   POST /api/interviewers
 * @desc    Create a new interviewer
 * @access  Private/Admin, HR, Recruiter
 */
router.post('/', 
  authenticate, 
  authorize(['admin', 'hr', 'recruiter']), 
  async<PERSON>and<PERSON>(interviewerController.createInterviewer)
);

/**
 * @route   PUT /api/interviewers/:id
 * @desc    Update interviewer
 * @access  Private/Admin, HR, Recruiter
 */
router.put('/:id', 
  authenticate, 
  authorize(['admin', 'hr', 'recruiter']), 
  asyncHandler(interviewerController.updateInterviewer)
);

/**
 * @route   DELETE /api/interviewers/:id
 * @desc    Delete interviewer
 * @access  Private/Admin, HR, Recruiter
 */
router.delete('/:id', 
  authenticate, 
  authorize(['admin', 'hr', 'recruiter']), 
  asyncHandler(interviewerController.deleteInterviewer)
);

/**
 * @route   PATCH /api/interviewers/:id/availability
 * @desc    Update interviewer availability
 * @access  Private/Admin, HR, Recruiter, Interviewer
 */
router.patch('/:id/availability', 
  authenticate, 
  authorize(['admin', 'hr', 'recruiter', 'interviewer']), 
  asyncHandler(interviewerController.updateAvailability)
);

export default router; 