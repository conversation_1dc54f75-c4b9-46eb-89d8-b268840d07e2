import { supabase } from '../config/db.js';
import { logger } from '../utils/logger.js';

export const applicationService = {
  async getApplications(query) {
    try {
      const { page = 1, limit = 10, ...filters } = query;
      const start = (page - 1) * limit;
      const end = start + limit - 1;

      let supabaseQuery = supabase
        .from('applications')
        .select(`
          *,
          user:users(full_name, email),
          resume:resumes(*),
          job:job_posts(title)
        `, { count: 'exact' })
        .eq('flag_deleted', false)
        .range(start, end)
        .order('created_at', { ascending: false });

      // Apply any additional filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) supabaseQuery = supabaseQuery.eq(key, value);
      });

      const { data: applications, count, error } = await supabaseQuery;

      if (error) throw error;

      return {
        applications,
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to get applications:', error);
      throw error;
    }
  },

  async getApplicationById(id) {
    try {
      const { data: application, error } = await supabase
        .from('applications')
        .select(`
          *,
          user:users(full_name, email),
          resume:resumes(*),
          job:job_posts(title, description)
        `)
        .eq('id', id)
        .eq('flag_deleted', false)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Application not found' };
        }
        throw error;
      }

      return { success: true, application };
    } catch (error) {
      logger.error('Failed to get application:', error);
      throw error;
    }
  },

  async createApplication(applicationData) {
    try {
      // Check if user already applied for this job
      const { data: existingApplication, error: checkError } = await supabase
        .from('applications')
        .select('id')
        .eq('user_id', applicationData.user_id)
        .eq('job_post_id', applicationData.job_post_id)
        .eq('flag_deleted', false)
        .single();

      if (checkError && checkError.code !== 'PGRST116') throw checkError;

      if (existingApplication) {
        return { success: false, message: 'You have already applied for this job' };
      }

      const { data: application, error } = await supabase
        .from('applications')
        .insert([{
          ...applicationData,
          status: 'pending',
          flag_active: true,
          flag_deleted: false
        }])
        .select(`
          *,
          user:users(full_name, email),
          job:job_posts(title)
        `)
        .single();

      if (error) throw error;

      return { success: true, application };
    } catch (error) {
      logger.error('Failed to create application:', error);
      throw error;
    }
  },

  async updateApplicationStatus(id, status, userId) {
    try {
      const { data: application, error } = await supabase
        .from('applications')
        .update({
          status,
          updated_at: new Date().toISOString(),
          updated_by: userId
        })
        .eq('id', id)
        .eq('flag_deleted', false)
        .select(`
          *,
          user:users(full_name, email),
          job:job_posts(title)
        `)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Application not found', status: 404 };
        }
        throw error;
      }

      return { success: true, application };
    } catch (error) {
      logger.error('Failed to update application status:', error);
      throw error;
    }
  }
};