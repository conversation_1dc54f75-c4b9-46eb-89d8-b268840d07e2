import nodemailer from 'nodemailer';
import handlebars from 'handlebars';
import fs from 'fs/promises';
import path from 'path';
import { logger } from './logger.js';
import { supabase } from '../config/db.js';

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: false,
  requireTLS: true,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
});

// Register a Handlebars helper to format ISO date strings to IST
handlebars.registerHelper('formatIST', function(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  // Convert to IST (UTC+5:30)
  const istOffset = 5.5 * 60; // in minutes
  const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
  const istTime = new Date(utc + (istOffset * 60000));
  return istTime.toLocaleString('en-IN', { dateStyle: 'medium', timeStyle: 'short', hour12: true, timeZone: 'Asia/Kolkata' });
});

export const emailUtils = {
  async sendEmail({ to, subject, body, template, data = {} }) {
    try {
      let htmlContent = body;

      if (template) {
        const templatePath = path.join(process.cwd(), 'templates', 'emails', `${template}.hbs`);
        const templateContent = await fs.readFile(templatePath, 'utf-8');
        const compiledTemplate = handlebars.compile(templateContent);
        htmlContent = compiledTemplate(data);
      }

      const mailOptions = {
        from: process.env.EMAIL_FROM,
        to,
        subject,
        html: htmlContent
      };

      const info = await transporter.sendMail(mailOptions);
      logger.info('Email sent successfully:', info.messageId);

      return {
        success: true,
        messageId: info.messageId
      };
    } catch (error) {
      logger.error('Email sending failed:', error);
      throw new Error('Failed to send email');
    }
  },

  /**
   * Send available slots email to candidate
   * @param {Object} candidate - Candidate object
   * @param {Object} details - Additional details (e.g., slots)
   */
  async sendAvailableSlotsEmail(candidate, details) {
    const subject = 'Initial Screening: Select Your Available Slot';
    const template = 'available-slots';
    const data = {
      candidateName: candidate?.full_name || candidate?.name || 'Candidate',
      slots: details?.slots || [],
      candidate,
      ...details
    };
    return this.sendEmail({
      to: candidate?.email,
      subject,
      template,
      data
    });
  },

  /**
   * Send rejection email to candidate
   * @param {Object} candidate - Candidate object
   * @param {Object} details - Additional details
   */
  async sendRejectionEmail(candidate, details) {
    try {
      // Get the job title from the database using the candidate's job_id
      let jobTitle = 'the position';
      if (candidate?.job_id) {
        const { data: job, error } = await supabase
          .from('job_posts')
          .select('title')
          .eq('id', candidate.job_id)
          .eq('flag_deleted', false)
          .single();
        if (!error && job) {
          jobTitle = job.title;
        }
      }
      // Get recruiter info from users table using candidate.created_by
      let recruiterName = 'Recruiter Team';
      let recruiterEmail = '';
      let recruiterPhone = '1111111111';
      let recruiterTitle = 'Recruiter Team';
      const recruiterId = candidate?.created_by;
      if (recruiterId) {
        const { data: recruiter, error: recruiterError } = await supabase
          .from('users')
          .select('full_name, email, phone')
          .eq('id', recruiterId)
          .eq('flag_deleted', false)
          .single();
        if (!recruiterError && recruiter) {
          recruiterName = recruiter.full_name || recruiterName;
          recruiterEmail = recruiter.email || recruiterEmail;
          recruiterPhone = recruiter.phone || recruiterPhone;
        }
      }
      const subject = `Regarding Your Application for ${jobTitle}`;
      const template = 'rejection';
      const data = {
        candidateName: candidate?.full_name || candidate?.name || 'Candidate',
        jobTitle: jobTitle,
        recruiterName,
        recruiterEmail,
        recruiterPhone,
        recruiterTitle,
        candidate,
        ...details
      };
      return this.sendEmail({
        to: candidate?.email,
        subject,
        template,
        data
      });
    } catch (error) {
      logger.error('Failed to send rejection email:', error);
      throw new Error('Failed to send rejection email');
    }
  },

  /**
   * Send acceptance email to candidate
   * @param {Object} candidate - Candidate object
   */
  async sendAcceptanceEmail(candidate, details = {}) {
    try {
      // Get the job title from the database using the candidate's job_id
      let jobTitle = 'the position';
      if (candidate?.job_id) {
        const { data: job, error } = await supabase
          .from('job_posts')
          .select('title')
          .eq('id', candidate.job_id)
          .eq('flag_deleted', false)
          .single();
        if (!error && job) {
          jobTitle = job.title;
        }
      }
      // Get recruiter info from users table
      let recruiterName = 'Recruiter Team';
      let recruiterEmail = '';
      let recruiterPhone = '1111111111';
      let recruiterTitle = 'Recruiter Team';
      const recruiterId = details?.recruiter_id || candidate?.created_by;
      if (recruiterId) {
        const { data: recruiter, error: recruiterError } = await supabase
          .from('users')
          .select('full_name, email, phone')
          .eq('id', recruiterId)
          .eq('flag_deleted', false)
          .single();
        if (!recruiterError && recruiter) {
          recruiterName = recruiter.full_name || recruiterName;
          recruiterEmail = recruiter.email || recruiterEmail;
          recruiterPhone = recruiter.phone || recruiterPhone;
        }
      }
      const subject = `Your Application for ${jobTitle} – Next Steps`;
      const template = 'acceptance';
      const data = {
        candidateName: candidate?.full_name || candidate?.name || 'Candidate',
        jobTitle: jobTitle,
        recruiterName,
        recruiterEmail,
        recruiterPhone,
        recruiterTitle,
        candidate,
        ...details
      };
      return this.sendEmail({
        to: candidate?.email,
        subject,
        template,
        data
      });
    } catch (error) {
      logger.error('Failed to send acceptance email:', error);
      throw new Error('Failed to send acceptance email');
    }
  },

  async sendInterviewInvite({ to, meetLink, interview, recruiterName }) {
    // Fetch job title if available
    let jobTitle = '';
    if (interview.job_id) {
      const { data: job, error } = await supabase
        .from('job_posts')
        .select('title')
        .eq('id', interview.job_id)
        .single();
      if (!error && job) {
        jobTitle = job.title;
      }
    }
    // Fetch recruiter details for signature
    let recruiterEmail = '';
    let recruiterPhone = '';
    let recruiterTitle = 'Recruiter Team';
    if (interview.created_by) {
      const { data: recruiter, error: recruiterError } = await supabase
        .from('users')
        .select('full_name, email, phone')
        .eq('id', interview.created_by)
        .single();
      if (!recruiterError && recruiter) {
        recruiterEmail = recruiter.email || '';
        recruiterPhone = recruiter.phone || '';
        recruiterTitle = recruiter.title || recruiterTitle;
      }
    }
    const subject = `Your Interview Details: ${interview.type || 'Interview'} - ${interview.stage || ''}`;
    const template = 'slot-confirmation';
    const data = {
      candidateName: interview.candidate?.name || 'Candidate',
      interviewerName: interview.interviewer?.full_name || 'Interviewer',
      recruiterName,
      recruiterEmail,
      recruiterPhone,
      recruiterTitle,
      meetLink,
      jobTitle,
      type: interview.type,
      stage: interview.stage,
      scheduled_date: interview.scheduled_date,
      scheduled_time: interview.scheduled_time,
      special_requirements: interview.special_requirements,
      interview,
    };
    return this.sendEmail({
      to,
      subject,
      template,
      data
    });
  }
};