import { supabase } from "../config/db.js";
import { emailUtils } from "../utils/emailUtils.js";
import { logger } from "../utils/logger.js";
import { messages } from "../utils/messages.utils.js";
import { googleService } from "../services/googleService.js";

const handleServiceError = (error, message) => {
  logger.error(`${message}:`, error);
  throw new Error(message);
};

export const interviewService = {
  async getInterviews(filters = {}) {
    try {
      const { status, interviewer_id } = filters;
      let query = supabase
        .from("interviews")
        .select(
          `*,
          candidate:candidates(
            *,
            job:job_posts(id, title, description, department)
          ),
          interviewer:interviewers(id, full_name, email)
        `,
          { count: "exact" }
        )
        .eq("flag_deleted", false)
        .order("scheduled_time", { ascending: false });

      if (status) {
        query = query.eq("status", status);
      }
      if (interviewer_id) {
        query = query.eq("interviewer_id", interviewer_id);
      }

      const { data: interviews, error, count } = await query;

      if (error) throw error;

      return {
        success: true,
        message: messages.INTERVIEWS_RETRIEVED,
        interviews,
        total: count,
      };
    } catch (error) {
      handleServiceError(error, "Failed to get interviews");
    }
  },

  async generateGoogleMeetLink(details) {
    // In production, integrate with Google Calendar API here
    return `https://meet.google.com/mock-link-${Date.now()}`;
  },

  async scheduleInterview({ interview_id, supabase }) {
    try {
      // Fetch interview and related details
      const { data: interview, error: interviewError } = await supabase
        .from("interviews")
        .select(
          `id, type, stage, video_link, special_requirements, scheduled_date, scheduled_time, candidate:candidate_id(id, name, email, can_status), interviewer:interviewer_id(id, full_name, email), created_by`
        )
        .eq("id", interview_id)
        .single();
      if (interviewError || !interview) {
        return { success: false, message: messages.INTERVIEW_RETRIEVE_FAILED };
      }
      // Always update candidate status to 'interview'
      await supabase
        .from("candidates")
        .update({ can_status: "interview" })
        .eq("id", interview.candidate.id);
      // Fetch recruiter (created_by)
      const { data: recruiter, error: recruiterError } = await supabase
        .from("users")
        .select("id, full_name, email")
        .eq("id", interview.created_by)
        .single();
      if (recruiterError || !recruiter) {
        return { success: false, message: messages.INTERVIEW_RETRIEVE_FAILED };
      }
      // Send emails
      await emailUtils.sendInterviewInvite({
        to: interview.candidate.email,
        meetLink: interview.video_link,
        interview,
        recruiterName: recruiter.full_name,
      });
      await emailUtils.sendInterviewInvite({
        to: interview.interviewer.email,
        meetLink: interview.video_link,
        interview,
        recruiterName: recruiter.full_name,
      });
      await emailUtils.sendInterviewInvite({
        to: recruiter.email,
        meetLink: interview.video_link,
        interview,
        recruiterName: recruiter.full_name,
      });
      return { success: true, message: messages.INTERVIEW_SCHEDULED, interview };
    } catch (error) {
      return { success: false, message: error.message || messages.INTERVIEW_SCHEDULE_FAILED };
    }
  },

  async rescheduleInterview(id, newTime) {
    try {
      const { data: interview, error } = await supabase
        .from("interviews")
        .update({
          scheduled_time: newTime,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("flag_deleted", false)
        .select(
          `
          *,
          application:applications(*),
          interviewer:users!interviewer_id(id, full_name, email)
        `
        )
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return { success: false, message: messages.INTERVIEW_RETRIEVE_FAILED, status: 404 };
        }
        throw error;
      }

      // Send calendar update
      await this.sendCalendarInvite({
        ...interview,
        interviewId: interview.id,
        scheduled_time: newTime,
      });

      return { success: true, message: messages.INTERVIEW_RESCHEDULED, interview };
    } catch (error) {
      handleServiceError(error, "Failed to reschedule interview");
    }
  },

  async getInterviewById(id) {
    try {
      const { data: interview, error } = await supabase
        .from("interviews")
        .select(
          `
        *,
        candidate:candidates(
          *,
          job:job_posts(id, title, description, department)
        ),
        interviewer:interviewers(id, full_name, email)
      `
        )
        .eq("id", id)
        .eq("flag_deleted", false)
        .single(); // ensures you only get one result

      if (error) throw error;

      return {
        success: true,
        message: "Interview retrieved successfully",
        interview,
      };
    } catch (error) {
      handleServiceError(error, "Failed to get interview by ID");
    }
  },

  async updateInterviewFeedback(id, feedback) {
    try {
      const { data: interview, error } = await supabase
        .from("interviews")
        .update({
          feedback,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("flag_deleted", false)
        .select()
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return {
            success: false,
            message: messages.INTERVIEW_FEEDBACK_UPDATE_FAILED,
            status: 404,
          };
        }
        throw error;
      }

      return { success: true, message: messages.INTERVIEW_FEEDBACK_UPDATED, interview };
    } catch (error) {
      handleServiceError(error, "Failed to update interview feedback");
    }
  },

  async deleteInterview(id) {
    try {
      const { data: interview, error } = await supabase
        .from("interviews")
        .update({
          flag_deleted: true,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return { success: false, message: messages.INTERVIEW_DELETE_FAILED, status: 404 };
        }
        throw error;
      }

      return { success: true, message: messages.INTERVIEW_DELETED };
    } catch (error) {
      handleServiceError(error, "Failed to delete interview");
    }
  },

  async sendCalendarInvite(details) {
    // Implementation of calendar invite sending would go here
    // This could use the calendarUtils to send invites via Google Calendar or Outlook
    try {
      await emailUtils.sendInterviewInvite(details);
      return { success: true, message: messages.GOOGLE_CALENDAR_EVENT_SUCCESS };
    } catch (error) {
      handleServiceError(error, "Failed to send calendar invite");
    }
  },

  async approveCandidate(candidateId, details) {
    try {
      // Update candidate status to 'screening' or 'accepted'
      const { data: candidate, error } = await supabase
        .from("candidates")
        .update({ can_status: "screening" })
        .eq("id", candidateId)
        .select()
        .single();
      if (error) throw error;

      // Send acceptance email (no slot logic)
      await emailUtils.sendAcceptanceEmail(candidate);
      return { success: true, message: messages.INTERVIEW_APPROVE_SUCCESS };
    } catch (error) {
      handleServiceError(error, "Failed to approve candidate");
    }
  },

  async declineCandidate(candidateId, details) {
    try {
      logger.info("Declining candidate with id:", candidateId);

      const { data: candidate, error } = await supabase
        .from("candidates")
        .update({ can_status: "rejected" })
        .eq("id", candidateId)
        .select()
        .single();

      if (error) {
        logger.error("Supabase update error:", error);
        throw error;
      }
      if (!candidate) {
        logger.error("No candidate found with id:", candidateId);
        throw new Error("Candidate not found");
      }

      logger.info("Candidate found, sending rejection email to:", candidate.email);

      await emailUtils.sendRejectionEmail(candidate, details);
      logger.info("Rejection email sent successfully to:", candidate.email);
      return { success: true, message: messages.INTERVIEW_DECLINE_SUCCESS };
    } catch (error) {
      logger.error("Decline candidate error:", error);
      handleServiceError(error, "Failed to decline candidate");
    }
  },

  async confirmSlot(candidateId, recruiterId, slot) {
    try {
      // Fetch candidate and recruiter info
      const { data: candidate, error: candidateError } = await supabase
        .from("candidates")
        .select("id, name, email")
        .eq("id", candidateId)
        .eq("flag_deleted", false)
        .single();
      if (candidateError || !candidate) throw new Error("Candidate not found");

      const { data: recruiter, error: recruiterError } = await supabase
        .from("users")
        .select("id, full_name, email, google_credentials")
        .eq("id", recruiterId)
        .single();
      if (recruiterError || !recruiter) throw new Error("Recruiter not found");

      // Parse Google credentials
      let creds;
      try {
        creds =
          typeof recruiter.google_credentials === "string"
            ? JSON.parse(recruiter.google_credentials)
            : recruiter.google_credentials;
      } catch (e) {
        throw new Error("Invalid Google credentials format.");
      }
      const { access_token, refresh_token } = creds;
      if (!access_token) throw new Error("No access token found in Google credentials.");

      // Create OAuth2 client
      const { google } = await import("googleapis");
      const oauth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);
      oauth2Client.setCredentials({ access_token, refresh_token });
      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      // Create the event
      const event = {
        summary: `Interview with ${candidate.name}`,
        description: "Interview scheduled via AI Recruiter",
        start: { dateTime: slot },
        end: { dateTime: new Date(new Date(slot).getTime() + 30 * 60000).toISOString() },
        attendees: [{ email: candidate.email }, { email: recruiter.email }],
        conferenceData: {
          createRequest: { requestId: `meet-${Date.now()}` },
        },
      };
      const { data: eventData } = await calendar.events.insert({
        calendarId: "primary",
        resource: event,
        conferenceDataVersion: 1,
      });
      const meetLink = eventData.conferenceData?.entryPoints?.find(
        e => e.entryPointType === "video"
      )?.uri;

      // Send confirmation emails
      const confirmationDetails = {
        candidateName: candidate.name,
        recruiterName: recruiter.full_name,
        slot: slot,
        meetLink: meetLink || "Google Meet link will be sent soon.",
      };
      await emailUtils.sendEmail({
        to: candidate.email,
        subject: "Interview Slot Confirmed",
        template: "slot-confirmation",
        data: confirmationDetails,
      });
      await emailUtils.sendEmail({
        to: recruiter.email,
        subject: "Interview Slot Confirmed",
        template: "slot-confirmation",
        data: confirmationDetails,
      });
      return { success: true, message: messages.GOOGLE_CALENDAR_EVENT_SUCCESS };
    } catch (error) {
      logger.error("Failed to confirm slot:", error);
      return { success: false, message: error.message };
    }
  },

  async sendInterviewEmails({
    recruiterEmail,
    interviewerEmail,
    candidateEmail,
    meetLink,
    ...rest
  }) {
    // Use your emailUtils to send emails to all three
    try {
      if (recruiterEmail)
        await emailUtils.sendInterviewInvite({ to: recruiterEmail, meetLink, ...rest });
      if (interviewerEmail)
        await emailUtils.sendInterviewInvite({ to: interviewerEmail, meetLink, ...rest });
      if (candidateEmail)
        await emailUtils.sendInterviewInvite({ to: candidateEmail, meetLink, ...rest });
      return { success: true };
    } catch (error) {
      logger.error("Failed to send interview emails:", error);
      return { success: false, message: error.message };
    }
  },

  async generateMeetLinkService({
    user_id,
    candidate_id,
    interviewer_id,
    scheduled_time,
    duration,
    type,
    stage,
    instructions,
    supabase,
  }) {
    try {
      if (
        !user_id ||
        !candidate_id ||
        !interviewer_id ||
        !scheduled_time ||
        !type ||
        !stage ||
        !duration
      ) {
        return { success: false, message: messages.INTERVIEW_SCHEDULE_FAILED };
      }
      // Look up candidate email
      console.log("[DEBUG] Looking up candidate with id:", candidate_id);
      const { data: candidate, error: candError } = await supabase
        .from("candidates")
        .select("id, email, name, can_status")
        .eq("id", candidate_id)
        .eq("flag_deleted", false)
        .single();
      console.log("[DEBUG] Candidate lookup result:", candidate, candError);
      if (candError || !candidate) {
        return { success: false, message: messages.CANDIDATE_NOT_FOUND };
      }
      // Look up interviewer email
      const { data: interviewer, error: intError } = await supabase
        .from("interviewers")
        .select("id, email, full_name")
        .eq("id", interviewer_id)
        .single();
      if (intError || !interviewer) {
        return { success: false, message: messages.INTERVIEW_RETRIEVE_FAILED };
      }
      const meetLink = await googleService.createGoogleMeetEvent({
        recruiterId: user_id,
        candidateEmail: candidate.email,
        interviewerEmail: interviewer.email,
        scheduledTime: scheduled_time,
        duration,
        summary: `Interview (${type}, ${stage})`,
        description: instructions || "",
      });
      // Insert interview into DB
      const { data: interview, error: insertError } = await supabase
        .from("interviews")
        .insert({
          candidate_id,
          interviewer_id,
          created_by: user_id,
          type,
          stage,
          duration,
          video_link: meetLink,
          special_requirements: instructions,
          flag_active: true,
          flag_deleted: false,
        })
        .select(
          `id, type, stage, duration, video_link, special_requirements, candidate:candidate_id(id, name, email, can_status), interviewer:interviewer_id(id, full_name, email)`
        ) // fetch joined data
        .single();
      if (insertError || !interview) {
        console.log("[DEBUG] Interview insert error:", insertError, interview);
        return { success: false, message: messages.INTERVIEW_SCHEDULE_FAILED };
      }
      return {
        success: true,
        message: messages.GOOGLE_CALENDAR_EVENT_SUCCESS,
        interview_id: interview.id,
        interview,
        meetLink,
        type,
        stage,
        interviewer_email: interviewer.email,
        candidate_email: candidate.email,
        duration,
        special_requirements: interview.special_requirements || "",
      };
    } catch (error) {
      return { success: false, message: error.message || messages.GOOGLE_CALENDAR_EVENT_FAILED };
    }
  },

  async updateScheduleDetails(
    interviewId,
    { scheduled_date, scheduled_time, video_link, supabase }
  ) {
    try {
      const { data: interview, error } = await supabase
        .from("interviews")
        .update({
          scheduled_date,
          scheduled_time,
          video_link,
          updated_at: new Date().toISOString(),
        })
        .eq("id", interviewId)
        .eq("flag_deleted", false)
        .select(
          `id, type, stage, video_link, special_requirements, scheduled_date, scheduled_time, candidate:candidate_id(id, name, email, can_status), interviewer:interviewer_id(id, full_name, email)`
        )
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return { success: false, message: messages.INTERVIEW_RETRIEVE_FAILED, status: 404 };
        }
        throw error;
      }

      if (!interview) {
        return { success: false, message: messages.INTERVIEW_RETRIEVE_FAILED, status: 404 };
      }

      return {
        success: true,
        message: messages.INTERVIEW_RESCHEDULED,
        interview,
      };
    } catch (error) {
      logger.error("Failed to update schedule details:", error);
      return { success: false, message: error.message || messages.INTERVIEW_RESCHEDULE_FAILED };
    }
  },

  async getInterviewByCandidateId(candidateId) {
    try {
      const { data: interviews, error } = await supabase
        .from("interviews")
        .select(
          `id, type, stage, video_link, special_requirements, scheduled_date, scheduled_time, candidate:candidate_id(id, name, email, can_status), interviewer:interviewer_id(id, full_name, email)`
        )
        .eq("candidate_id", candidateId)
        .eq("flag_deleted", false);
      if (error) {
        if (error.code === "PGRST116") {
          return { success: false, message: messages.INTERVIEW_RETRIEVE_FAILED, status: 404 };
        }
        throw error;
      }
      if (!interviews || interviews.length === 0) {
        return { success: false, message: messages.INTERVIEW_RETRIEVE_FAILED, status: 404 };
      }
      return { success: true, message: messages.INTERVIEW_RETRIEVED, interviews };
    } catch (error) {
      return { success: false, message: error.message || messages.INTERVIEW_RETRIEVE_FAILED };
    }
  },
};
