import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import {
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  Video,
  Users,
  Mail,
  ChevronRight,
  FileText,
  Search,
  X,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { toast } from "@/components/ui/sonner";
import { Sheet, SheetContent, SheetTitle, SheetClose } from "@/components/ui/sheet";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";

import InterviewerAvailability from "@/components/interviews/InterviewerAvailability";
import CandidateSummary from "@/components/interviews/CandidateSummary";
import InterviewSummary from "@/components/interviews/InterviewSummary";
import { useCandidates } from "@/hooks/useCandidates";
import { useInterviewers } from "@/hooks/useInterviewers";
import { InterviewStage, useInterviews } from "@/hooks/useInterviews";

interface Candidate {
  id: string;
  name: string;
  photo: string;
  role: string;
  stage: string;
  email: string;
}

const formSchema = z.object({
  candidateId: z.string({
    required_error: "Please select a candidate",
  }),
  interviewType: z.string(),
  interviewStage: z.string(),
  interviewers: z.array(z.string()).min(1, "Select at least one interviewer"),
  duration: z.string(),
  date: z.date().optional(),
  time: z.string().optional(),
  location: z.string().optional(),
  videoLink: z.string().optional(),
  selfScheduling: z.boolean().default(false),
  availableFrom: z.date().optional(),
  availableTo: z.date().optional(),
  isRescheduled: z.boolean().default(false),
  rescheduleNote: z.string().optional(),
  sendEmail: z.boolean().default(true),
  instructions: z.string().optional(),
});

interface ScheduleInterviewProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  defaultCandidate?: {
    id: string;
    name: string;
    email: string;
    phone: string;
    role: string;
    stage: string;
    photo: string;
  };
}

const ScheduleInterview = ({ isOpen, onOpenChange, defaultCandidate }: ScheduleInterviewProps) => {
  const [step, setStep] = useState(defaultCandidate ? 1 : 0);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(
    defaultCandidate
      ? {
          id: defaultCandidate.id,
          name: defaultCandidate.name,
          photo: defaultCandidate.photo,
          role: defaultCandidate.role,
          stage: defaultCandidate.stage,
          email: defaultCandidate.email,
        }
      : null
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [isCandidatePopoverOpen, setIsCandidatePopoverOpen] = useState(false);
  const candidatesHook = useCandidates();
  const [selectedCandidateDetail, setSelectedCandidateDetail] = useState<any>(null);
  const { interviewers: allInterviewers, loading: interviewersLoading } = useInterviewers();
  const {
    loading: interviewLoading,
    error: interviewError,
    generatedMeetLink,
    generatedInterviewId,
    generateMeetLink,
    saveScheduleDetails,
    scheduleInterview,
    scheduleSelfScheduling,
    reset,
    debugAuth,
  } = useInterviews();

  const [candidates, setCandidates] = useState<any[]>([]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      interviewType: "video",
      interviewStage: InterviewStage.Technical,
      interviewers: [],
      duration: "30",
      selfScheduling: false,
      isRescheduled: false,
      sendEmail: true,
    },
  });

  const selfScheduling = form.watch("selfScheduling");
  const interviewType = form.watch("interviewType");
  const isRescheduled = form.watch("isRescheduled");
  const candidateId = form.watch("candidateId");

  // Update selected candidate when candidateId changes
  useEffect(() => {
    if (candidateId) {
      const candidate = candidates.find(c => c.id === candidateId);
      if (candidate) {
        setSelectedCandidate(candidate);
      }
    }
  }, [candidateId, candidates]);

  // Update useEffect to set the candidate ID in the form when defaultCandidate is provided
  useEffect(() => {
    if (defaultCandidate) {
      form.setValue("candidateId", defaultCandidate.id);
    }
  }, [defaultCandidate]);

  // Fetch candidate details when candidateId changes
  useEffect(() => {
    const candidateId = form.getValues("candidateId");
    if (candidateId) {
      candidatesHook.getCandidateById(candidateId).then(data => {
        setSelectedCandidateDetail(data);
      });
    } else {
      setSelectedCandidateDetail(null);
    }
  }, [form.watch("candidateId")]);

  // Auto-fill video link when meet link is generated
  useEffect(() => {
    if (generatedMeetLink && interviewType === "video") {
      form.setValue("videoLink", generatedMeetLink);
      // Also trigger form validation to update the UI
      form.trigger("videoLink");
    }
  }, [generatedMeetLink, interviewType, form]);

  useEffect(() => {
    candidatesHook.fetchAllCandidates().then(res => {
      if (res && res.candidates) setCandidates(res.candidates);
    });
  }, []);

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      let result;

      if (selfScheduling) {
        result = await scheduleSelfScheduling(data);
      } else {
        result = await scheduleInterview(data);
      }

      if (result.success) {
        onOpenChange(false);
        reset();
      }
    } catch (error) {
      console.error("Error scheduling interview:", error);
    }
  };

  const nextStep = async () => {
    if (step === 0) {
      const result = await form.trigger("candidateId");
      if (result) setStep(1);
    } else if (step === 1) {
      const result = await form.trigger([
        "interviewType",
        "interviewStage",
        "interviewers",
        "duration",
      ]);
      if (result) {
        // Step 2: Generate meet link
        const formData = form.getValues();
        if (
          formData.candidateId &&
          formData.interviewType &&
          formData.interviewStage &&
          formData.interviewers &&
          formData.duration
        ) {
          // Debug authentication first
          debugAuth();

          const meetResult = await generateMeetLink(formData);
          if (meetResult.success) {
            setStep(2);
          } else {
            toast.error("Failed to generate meet link", {
              description: meetResult.message || "Please try again",
            });
          }
        }
      }
    } else if (step === 2) {
      if (selfScheduling) {
        const result = await form.trigger(["availableFrom", "availableTo"]);
        if (result) setStep(3);
      } else {
        const result = await form.trigger(["date", "time"]);
        if (result) {
          // Step 3: Save schedule details
          const formData = form.getValues();
          if (
            formData.candidateId &&
            formData.interviewType &&
            formData.interviewStage &&
            formData.interviewers &&
            formData.duration
          ) {
            const scheduleResult = await saveScheduleDetails(formData);
            if (scheduleResult.success) {
              setStep(3);
            } else {
              toast.error("Failed to save schedule details", {
                description: scheduleResult.message || "Please try again",
              });
            }
          }
        }
      }
    }
  };

  const prevStep = () => {
    if (step > 0) setStep(step - 1);
  };

  // Transform interviewers from useInterviewers hook to match InterviewerAvailability component interface
  const transformedInterviewers = allInterviewers.map(interviewer => ({
    id: interviewer.id,
    name: interviewer.name,
    role: interviewer.title || interviewer.designation || "Interviewer",
    department: interviewer.department,
    available: interviewer.isAvailable,
  }));

  // Generate time slots in 15-minute intervals from 9:00 AM to 6:00 PM
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 9; hour <= 18; hour++) {
      const hourFormat = hour < 12 ? `${hour}` : hour === 12 ? "12" : `${hour - 12}`;
      const amPm = hour < 12 ? "AM" : "PM";

      for (let minute = 0; minute < 60; minute += 15) {
        if (hour === 18 && minute > 0) continue; // Don't go past 6:00 PM

        const minuteFormat = minute === 0 ? "00" : `${minute}`;
        const timeLabel = `${hourFormat}:${minuteFormat} ${amPm}`;
        const timeValue = `${hour.toString().padStart(2, "0")}:${minuteFormat}`;

        slots.push({ label: timeLabel, value: timeValue });
      }
    }
    return slots;
  };

  const timeSlots = generateTimeSlots();

  // Filter candidates based on search term
  const filteredCandidates = candidates.filter(
    candidate =>
      candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      candidate.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className="w-full max-w-[90%] sm:max-w-[80%] lg:max-w-[1100px] p-0"
      >
        <div className="h-full overflow-y-auto">
          <div className="sticky top-0 z-10 bg-background border-b">
            <div className="flex items-center justify-between p-6">
              <div>
                <SheetTitle className="text-2xl font-bold tracking-tight">
                  Schedule Interview
                </SheetTitle>
                <p className="text-muted-foreground">
                  {defaultCandidate
                    ? `Schedule interview for ${defaultCandidate.name}`
                    : "Set up an interview for a candidate"}
                </p>
              </div>
              <SheetClose asChild>
                <Button variant="ghost" size="icon">
                  <X className="h-4 w-4" />
                </Button>
              </SheetClose>
            </div>
          </div>

          <div className="p-6">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Left column - Form steps */}
              <div className="w-full lg:w-2/3">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {/* Progress indicators */}
                    <div className="flex flex-wrap mb-6">
                      {!defaultCandidate && (
                        <>
                          <div
                            className={cn(
                              "flex items-center gap-2 p-2 rounded-lg",
                              step === 0 ? "bg-blue-50 text-blue-700" : "text-muted-foreground"
                            )}
                          >
                            <span
                              className={cn(
                                "flex items-center justify-center w-6 h-6 rounded-full text-sm",
                                step === 0
                                  ? "bg-blue-100 text-blue-700"
                                  : "bg-muted text-muted-foreground"
                              )}
                            >
                              1
                            </span>
                            <span className="font-medium">Candidate</span>
                          </div>
                          <div className="mx-2 flex items-center">
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </>
                      )}
                      <div
                        className={cn(
                          "flex items-center gap-2 p-2 rounded-lg",
                          step === 1 ? "bg-blue-50 text-blue-700" : "text-muted-foreground"
                        )}
                      >
                        <span
                          className={cn(
                            "flex items-center justify-center w-6 h-6 rounded-full text-sm",
                            step === 1
                              ? "bg-blue-100 text-blue-700"
                              : "bg-muted text-muted-foreground"
                          )}
                        >
                          2
                        </span>
                        <span className="font-medium">Details</span>
                      </div>
                      <div className="mx-2 flex items-center">
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div
                        className={cn(
                          "flex items-center gap-2 p-2 rounded-lg",
                          step === 2 ? "bg-blue-50 text-blue-700" : "text-muted-foreground"
                        )}
                      >
                        <span
                          className={cn(
                            "flex items-center justify-center w-6 h-6 rounded-full text-sm",
                            step === 2
                              ? "bg-blue-100 text-blue-700"
                              : "bg-muted text-muted-foreground"
                          )}
                        >
                          3
                        </span>
                        <span className="font-medium">Schedule</span>
                      </div>
                      <div className="mx-2 flex items-center">
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div
                        className={cn(
                          "flex items-center gap-2 p-2 rounded-lg",
                          step === 3 ? "bg-blue-50 text-blue-700" : "text-muted-foreground"
                        )}
                      >
                        <span
                          className={cn(
                            "flex items-center justify-center w-6 h-6 rounded-full text-sm",
                            step === 3
                              ? "bg-blue-100 text-blue-700"
                              : "bg-muted text-muted-foreground"
                          )}
                        >
                          4
                        </span>
                        <span className="font-medium">Review</span>
                      </div>
                    </div>

                    {/* Step 0: Candidate Selection */}
                    {!defaultCandidate && step === 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Select Candidate</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={form.control}
                            name="candidateId"
                            render={({ field }) => (
                              <FormItem className="flex flex-col">
                                <FormLabel>Candidate</FormLabel>
                                <Popover
                                  open={isCandidatePopoverOpen}
                                  onOpenChange={setIsCandidatePopoverOpen}
                                >
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant="outline"
                                        role="combobox"
                                        className={cn(
                                          "w-full justify-between",
                                          !field.value && "text-muted-foreground"
                                        )}
                                      >
                                        {field.value
                                          ? candidates.find(
                                              candidate => candidate.id === field.value
                                            )?.name
                                          : "Select candidate"}
                                        <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-[400px] p-0">
                                    <Command>
                                      <CommandInput
                                        placeholder="Search candidates..."
                                        value={searchTerm}
                                        onValueChange={setSearchTerm}
                                      />
                                      <CommandEmpty>No candidate found.</CommandEmpty>
                                      <CommandGroup>
                                        {filteredCandidates.map(candidate => (
                                          <CommandItem
                                            key={candidate.id}
                                            value={candidate.id}
                                            onSelect={currentValue => {
                                              form.setValue("candidateId", currentValue);
                                              setIsCandidatePopoverOpen(false);
                                            }}
                                            className="flex items-center gap-2 py-3"
                                          >
                                            <div className="flex items-center gap-3 flex-1">
                                              <div className="h-8 w-8 rounded-full overflow-hidden bg-muted">
                                                <img
                                                  src={candidate.photo}
                                                  alt={candidate.name}
                                                  className="h-full w-full object-cover"
                                                />
                                              </div>
                                              <div className="flex flex-col">
                                                <span className="font-medium">
                                                  {candidate.name}
                                                </span>
                                                <span className="text-xs text-muted-foreground">
                                                  {candidate.role}
                                                </span>
                                              </div>
                                            </div>
                                            <Badge variant="outline" className="ml-auto">
                                              {candidate.stage}
                                            </Badge>
                                          </CommandItem>
                                        ))}
                                      </CommandGroup>
                                    </Command>
                                  </PopoverContent>
                                </Popover>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    )}

                    {/* Step 1: Interview Details */}
                    {step === 1 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Interview Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={form.control}
                            name="interviewType"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Interview Type</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select interview type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="face_to_face">
                                      <div className="flex items-center gap-2">
                                        <Users className="h-4 w-4" />
                                        <span>Face to Face</span>
                                      </div>
                                    </SelectItem>
                                    <SelectItem value="phone">
                                      <div className="flex items-center gap-2">
                                        <Clock className="h-4 w-4" />
                                        <span>Phone</span>
                                      </div>
                                    </SelectItem>
                                    <SelectItem value="video">
                                      <div className="flex items-center gap-2">
                                        <Video className="h-4 w-4" />
                                        <span>Video</span>
                                      </div>
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="interviewStage"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Interview Stage</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select interview stage" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {Object.values(InterviewStage).map((stage) => (
                                      <SelectItem key={stage} value={stage}>
                                        {stage}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="interviewers"
                            render={() => (
                              <FormItem>
                                <FormLabel>Interviewers</FormLabel>
                                {interviewersLoading ? (
                                  <div className="flex items-center justify-center p-4 text-muted-foreground">
                                    Loading interviewers...
                                  </div>
                                ) : (
                                  <InterviewerAvailability
                                    interviewers={transformedInterviewers}
                                    selectedInterviewers={form.watch("interviewers")}
                                    onChange={interviewers =>
                                      form.setValue("interviewers", interviewers)
                                    }
                                  />
                                )}
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="duration"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Duration (minutes)</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select duration" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="15">15 minutes</SelectItem>
                                    <SelectItem value="30">30 minutes</SelectItem>
                                    <SelectItem value="45">45 minutes</SelectItem>
                                    <SelectItem value="60">60 minutes</SelectItem>
                                    <SelectItem value="90">90 minutes</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="instructions"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Instructions</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Add any special instructions or preparation notes for the candidate"
                                    {...field}
                                    className="min-h-[100px]"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="isRescheduled"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Reschedule</FormLabel>
                                  <p className="text-sm text-muted-foreground">
                                    Is this a rescheduled interview?
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          {isRescheduled && (
                            <FormField
                              control={form.control}
                              name="rescheduleNote"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Reschedule Note</FormLabel>
                                  <FormControl>
                                    <Textarea placeholder="Reason for rescheduling" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Step 2: Scheduling */}
                    {step === 2 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Scheduling</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={form.control}
                            name="selfScheduling"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">
                                    Enable Self-Scheduling
                                  </FormLabel>
                                  <p className="text-sm text-muted-foreground">
                                    Allow candidate to select from available time slots
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          {/* Manual Scheduling */}
                          {!selfScheduling && (
                            <div className="space-y-4">
                              <FormField
                                control={form.control}
                                name="date"
                                render={({ field }) => (
                                  <FormItem className="flex flex-col">
                                    <FormLabel>Date</FormLabel>
                                    <Popover>
                                      <PopoverTrigger asChild>
                                        <FormControl>
                                          <Button
                                            variant={"outline"}
                                            className={cn(
                                              "w-full pl-3 text-left font-normal",
                                              !field.value && "text-muted-foreground"
                                            )}
                                          >
                                            {field.value ? (
                                              format(field.value, "PPP")
                                            ) : (
                                              <span>Pick a date</span>
                                            )}
                                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                          </Button>
                                        </FormControl>
                                      </PopoverTrigger>
                                      <PopoverContent className="w-auto p-0" align="start">
                                        <Calendar
                                          mode="single"
                                          selected={field.value}
                                          onSelect={field.onChange}
                                          disabled={date => date < new Date()}
                                          initialFocus
                                          className="pointer-events-auto"
                                        />
                                      </PopoverContent>
                                    </Popover>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="time"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Time</FormLabel>
                                    <Select onValueChange={field.onChange}>
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="Select time" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent className="max-h-[300px]">
                                        {timeSlots.map(slot => (
                                          <SelectItem key={slot.value} value={slot.value}>
                                            {slot.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              {interviewType === "face_to_face" && (
                                <FormField
                                  control={form.control}
                                  name="location"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Location</FormLabel>
                                      <FormControl>
                                        <Input placeholder="Enter interview location" {...field} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}

                              {interviewType === "video" && (
                                <FormField
                                  control={form.control}
                                  name="videoLink"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Video Call Link</FormLabel>
                                      <FormControl>
                                        <Input
                                          placeholder="Video call link (auto-generated if left empty)"
                                          {...field}
                                          value={field.value || generatedMeetLink || ""}
                                        />
                                      </FormControl>
                                      {generatedMeetLink && (
                                        <div className="space-y-2">
                                          <p className="text-sm text-green-600 flex items-center gap-1">
                                            <span>✓</span>
                                            Meet link auto-generated and ready
                                          </p>
                                          <p className="text-xs text-muted-foreground">
                                            Generated link: {generatedMeetLink}
                                          </p>
                                        </div>
                                      )}
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}
                            </div>
                          )}

                          {/* Self Scheduling */}
                          {selfScheduling && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <FormField
                                  control={form.control}
                                  name="availableFrom"
                                  render={({ field }) => (
                                    <FormItem className="flex flex-col">
                                      <FormLabel>Available From</FormLabel>
                                      <Popover>
                                        <PopoverTrigger asChild>
                                          <FormControl>
                                            <Button
                                              variant={"outline"}
                                              className={cn(
                                                "w-full pl-3 text-left font-normal",
                                                !field.value && "text-muted-foreground"
                                              )}
                                            >
                                              {field.value ? (
                                                format(field.value, "PPP")
                                              ) : (
                                                <span>Start date</span>
                                              )}
                                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                            </Button>
                                          </FormControl>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0" align="start">
                                          <Calendar
                                            mode="single"
                                            selected={field.value}
                                            onSelect={field.onChange}
                                            disabled={date => date < new Date()}
                                            initialFocus
                                            className="pointer-events-auto"
                                          />
                                        </PopoverContent>
                                      </Popover>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={form.control}
                                  name="availableTo"
                                  render={({ field }) => (
                                    <FormItem className="flex flex-col">
                                      <FormLabel>Available To</FormLabel>
                                      <Popover>
                                        <PopoverTrigger asChild>
                                          <FormControl>
                                            <Button
                                              variant={"outline"}
                                              className={cn(
                                                "w-full pl-3 text-left font-normal",
                                                !field.value && "text-muted-foreground"
                                              )}
                                            >
                                              {field.value ? (
                                                format(field.value, "PPP")
                                              ) : (
                                                <span>End date</span>
                                              )}
                                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                            </Button>
                                          </FormControl>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0" align="start">
                                          <Calendar
                                            mode="single"
                                            selected={field.value}
                                            onSelect={field.onChange}
                                            disabled={date =>
                                              date < new Date() ||
                                              (form.getValues("availableFrom") &&
                                                date < form.getValues("availableFrom"))
                                            }
                                            initialFocus
                                            className="pointer-events-auto"
                                          />
                                        </PopoverContent>
                                      </Popover>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>

                              {interviewType === "face_to_face" && (
                                <FormField
                                  control={form.control}
                                  name="location"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Location</FormLabel>
                                      <FormControl>
                                        <Input placeholder="Enter interview location" {...field} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}

                              {/* Display time range options */}
                              <div>
                                <h3 className="text-sm font-medium mb-2">Available Time Ranges</h3>
                                <div className="space-y-2">
                                  <div className="flex items-center justify-between p-2 border rounded-md">
                                    <span className="text-sm">Weekdays (Mon-Fri)</span>
                                    <span className="text-sm font-medium">9:00 AM - 5:00 PM</span>
                                  </div>
                                  <div className="flex items-center justify-between p-2 border rounded-md bg-blue-50">
                                    <span className="text-sm">Generate 30-minute slots</span>
                                    <Badge variant="outline" className="bg-blue-100">
                                      {form.getValues("duration")} min each
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Step 3: Review */}
                    {step === 3 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Review and Confirm</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <InterviewSummary
                            formData={form.getValues()}
                            selfScheduling={selfScheduling}
                            interviewers={transformedInterviewers.filter(i =>
                              form.getValues("interviewers").includes(i.id)
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="sendEmail"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <div className="flex items-center gap-2">
                                    <Mail className="h-4 w-4" />
                                    <FormLabel className="text-base">Send Notifications</FormLabel>
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    Send email and calendar invites to all participants
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    )}

                    {/* Form Navigation */}
                    <div className="flex justify-between pt-4">
                      <div className="flex gap-2">
                        {step === 0 && (
                          <SheetClose asChild>
                            <Button variant="outline">Cancel</Button>
                          </SheetClose>
                        )}
                        <Button
                          type="button"
                          variant="outline"
                          onClick={prevStep}
                          disabled={step === 0}
                        >
                          Previous
                        </Button>
                      </div>
                      {step < 3 ? (
                        <Button type="button" onClick={nextStep} disabled={interviewLoading}>
                          {interviewLoading ? "Processing..." : "Next"}
                        </Button>
                      ) : (
                        <Button type="submit" disabled={interviewLoading}>
                          {interviewLoading
                            ? "Scheduling..."
                            : selfScheduling
                            ? "Send Scheduling Request"
                            : "Schedule Interview"}
                        </Button>
                      )}
                    </div>
                  </form>
                </Form>
              </div>

              {/* Right column - Candidate Summary */}
              <div className="w-full lg:w-1/3">
                {candidatesHook.loading ? (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Candidate</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-col items-center justify-center h-40 text-center text-muted-foreground">
                        <p>Loading candidate details...</p>
                      </div>
                    </CardContent>
                  </Card>
                ) : selectedCandidateDetail ? (
                  <CandidateSummary
                    candidate={selectedCandidateDetail}
                    interviews={selectedCandidateDetail.interviews || []}
                  />
                ) : (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Candidate</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-col items-center justify-center h-40 text-center text-muted-foreground">
                        <p>Please select a candidate to schedule an interview.</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default ScheduleInterview;
