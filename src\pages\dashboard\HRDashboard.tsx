import { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { StatsCard } from '@/components/dashboard/StatsCard';
import { ActivityItem } from '@/components/dashboard/ActivityItem';
import { AICard } from '@/components/dashboard/AICard';
import { FileText, Users, Briefcase, CheckCircle, Clock } from 'lucide-react';
import { useAuth } from '@/components/auth/AuthProvider';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/sonner';
import { Progress } from '@/components/ui/progress';

const HRDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  const handleGenerateOffer = () => {
    toast.success("Opening Offer Generator", {
      description: "This feature would open the AI offer letter generator tool."
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            HR Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.full_name || 'HR Manager'}!
          </p>
        </div>
        <div className="flex gap-2">
          <Button size="sm">
            <FileText size={16} className="mr-2" />
            Generate Offer
          </Button>
          <Button size="sm" variant="outline">
            <Users size={16} className="mr-2" />
            View Candidates
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard 
          title="Pending Offers" 
          value={loading ? "..." : "6"}
          description="2 awaiting response"
          icon={<FileText size={16} />}
        />
        <StatsCard 
          title="New Hires" 
          value={loading ? "..." : "8"}
          description="This month"
          icon={<Users size={16} />}
        />
        <StatsCard 
          title="Acceptance Rate" 
          value={loading ? "..." : "92%"}
          trend="up"
          trendValue="5% from last quarter"
          icon={<CheckCircle size={16} />}
        />
        <StatsCard 
          title="Time to Onboard" 
          value={loading ? "..." : "12 days"}
          trend="down"
          trendValue="2 days improvement"
          icon={<Clock size={16} />}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-7">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Offers Pending Action</CardTitle>
            <CardDescription>Review and send offer letters</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {loading ? (
              <div className="p-6 space-y-2">
                <div className="h-16 bg-muted animate-pulse rounded-md" />
                <div className="h-16 bg-muted animate-pulse rounded-md" />
                <div className="h-16 bg-muted animate-pulse rounded-md" />
              </div>
            ) : (
              <div className="divide-y">
                <div className="p-4 flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <h3 className="font-medium">Thomas Wright</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">Senior Product Manager</Badge>
                      <Badge variant="outline" className="bg-amber-50 text-amber-700">Draft</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">Approved by Alex Chen on May 10</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">View Details</Button>
                    <Button size="sm">Send Offer</Button>
                  </div>
                </div>
                
                <div className="p-4 flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <h3 className="font-medium">Jennifer Lopez</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">Frontend Developer</Badge>
                      <Badge variant="outline" className="bg-green-50 text-green-700">Ready</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">Approved by Sam Davis on May 11</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">View Details</Button>
                    <Button size="sm">Send Offer</Button>
                  </div>
                </div>
                
                <div className="p-4 flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <h3 className="font-medium">Michael Brown</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">DevOps Engineer</Badge>
                      <Badge variant="outline" className="bg-green-50 text-green-700">Ready</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">Approved by Lisa Kim on May 12</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">View Details</Button>
                    <Button size="sm">Send Offer</Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="border-t px-6 py-4">
            <Button variant="outline" className="w-full">View All Offers</Button>
          </CardFooter>
        </Card>
        
        <div className="md:col-span-3 grid gap-4">
          <AICard
            title="Offer Letter Generator"
            description="Generate personalized offer letters with competitive terms"
            action={handleGenerateOffer}
            actionLabel="Create Offer"
            icon={<FileText size={18} className="text-violet-500" />}
          />
          
          <Card>
            <CardHeader>
              <CardTitle>Onboarding Status</CardTitle>
              <CardDescription>New hire progress</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  <div className="h-8 bg-muted animate-pulse rounded-md w-2/3" />
                  <div className="h-2 bg-muted animate-pulse rounded-md" />
                  <div className="h-8 bg-muted animate-pulse rounded-md w-2/3 mt-4" />
                  <div className="h-2 bg-muted animate-pulse rounded-md" />
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Sarah Johnson - UX Designer</span>
                      <span className="text-sm text-muted-foreground">85%</span>
                    </div>
                    <Progress value={85} />
                    <div className="text-xs text-muted-foreground">
                      Starting May 20 - Pending equipment setup
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">David Park - Backend Developer</span>
                      <span className="text-sm text-muted-foreground">40%</span>
                    </div>
                    <Progress value={40} />
                    <div className="text-xs text-muted-foreground">
                      Starting June 1 - Pending paperwork
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest HR activities</CardDescription>
          </CardHeader>
          <CardContent className="px-2">
            {loading ? (
              <div className="space-y-2">
                <div className="h-12 bg-muted animate-pulse rounded-md" />
                <div className="h-12 bg-muted animate-pulse rounded-md" />
                <div className="h-12 bg-muted animate-pulse rounded-md" />
                <div className="h-12 bg-muted animate-pulse rounded-md" />
              </div>
            ) : (
              <div className="space-y-0">
                <ActivityItem
                  avatarFallback="JD"
                  title={<>Offer accepted by <strong>John Doe</strong></>}
                  description="Data Analyst - Starting June 15"
                  timestamp="2 hours ago"
                  status="success"
                />
                <ActivityItem
                  avatarFallback="KW"
                  title={<>Sent offer to <strong>Karen Williams</strong></>}
                  description="Marketing Manager - Awaiting response"
                  timestamp="Yesterday"
                  status="warning"
                />
                <ActivityItem
                  avatarFallback="RS"
                  title={<>Completed onboarding for <strong>Robert Smith</strong></>}
                  description="Backend Engineer - Started May 8"
                  timestamp="3 days ago"
                  status="success"
                />
                <ActivityItem
                  avatarFallback="AP"
                  title={<>Updated offer for <strong>Anna Peterson</strong></>}
                  description="UX Designer - Salary adjustment"
                  timestamp="4 days ago"
                  status="default"
                />
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Hiring Metrics</CardTitle>
            <CardDescription>Performance metrics for this quarter</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                <div className="h-[220px] bg-muted animate-pulse rounded-md" />
              </div>
            ) : (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Average Time to Fill</p>
                    <p className="text-2xl font-bold">32 days</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Cost per Hire</p>
                    <p className="text-2xl font-bold">$4,250</p>
                  </div>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Offers Sent vs. Accepted</p>
                  <div className="h-4 bg-muted rounded-full overflow-hidden">
                    <div className="bg-green-500 h-full rounded-full" style={{ width: '86%' }}></div>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>22 sent</span>
                    <span>19 accepted (86%)</span>
                  </div>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Positions Filled vs. Open</p>
                  <div className="h-4 bg-muted rounded-full overflow-hidden">
                    <div className="bg-blue-500 h-full rounded-full" style={{ width: '65%' }}></div>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>13 filled</span>
                    <span>7 open</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default HRDashboard;
