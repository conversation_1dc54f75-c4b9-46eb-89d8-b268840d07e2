
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";

interface AICardProps {
  title: string;
  description: string;
  action: () => void;
  actionLabel: string;
  icon?: React.ReactNode;
}

export function AICard({ title, description, action, actionLabel, icon }: AICardProps) {
  return (
    <Card className="border-violet-200 bg-violet-50">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg text-violet-900">
            {icon || <Sparkles size={18} className="text-violet-500" />}
            {title}
          </CardTitle>
        </div>
        <CardDescription className="text-violet-700">{description}</CardDescription>
      </CardHeader>
      <CardContent className="pb-2 text-violet-900">
        <div className="h-28 w-full rounded-md border border-dashed border-violet-300 bg-violet-100/50 flex items-center justify-center">
          <p className="text-sm text-violet-600 text-center px-4">AI-powered assistance available</p>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={action} variant="default" className="w-full bg-violet-600 hover:bg-violet-700">
          <Sparkles size={16} className="mr-2" />
          {actionLabel}
        </Button>
      </CardFooter>
    </Card>
  );
}
