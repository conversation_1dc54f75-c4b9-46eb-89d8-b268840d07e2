import { supabase } from '../config/db.js';
import { logger } from './logger.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const executeStatement = async (statement) => {
  try {
    const response = await fetch(`${process.env.VITE_SUPABASE_URL}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': process.env.VITE_SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${process.env.VITE_SUPABASE_ANON_KEY}`,
        'Prefer': 'return=minimal'
      },
      body: JSON.stringify({ query: statement })
    });

    if (!response.ok && response.status !== 404) {
      const error = await response.text();
      if (!error.toLowerCase().includes('already exists')) {
        logger.debug(`Statement execution note: ${error}`);
      }
    }
  } catch (error) {
    if (!error.message?.toLowerCase().includes('already exists')) {
      logger.debug(`Statement execution note: ${error.message}`);
    }
  }
};

export const initializeDatabase = async () => {
  try {
    logger.info('Initializing database...');

    // Test Supabase connection
    const { data, error: connectionError } = await supabase
      .from('roles')
      .select('id')
      .limit(1);

    if (connectionError) {
      logger.info('Creating database schema...');

      // Read the SQL file
      const sqlPath = path.join(__dirname, '../migrations/initial_schema.sql');
      const sqlContent = await fs.readFile(sqlPath, 'utf8');

      // Split into individual statements and filter out comments and empty lines
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      // Execute each statement
      for (const statement of statements) {
        await executeStatement(statement);
      }

      // Create initial admin role if it doesn't exist
      const { data: existingRole } = await supabase
        .from('roles')
        .select('id')
        .eq('name', 'admin')
        .single();

      if (!existingRole) {
        await supabase
          .from('roles')
          .insert([{ 
            name: 'admin',
            flag_active: true,
            flag_deleted: false
          }])
          .single();
      }

      logger.info('Database initialization completed');
    } else {
      logger.info('Database schema already exists');
    }
    
    logger.info('Supabase connection successful');
    return true;
  } catch (error) {
    logger.error('Database initialization error:', error);
    return false;
  }
};