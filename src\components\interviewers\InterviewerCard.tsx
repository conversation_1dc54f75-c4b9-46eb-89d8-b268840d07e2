import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Calendar, MapPin } from "lucide-react";
import { InterviewerDetail } from "@/hooks/useInterviewers";
import { useState } from "react";

interface InterviewerCardProps {
  interviewer: InterviewerDetail;
  onViewDetails: (interviewer: InterviewerDetail) => void;
}

export function InterviewerCard({ interviewer, onViewDetails }: InterviewerCardProps) {
  const [showAllSkills, setShowAllSkills] = useState(false);
  
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  const displayedSkills = showAllSkills 
    ? interviewer.expertise 
    : interviewer.expertise.slice(0, 3);

  return (
    <Card 
      className="overflow-hidden hover:shadow-md transition-all duration-200 hover:ring-2 hover:ring-primary/20 hover:scale-[1.01] h-[280px] w-full cursor-pointer"
      onClick={() => onViewDetails(interviewer)}
    >
      <CardContent className="p-6 h-full">
        <div className="flex items-center gap-4 mb-5">
          <Avatar className="h-14 w-14 border-2 border-primary/10">
            <AvatarFallback className="bg-primary/5 text-primary font-medium">
              {getInitials(interviewer.name)}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-lg text-foreground hover:text-primary hover:underline transition-colors">
              {interviewer.name}
            </h3>
            <p className="text-muted-foreground font-medium text-sm">
              {interviewer.title}
            </p>
          </div>
        </div>
        
        <div className="flex items-center text-sm text-muted-foreground mb-4">
          <MapPin className="h-4 w-4 mr-1 text-primary/70" />
          {interviewer.location}
        </div>
        
        <div className="mb-4">
          <p className="text-sm font-medium text-foreground mb-2">Expertise</p>
          <div className="flex flex-wrap gap-1.5 max-h-[80px] overflow-y-auto">
            {displayedSkills.map((skill, index) => (
              <Badge key={index} variant="secondary" className="text-xs font-medium">
                {skill}
              </Badge>
            ))}
            {!showAllSkills && interviewer.expertise.length > 3 && (
              <Badge 
                variant="outline" 
                className="bg-primary/5 text-primary hover:bg-primary/10 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowAllSkills(true);
                }}
              >
                +{interviewer.expertise.length - 3}
              </Badge>
            )}
            {showAllSkills && interviewer.expertise.length > 3 && (
              <Badge 
                variant="outline" 
                className="bg-primary/5 text-primary hover:bg-primary/10 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowAllSkills(false);
                }}
              >
                Show less
              </Badge>
            )}
          </div>
        </div>
        
        <div className="flex items-center text-sm mt-auto">
          <Calendar className="h-4 w-4 mr-1 text-primary/70" />
          <span className="font-medium text-foreground">{interviewer.upcomingInterviews}</span>
          <span className="text-muted-foreground ml-1">upcoming interviews</span>
        </div>
      </CardContent>
    </Card>
  );
}
