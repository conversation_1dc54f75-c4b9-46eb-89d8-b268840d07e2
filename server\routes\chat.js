import express from "express";
import { 
  startChat,
  sendMessage,
  getChatLogs
} from "../controllers/ChatController.js";
import { asyncHandler } from "../middleware/asyncHandler.js";
import { authenticate } from "../middleware/authMiddleware.js";
import { chatValidation } from "../validation/chatValidation.js";

const router = express.Router();

router.use(authenticate);

router.post("/start", [...chatValidation.start], asyncHandler(startChat));
router.post("/message", [...chatValidation.message], asyncHandler(sendMessage));
router.get("/logs", asyncHandler(getChatLogs));

export default router;