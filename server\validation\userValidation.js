import { z } from 'zod';

export const userValidation = {
  loginSchema: z.object({
    body: z.object({
      email: z.string().email('Invalid email format'),
      password: z.string().min(6, 'Password must be at least 6 characters')
    })
  }),

  registerSchema: z.object({
    body: z.object({
      full_name: z.string().min(2, 'Full name must be at least 2 characters'),
      email: z.string().email('Invalid email format'),
      password: z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/[0-9]/, 'Password must contain at least one number')
        .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
      role_id: z.string().uuid('Invalid role ID'),
      language_preference: z.string().default('en')
    })
  })
};