
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3, LineChart, Download, Calendar as CalendarIcon, ChevronDown } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { StatsCard } from '@/components/dashboard/StatsCard';

const Reports = () => {
  const [loading, setLoading] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Analytics & Reports
          </h1>
          <p className="text-muted-foreground">
            Track recruitment metrics and performance
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <CalendarIcon size={16} className="mr-2" />
            Last 30 Days
            <ChevronDown size={16} className="ml-2" />
          </Button>
          <Button variant="outline">
            <Download size={16} className="mr-2" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <StatsCard 
          title="Total Applicants" 
          value="248"
          trend="up"
          trendValue="12% from last month"
        />
        <StatsCard 
          title="Avg Time-to-Hire" 
          value="34 days"
          trend="down"
          trendValue="5 days improvement"
        />
        <StatsCard 
          title="Offer Acceptance" 
          value="78%"
          trend="up"
          trendValue="5% from last quarter"
        />
      </div>

      <Tabs defaultValue="hiring-funnel">
        <TabsList>
          <TabsTrigger value="hiring-funnel">Hiring Funnel</TabsTrigger>
          <TabsTrigger value="time-metrics">Time Metrics</TabsTrigger>
          <TabsTrigger value="source-analytics">Source Analytics</TabsTrigger>
          <TabsTrigger value="interviewer">Interviewer Stats</TabsTrigger>
        </TabsList>
        
        <TabsContent value="hiring-funnel" className="mt-6">
          <div className="grid gap-6 lg:grid-cols-3">
            <Card className="lg:col-span-2">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Hiring Funnel</CardTitle>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[160px]">
                    <SelectValue placeholder="All Jobs" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Jobs</SelectItem>
                    <SelectItem value="engineering">Engineering</SelectItem>
                    <SelectItem value="design">Design</SelectItem>
                    <SelectItem value="product">Product</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                  </SelectContent>
                </Select>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="h-80 bg-muted animate-pulse rounded-md" />
                ) : (
                  <div className="h-80 flex items-end gap-4 pt-6 px-2">
                    <div className="relative h-full flex flex-col justify-end items-center gap-2">
                      <div className="absolute top-0 text-sm font-medium">248</div>
                      <div className="w-20 bg-blue-500 rounded-t-md" style={{ height: '100%' }}></div>
                      <div className="text-sm font-medium">Applied</div>
                    </div>
                    
                    <div className="relative h-full flex flex-col justify-end items-center gap-2">
                      <div className="absolute top-0 text-sm font-medium">162</div>
                      <div className="w-20 bg-blue-400 rounded-t-md" style={{ height: '65%' }}></div>
                      <div className="text-sm font-medium">Screened</div>
                    </div>
                    
                    <div className="relative h-full flex flex-col justify-end items-center gap-2">
                      <div className="absolute top-0 text-sm font-medium">94</div>
                      <div className="w-20 bg-blue-300 rounded-t-md" style={{ height: '38%' }}></div>
                      <div className="text-sm font-medium">Interviewed</div>
                    </div>
                    
                    <div className="relative h-full flex flex-col justify-end items-center gap-2">
                      <div className="absolute top-0 text-sm font-medium">28</div>
                      <div className="w-20 bg-blue-200 rounded-t-md" style={{ height: '11%' }}></div>
                      <div className="text-sm font-medium">Offer</div>
                    </div>
                    
                    <div className="relative h-full flex flex-col justify-end items-center gap-2">
                      <div className="absolute top-0 text-sm font-medium">22</div>
                      <div className="w-20 bg-green-500 rounded-t-md" style={{ height: '9%' }}></div>
                      <div className="text-sm font-medium">Hired</div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Conversion Rates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Applied → Screened</span>
                      <span className="text-sm font-medium">65.3%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-blue-500 h-full rounded-full" style={{ width: '65.3%' }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Screened → Interviewed</span>
                      <span className="text-sm font-medium">58.0%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-blue-500 h-full rounded-full" style={{ width: '58%' }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Interviewed → Offer</span>
                      <span className="text-sm font-medium">29.8%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-blue-500 h-full rounded-full" style={{ width: '29.8%' }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Offer → Hired</span>
                      <span className="text-sm font-medium">78.6%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-green-500 h-full rounded-full" style={{ width: '78.6%' }}></div>
                    </div>
                  </div>
                  
                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium">Overall Conversion</span>
                      <span className="text-sm font-medium">8.9%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-violet-500 h-full rounded-full" style={{ width: '8.9%' }}></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">Industry average: 8.4%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="time-metrics" className="mt-6">
          <div className="grid gap-6 lg:grid-cols-3">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Time-to-Hire Trend</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="h-80 bg-muted animate-pulse rounded-md" />
                ) : (
                  <div className="h-80 flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <LineChart className="h-16 w-16 mx-auto mb-2 text-muted-foreground/60" />
                      <p>Time-to-hire trend chart would appear here</p>
                      <p className="text-sm">Showing decreasing trend over the past 6 months</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Time Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Application to Screening</span>
                      <span className="text-sm font-medium">5 days</span>
                    </div>
                    <div className="h-4 bg-blue-100 rounded-full overflow-hidden">
                      <div className="bg-blue-500 h-full rounded-full" style={{ width: '15%' }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Screening to Interview</span>
                      <span className="text-sm font-medium">8 days</span>
                    </div>
                    <div className="h-4 bg-purple-100 rounded-full overflow-hidden">
                      <div className="bg-purple-500 h-full rounded-full" style={{ width: '24%' }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Interview to Decision</span>
                      <span className="text-sm font-medium">12 days</span>
                    </div>
                    <div className="h-4 bg-amber-100 rounded-full overflow-hidden">
                      <div className="bg-amber-500 h-full rounded-full" style={{ width: '35%' }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Decision to Offer</span>
                      <span className="text-sm font-medium">5 days</span>
                    </div>
                    <div className="h-4 bg-green-100 rounded-full overflow-hidden">
                      <div className="bg-green-500 h-full rounded-full" style={{ width: '15%' }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Offer to Acceptance</span>
                      <span className="text-sm font-medium">4 days</span>
                    </div>
                    <div className="h-4 bg-blue-100 rounded-full overflow-hidden">
                      <div className="bg-blue-500 h-full rounded-full" style={{ width: '11%' }}></div>
                    </div>
                  </div>
                  
                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Total Time-to-Hire</span>
                      <span className="text-sm font-medium">34 days</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">5 days faster than last quarter</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="source-analytics" className="mt-6">
          <div className="grid gap-6 lg:grid-cols-3">
            <Card className="lg:col-span-2">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Candidate Sources</CardTitle>
                <Select defaultValue="applications">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Metric" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="applications">Applications</SelectItem>
                    <SelectItem value="interviews">Interviews</SelectItem>
                    <SelectItem value="hires">Hires</SelectItem>
                    <SelectItem value="conversion">Conversion Rate</SelectItem>
                  </SelectContent>
                </Select>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="h-80 bg-muted animate-pulse rounded-md" />
                ) : (
                  <div className="h-80 flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <BarChart3 className="h-16 w-16 mx-auto mb-2 text-muted-foreground/60" />
                      <p>Candidate source distribution chart would appear here</p>
                      <p className="text-sm">Showing applications by source</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Source Effectiveness</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Referrals</span>
                      <span className="text-sm font-medium">18.5%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-green-500 h-full rounded-full" style={{ width: '18.5%' }}></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">42 applications, 8 hires</p>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">LinkedIn</span>
                      <span className="text-sm font-medium">9.6%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-amber-500 h-full rounded-full" style={{ width: '9.6%' }}></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">83 applications, 8 hires</p>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Job Boards</span>
                      <span className="text-sm font-medium">4.3%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-blue-500 h-full rounded-full" style={{ width: '4.3%' }}></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">92 applications, 4 hires</p>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">Company Website</span>
                      <span className="text-sm font-medium">3.2%</span>
                    </div>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div className="bg-blue-400 h-full rounded-full" style={{ width: '3.2%' }}></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">31 applications, 1 hire</p>
                  </div>
                  
                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Overall Conversion</span>
                      <span className="text-sm font-medium">8.9%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="interviewer" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Interviewer Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="py-3 text-left font-medium">Interviewer</th>
                      <th className="py-3 text-center font-medium">Interviews</th>
                      <th className="py-3 text-center font-medium">Avg. Duration</th>
                      <th className="py-3 text-center font-medium">Feedback Time</th>
                      <th className="py-3 text-center font-medium">Hire Rate</th>
                      <th className="py-3 text-center font-medium">Quality</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    <tr>
                      <td className="py-3">Michael Lee</td>
                      <td className="py-3 text-center">24</td>
                      <td className="py-3 text-center">48 min</td>
                      <td className="py-3 text-center">1.2 days</td>
                      <td className="py-3 text-center">32%</td>
                      <td className="py-3 text-center">4.8/5</td>
                    </tr>
                    <tr>
                      <td className="py-3">Sarah Johnson</td>
                      <td className="py-3 text-center">18</td>
                      <td className="py-3 text-center">42 min</td>
                      <td className="py-3 text-center">0.8 days</td>
                      <td className="py-3 text-center">28%</td>
                      <td className="py-3 text-center">4.7/5</td>
                    </tr>
                    <tr>
                      <td className="py-3">Alex Chen</td>
                      <td className="py-3 text-center">22</td>
                      <td className="py-3 text-center">55 min</td>
                      <td className="py-3 text-center">1.5 days</td>
                      <td className="py-3 text-center">36%</td>
                      <td className="py-3 text-center">4.6/5</td>
                    </tr>
                    <tr>
                      <td className="py-3">Jessica Miller</td>
                      <td className="py-3 text-center">15</td>
                      <td className="py-3 text-center">46 min</td>
                      <td className="py-3 text-center">1.0 days</td>
                      <td className="py-3 text-center">33%</td>
                      <td className="py-3 text-center">4.9/5</td>
                    </tr>
                    <tr>
                      <td className="py-3">David Wilson</td>
                      <td className="py-3 text-center">20</td>
                      <td className="py-3 text-center">52 min</td>
                      <td className="py-3 text-center">1.8 days</td>
                      <td className="py-3 text-center">25%</td>
                      <td className="py-3 text-center">4.4/5</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Reports;
