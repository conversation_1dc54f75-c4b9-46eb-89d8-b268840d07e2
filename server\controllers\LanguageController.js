import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { languageService } from "../services/languageService.js";

export const getLanguages = async (req, res) => {
  try {
    const languages = await languageService.getLanguages();
    logger.info("Languages retrieved successfully");
    return responseHandler.success(res, { languages });
  } catch (err) {
    logger.error("Failed to get languages:", err);
    return responseHandler.error(res, "Failed to retrieve languages");
  }
};

export const updateUserLanguage = async (req, res) => {
  try {
    const result = await languageService.updateUserLanguage(
      req.params.id,
      req.body.language_code,
      req.user._id
    );
    
    if (!result.success) {
      logger.info(`Language update failed: ${result.message}`);
      return responseHandler.error(res, result.message, result.status || 400);
    }

    logger.info(`User language updated: ${req.params.id}`);
    return responseHandler.success(res, { user: result.user });
  } catch (err) {
    logger.error("Failed to update user language:", err);
    return responseHandler.error(res, "Failed to update language preference");
  }
};