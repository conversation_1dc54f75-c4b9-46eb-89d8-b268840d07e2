import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { chatService } from "../services/chatService.js";

export const startChat = async (req, res) => {
  try {
    const result = await chatService.startChat(req.user._id);
    logger.info(`Chat session started for user: ${req.user._id}`);
    return responseHandler.success(res, { session: result.session });
  } catch (err) {
    logger.error("Failed to start chat:", err);
    return responseHandler.error(res, "Failed to start chat session");
  }
};

export const sendMessage = async (req, res) => {
  try {
    const result = await chatService.sendMessage({
      user_id: req.user._id,
      message: req.body.message
    });
    
    logger.info(`Message processed for user: ${req.user._id}`);
    return responseHandler.success(res, { response: result.response });
  } catch (err) {
    logger.error("Failed to process message:", err);
    return responseHandler.error(res, "Failed to process message");
  }
};

export const getChatLogs = async (req, res) => {
  try {
    const logs = await chatService.getChatLogs(req.user._id, req.query);
    logger.info(`Chat logs retrieved for user: ${req.user._id}`);
    return responseHandler.success(res, logs);
  } catch (err) {
    logger.error("Failed to get chat logs:", err);
    return responseHandler.error(res, "Failed to retrieve chat logs");
  }
};