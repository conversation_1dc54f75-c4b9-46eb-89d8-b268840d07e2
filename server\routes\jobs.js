import express from 'express';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { jobController } from '../controllers/JobController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/jobs
 * @desc    Get all jobs with pagination
 * @access  Public
 */
router.get('/', async<PERSON>and<PERSON>(jobController.getJobs));

/**
 * @route   GET /api/jobs/:id
 * @desc    Get job by ID
 * @access  Public
 */
router.get('/:id', asyncHandler(jobController.getJobById));

/**
 * @route   POST /api/jobs
 * @desc    Create a new job
 * @access  Private/Admin
 */
router.post('/',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(jobController.createJob)
);

/**
 * @route   PUT /api/jobs/:id
 * @desc    Update job
 * @access  Private/Admin
 */
router.put('/:id',
  authenticate,
  authorize(['admin', 'recruiter']),
  as<PERSON><PERSON><PERSON><PERSON>(jobController.updateJob)
);

/**
 * @route   DELETE /api/jobs/:id
 * @desc    Delete job
 * @access  Private/Admin
 */
router.delete('/:id',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(jobController.deleteJob)
);

/**
 * @route   POST /api/jobs/:id/post-to-platform
 * @desc    Post job to external platforms
 * @access  Private/Admin
 */
router.post('/:id/post-to-platform',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(jobController.postToPlatform)
);

/**
 * @route   GET /api/jobs/:id/candidates
 * @desc    Get candidates for a job
 * @access  Public
 */
router.get("/:id/candidates", asyncHandler(jobController.getJobCandidates));

export default router;