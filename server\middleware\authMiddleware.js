import { jwtUtils } from '../utils/jwtUtils.js';
import { responseHandler } from '../utils/responseHandler.js';
import { logger } from '../utils/logger.js';

export const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return responseHandler.unauthorized(res, 'No token provided');
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwtUtils.verifyToken(token);
    
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Authentication failed:', error);
    return responseHandler.unauthorized(res, 'Invalid token');
  }
};

export const authorize = (...roles) => {
  return (req, res, next) => {
    // Debug log
    logger.info('Authorize middleware: req.user =', req.user);
    logger.info('Authorize middleware: allowed roles =', roles);
    if (!roles[0].includes(req.user.role)) {
      logger.warn(`Unauthorized access attempt by user ${req.user.id} with role ${req.user.role}`);
      return responseHandler.forbidden(res, 'You do not have permission to perform this action');
    }
    next();
  };
};