export function addInterviewStatus(interview) {
  const now = new Date();

  const { scheduled_date, scheduled_time, feedback_id } = interview;

  const scheduledDateTime = new Date(`${scheduled_date}T${scheduled_time}`);
  const isPast = scheduledDateTime < now;
  const hasFeedback = feedback_id !== null;

  let status = "upcoming";

  if (isPast) {
    status = hasFeedback ? "completed" : "feedback_pending";
  }

  return {
    ...interview,
    status,
  };
}
