import { createClient } from '@supabase/supabase-js';
import { logger } from './logger.js';

const { VITE_SUPABASE_URL, 
  VITE_SUPABASE_ANON_KEY, 
  SUPABASE_STORAGE_URL, 
  SUPABASE_STORAGE_BUCKET 
} = process.env;

const supabase = createClient(VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY);

export const fileUploader = {
  async uploadToSupabase(file, folderName) {
    try {
      const fileName = `${Date.now()}-${file.originalname}`;
      const { data, error } = await supabase.storage
        .from(SUPABASE_STORAGE_BUCKET)
        .upload(`${folderName}/${fileName}`, file.buffer, {
          contentType: file.mimetype,
          cacheControl: '3600'
        });

      if (error) {
        logger.error('File upload failed:', error);
        throw new Error('Failed to upload file');
      }

      const fileUrl = `${SUPABASE_STORAGE_URL}${SUPABASE_STORAGE_BUCKET}/${data.path}`;

      return {
        success: true,
        url: fileUrl,
      };
    } catch (error) {
      logger.error('File upload error:', error);
      throw new Error('File upload failed');
    }
  }
};