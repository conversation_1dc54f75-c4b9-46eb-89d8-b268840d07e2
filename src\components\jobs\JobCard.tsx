import { useState } from "react";
import { MapPin, Briefcase, Tag, Users, Info, Calendar } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn, formatWorkLocation } from "@/lib/utils";
import { getStatusColorClass } from "@/lib/utils";
import { useNavigate } from "react-router-dom";
import { JobInfoDialog } from "./JobInfoDialog";
import { type Job } from "@/hooks/useJobs";

interface JobCardProps {
  job: Job;
  onView?: (job: Job) => void;
  selected?: boolean;
  candidateCount?: number;
}

export function JobCard({ job, onView, selected = false, candidateCount = 0 }: JobCardProps) {
  const navigate = useNavigate();
  const [infoDialogOpen, setInfoDialogOpen] = useState(false);

  const handleView = () => {
    navigate(`/jobs/${job.id}/candidates`);
  };

  const handleInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setInfoDialogOpen(true);
  };

  // Capitalize first letter of status
  const capitalizedStatus = job.status 
    ? job.status.charAt(0).toUpperCase() + job.status.slice(1) 
    : "Unknown";

  // Format salary range
  const formatSalary = () => {
    if (job.salary_min && job.salary_max) {
      return `$${job.salary_min.toLocaleString()} - $${job.salary_max.toLocaleString()}`;
    } else if (job.salary_min) {
      return `$${job.salary_min.toLocaleString()}+`;
    } else if (job.salary_max) {
      return `Up to $${job.salary_max.toLocaleString()}`;
    }
    return "Competitive";
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <>
      <Card
        className={cn(
          "overflow-hidden transition-all duration-300 flex flex-col justify-between",
          "hover:shadow-lg hover:scale-[1.02] hover:ring-2 hover:ring-primary/40",
          selected ? "ring-2 ring-primary" : "border-border/50",
          "cursor-pointer rounded-xl min-h-[400px] max-h-[400px] w-[340px]"
        )}
        onClick={handleView}
      >
        {/* Job Title (Centered) */}
        <CardHeader className="pb-2 pt-4 space-y-1 text-center">
          <div className="flex justify-center items-start">
            <div className="space-y-0.5 w-full">
              {/* Department (if available) */}
              {job.department && (
                <p className="text-xs text-muted-foreground">{job.department}</p>
              )}
              <div className="flex items-center justify-center gap-1.5 flex-wrap">
                <CardTitle className="text-lg font-semibold hover:text-primary transition-colors break-words">
                  {job.title}
                </CardTitle>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info 
                        className="h-4 w-4 text-muted-foreground hover:text-primary cursor-pointer" 
                        onClick={handleInfoClick}
                      />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>View job details</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex flex-col flex-1 justify-between pt-0 pb-4">
          {/* Job Details (Left-aligned) */}
          <div className="flex flex-col gap-2 text-sm text-muted-foreground pl-4 pr-2">
            {/* Location */}
            <div className="flex items-center gap-1">
              <MapPin className="h-3.5 w-3.5 flex-shrink-0" />
              <span className="truncate">{formatWorkLocation(job.location)}</span>
            </div>
            {/* Office Location (if different from location) */}
            {job.office_location && job.office_location !== job.location && (
              <div className="flex items-center gap-1">
                <Briefcase className="h-3.5 w-3.5 flex-shrink-0" />
                <span className="truncate">{job.office_location}</span>
              </div>
            )}
            {/* Employment Type */}
            {job.employment_type && (
              <div className="flex items-center gap-1">
                <Users className="h-3.5 w-3.5 flex-shrink-0" />
                <span className="capitalize">{job.employment_type.replace('-', ' ')}</span>
              </div>
            )}
            {/* Salary Range */}
            <div className="flex items-center gap-1">
              <Tag className="h-3.5 w-3.5 flex-shrink-0" />
              <span>{formatSalary()}</span>
            </div>
            {/* Posted Date */}
            <div className="flex items-center gap-1">
              <Calendar className="h-3.5 w-3.5 flex-shrink-0" />
              <span>Posted {formatDate(job.created_at)}</span>
            </div>
          </div>

          {/* Status Badge (Centered) */}
          <div className="flex justify-center mt-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge
                    variant="outline"
                    className={getStatusColorClass(job.status)}
                  >
                    <span className="flex items-center gap-1">
                      <Info className="h-3 w-3" />
                      {capitalizedStatus}
                    </span>
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Job Status: {capitalizedStatus}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Candidate Count (Centered) */}
          <div className="flex justify-center mt-4">
            <div className="relative w-20 h-20 flex flex-col items-center justify-center">
              <div className={cn(
                "absolute w-full h-full rounded-full border-4 border-l-transparent border-r-transparent border-b-transparent",
                "border-blue-500"
              )} style={{ 
                transform: `rotate(${Math.min(candidateCount, 100) * 3.6}deg)` 
              }}></div>
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <span className="text-xl font-semibold">{candidateCount}</span>
                <Users className="h-5 w-5 text-muted-foreground mt-1" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Job Info Dialog */}
      <JobInfoDialog 
        job={job}
        open={infoDialogOpen}
        onOpenChange={setInfoDialogOpen}
      />
    </>
  );
}
