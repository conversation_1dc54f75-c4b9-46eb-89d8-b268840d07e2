import express from 'express';
import multer from 'multer';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { resumeController } from '../controllers/ResumeController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const upload = multer({ storage: multer.memoryStorage() });
const router = express.Router();

/**
 * @route   POST /api/resumes/upload
 * @desc    Upload a new resume
 * @access  Private
 */
router.post('/upload',
  authenticate,
  upload.single('resume'),
  asyncHandler(resumeController.uploadResume)
);

/**
 * @route   GET /api/resumes
 * @desc    Get all resumes with pagination
 * @access  Private
 */
router.get('/',
  authenticate,
  asyncHandler(resumeController.getResumes)
);

/**
 * @route   GET /api/resumes/:id
 * @desc    Get resume by ID
 * @access  Private
 */
router.get('/:id',
  authenticate,
  asyncHandler(resumeController.getResumeById)
);

/**
 * @route   DELETE /api/resumes/:id
 * @desc    Delete resume
 * @access  Private
 */
router.delete('/:id',
  authenticate,
  asyncHandler(resumeController.deleteResume)
);

export default router;