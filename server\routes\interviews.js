import express from 'express';
import { asyncHandler } from '../middleware/asyncHandler.js';
import { interview<PERSON>ontroller } from '../controllers/InterviewController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/interviews
 * @desc    Get all interviews with pagination
 * @access  Private
 */
router.get('/',
  authenticate,
  async<PERSON><PERSON><PERSON>(interviewController.getInterviews)
);

/**
 * @route   POST /api/interviews/schedule
 * @desc    Send interview invite emails to candidate, interviewer, and recruiter (by recruiter only)
 * @access  Private/Recruiter
 */
router.post('/schedule',
  authenticate,
  authorize(['admin', 'recruiter']),
  async<PERSON>andler(interviewController.scheduleInterview)
);

/**
 * @route   PUT /api/interviews/:id/reschedule
 * @desc    Reschedule an existing interview
 * @access  Private/Recruiter
 */
router.put('/:id/reschedule',
  authenticate,
  authorize(['admin', 'recruiter']),
  as<PERSON><PERSON><PERSON><PERSON>(interviewController.rescheduleInterview)
);

/**
 * @route   POST /api/interviews/:id/feedback
 * @desc    Submit interview feedback
 * @access  Private/Interviewer
 */
router.post('/:id/feedback',
  authenticate,
  authorize(['admin', 'recruiter', 'interviewer']),
  asyncHandler(interviewController.submitFeedback)
);

/**
 * @route   PUT /api/interviews/:id/approve
 * @desc    Approve a candidate for interview (send slots)
 * @access  Private/Recruiter
 */
router.put('/:id/approve',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(interviewController.approveCandidate)
);

/**
 * @route   PUT /api/interviews/:id/decline
 * @desc    Decline a candidate for interview (send rejection)
 * @access  Private/Recruiter
 */
router.put('/:id/decline',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(interviewController.declineCandidate)
);

/**
 * @route   POST /api/interviews/confirm-slot
 * @desc    Confirm a slot selection from candidate (from email form)
 * @access  Public (email link)
 */
router.post('/confirm-slot', asyncHandler(interviewController.confirmSlot));

/**
 * @route   POST /api/interviews/generate-meet-link
 * @desc    Generate a meet link for an interview and create the interview entry
 * @access  Private/Recruiter
 */
router.post('/generate-meet-link',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(interviewController.generateMeetLink)
);

/**
 * @route   PUT /api/interviews/:id/schedule-details
 * @desc    Update interview schedule details
 * @access  Private/Recruiter
 */
router.put('/:id/schedule-details',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(interviewController.updateScheduleDetails)
);

/**
 * @route   GET /api/interviews/candidate/:candidateId
 * @desc    Get interview details for a candidate by candidate ID
 * @access  Private
 */
router.get('/candidate/:candidateId',
  authenticate,
  asyncHandler(interviewController.getInterviewByCandidateId)
);

/**
 * @route   GET /api/interviews/:id
 * @desc    Get interview details by ID
 * @access  Private
 */
router.get('/:id',
  authenticate,
  asyncHandler(interviewController.getInterviewById)
);

export default router;