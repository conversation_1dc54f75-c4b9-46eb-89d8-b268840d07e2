export const messages = {
    // Candidates
    GET_CANDIDATES: 'Get candidates successfully',
    GET_CANDIDATE: 'Get candidate successfully',
    CREATE_CANDIDATE: 'Candidate created successfully',
    UPDATE_CANDIDATE: 'Candidate updated successfully',
    DELETE_CANDIDATE: 'Candidate deleted successfully',
    NO_DATA_PROVIDED_FOR_UPDATE: 'No data provided for update',
    CANDIDATE_NOT_FOUND: 'Candidate not found',
    // Google OAuth
    GOOGLE_OAUTH_MISSING_RECRUITER_ID: 'recruiter_id is required',
    GOOGLE_OAUTH_MISSING_CODE_OR_STATE: 'Missing code or state',
    GOOGLE_OAUTH_SAVE_SUCCESS: 'Google credentials saved successfully!',
    GOOGLE_OAUTH_SAVE_ERROR: 'Failed to save credentials',
    // Interview Approve/Decline
    INTERVIEW_APPROVE_SLOTS_SENT: 'Available slots sent to candidate',
    INTERVIEW_SLOT_SELECTED_EVENT_CREATED: 'Event created and Google Meet link sent to recruiter and candidate',
    INTERVIEW_DECLINE_REJECTION_SENT: 'Rejection email sent to candidate',
    INTERVIEW_SCHEDULED: 'Interview scheduled successfully',
    INTERVIEW_SCHEDULE_FAILED: 'Failed to schedule interview',
    INTERVIEW_RESCHEDULED: 'Interview rescheduled successfully',
    INTERVIEW_RESCHEDULE_FAILED: 'Failed to reschedule interview',
    INTERVIEW_DELETED: 'Interview deleted successfully',
    INTERVIEW_DELETE_FAILED: 'Failed to delete interview',
    INTERVIEW_FEEDBACK_UPDATED: 'Interview feedback updated successfully',
    INTERVIEW_FEEDBACK_UPDATE_FAILED: 'Failed to update interview feedback',
    INTERVIEW_RETRIEVED: 'Interview retrieved successfully',
    INTERVIEW_RETRIEVE_FAILED: 'No Interview found',
    INTERVIEWS_RETRIEVED: 'Interviews retrieved successfully',
    INTERVIEWS_RETRIEVE_FAILED: 'No Interviews Found',
    INTERVIEW_APPROVE_SUCCESS: 'Candidate approved for interview',
    INTERVIEW_APPROVE_FAILED: 'Failed to approve candidate',
    INTERVIEW_DECLINE_SUCCESS: 'Candidate declined for interview',
    INTERVIEW_DECLINE_FAILED: 'Failed to decline candidate',
    GOOGLE_OAUTH_CALLBACK_SUCCESS: 'Google OAuth callback successful',
    GOOGLE_OAUTH_CALLBACK_FAILED: 'Google OAuth callback failed',
    GOOGLE_CALENDAR_EVENT_SUCCESS: 'Google Calendar event created successfully',
    GOOGLE_CALENDAR_EVENT_FAILED: 'Failed to create Google Calendar event',
    GOOGLE_GMAIL_PROFILE_SUCCESS: 'Gmail profile retrieved successfully',
    GOOGLE_GMAIL_PROFILE_FAILED: 'Failed to retrieve Gmail profile',
    GOOGLE_CALENDAR_EVENTS_SUCCESS: 'Calendar events retrieved successfully',
    GOOGLE_CALENDAR_EVENTS_FAILED: 'Failed to retrieve calendar events',
    INTERVIEW_DETAILS_RETRIEVED: 'Interview details retrieved successfully',
    INTERVIEW_DETAILS_BY_CANDIDATE_RETRIEVED: 'Interview details retrieved for candidate',
}

export const validationMessages = {
    // Validation Messages
    PHONE_NUMBER_MIN: "Phone number must be at least 10 characters long",
    PHONE_NUMBER_MAX: "Phone number must be at most 10 characters long",
    INVALID_JOB_ID: "Invalid job id",
    NAME_REQUIRED: "Name is required",
    EMAIL_REQUIRED: "Email is required",
    EMAIL_INVALID: "Email is invalid",
    EMAIL_ALREADY_EXISTS: "Email already exists",
    PHONE_REQUIRED: "Phone is required",
    PHONE_INVALID: "Phone is invalid",
    SOURCE_REQUIRED: "Source is required",
}