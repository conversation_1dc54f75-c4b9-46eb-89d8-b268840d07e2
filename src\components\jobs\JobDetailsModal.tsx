import { Dialog, DialogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { JobDescription } from "@/components/jobs/JobDescription";
import { type Job } from "@/hooks/useJobs";

interface JobDetailsModalProps {
  job: Job | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function JobDetailsModal({ job, open, onOpenChange }: JobDetailsModalProps) {
  if (!job) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">Job Details</DialogTitle>
        </DialogHeader>
        <JobDescription job={job} expanded />
      </DialogContent>
    </Dialog>
  );
}