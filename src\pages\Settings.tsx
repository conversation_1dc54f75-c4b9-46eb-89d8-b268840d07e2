import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

// Custom hook to start Google OAuth
function useGoogleConnect() {
  const { user } = useAuth();
  const API_BASE_URL = import.meta.env.VITE_API_BASE;
  return (service: "calendar" | "gmail") => {
    if (!user?.id) return;
    window.open(
      `${API_BASE_URL}/google/auth?recruiter_id=${user.id}&service=${service}`,
      "_blank",
      "width=500,height=700"
    );
  };
}

const appCards = [
  {
    key: "calendar",
    icon: <img src="/Frame-1.svg" alt="Google Calendar" width={40} height={40} />,
    title: "Google Calendar",
    description: "Optimize meeting schedules with Google Calendar integration.",
    button: "Connect",
  },
  {
    key: "gmail",
    icon: <img src="/Frame.svg" alt="Gmail" width={40} height={40} />,
    title: "Gmail",
    description: "Connect your Gmail account to access emails and automate workflows.",
    button: "Connect",
  },
];

const Settings = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState(user?.full_name || "");
  const [language, setLanguage] = useState<string>(user?.language_preference || "en");
  const { toast } = useToast();
  const connectGoogle = useGoogleConnect();

  // Simulated connection status (replace with real API call in production)
  const [isCalendarConnected, setIsCalendarConnected] = useState(false);
  const [isGmailConnected, setIsGmailConnected] = useState(false);

  // Listen for OAuth popup postMessage
  useEffect(() => {
    function handleMessage(event: MessageEvent) {
      if (event.data && event.data.type === "GOOGLE_CONNECT") {
        if (event.data.status === "success") {
          toast({
            title: "Google account connected!",
            description: "Your Google account was connected successfully.",
          });
          if (event.data.service === "calendar") setIsCalendarConnected(true);
          if (event.data.service === "gmail") setIsGmailConnected(true);
          if (!event.data.service) {
            setIsCalendarConnected(true);
            setIsGmailConnected(true);
          }
        } else if (event.data.status === "error") {
          toast({
            title: "Google connection failed",
            description: event.data.msg || "There was an error connecting your Google account.",
          });
        }
      }
    }
    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, [toast]);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelClick = () => {
    setIsEditing(false);
    setDisplayName(user?.full_name || "");
    setLanguage(user?.language_preference || "en");
  };

  const handleUpdateProfile = async () => {
    // Validate language is one of the allowed values
    const validLanguage = ["en", "es", "nl"].includes(language)
      ? (language as "en" | "es" | "nl")
      : "en";
    // TODO: Implement API call to update user profile
    setIsEditing(false);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Settings</h1>
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Connected Apps</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          {appCards.map(card => (
            <div
              key={card.key}
              className="bg-white rounded-lg shadow p-6 flex flex-col items-center justify-between border border-gray-200"
            >
              <div className="mb-3">{card.icon}</div>
              <div className="font-semibold text-base mb-1">{card.title}</div>
              <div className="text-gray-600 text-sm mb-4 text-center">{card.description}</div>
              <Button
                onClick={() => connectGoogle(card.key as "calendar" | "gmail")}
                disabled={card.key === "calendar" ? isCalendarConnected : isGmailConnected}
              >
                {card.key === "calendar"
                  ? isCalendarConnected
                    ? "Connected"
                    : "Connect"
                  : isGmailConnected
                  ? "Connected"
                  : "Connect"}
              </Button>
            </div>
          ))}
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="displayName">Display Name</Label>
          <Input
            type="text"
            id="displayName"
            value={displayName}
            onChange={e => setDisplayName(e.target.value)}
            disabled={!isEditing}
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="language">Language</Label>
          <Select value={language} onValueChange={setLanguage} disabled={!isEditing}>
            <SelectTrigger className="w-full mt-1">
              <SelectValue placeholder="Select a language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="es">Spanish</SelectItem>
              <SelectItem value="nl">Dutch</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="mt-6">
        {!isEditing ? (
          <Button onClick={handleEditClick}>Edit Profile</Button>
        ) : (
          <div className="flex gap-2">
            <Button variant="secondary" onClick={handleUpdateProfile}>
              Save
            </Button>
            <Button variant="ghost" onClick={handleCancelClick}>
              Cancel
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Settings;
