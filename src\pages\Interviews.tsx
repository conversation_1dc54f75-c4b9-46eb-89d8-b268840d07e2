import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useInterviews } from "../hooks/useInterviews";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Calendar as CalendarIcon,
  Video,
  CheckCircle,
  Users,
  Search,
  Filter,
  Clock,
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FeedbackModal from "@/components/feedback/FeedbackModal";
import ViewFeedbackModal from "@/components/feedback/ViewFeedbackModal";
import ViewProfileModal from "@/components/interviews/ViewProfileModal";
import ScheduleInterview from "@/pages/ScheduleInterview";
import { useFeedback } from "@/hooks/useFeedback";

// Define types for our data based on API response
interface Interview {
  id: string;
  interviewer_id: string;
  feedback: string | null;
  stage: string;
  duration: number;
  location: string | null;
  special_requirements: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string | null;
  flag_active: boolean;
  flag_deleted: boolean;
  video_link: string | null;
  type: string;
  feedback_id: string | null;
  candidate_id: string;
  scheduled_date: string;
  scheduled_time: string;
  candidate: {
    id: string;
    name: string;
    email: string;
    phone: string;
    job_id: string;
    source: string;
    status: string;
    can_status: string;
    created_at: string;
    created_by: string;
    resume_url: string;
    updated_at: string;
    flag_active: boolean;
    flag_deleted: boolean;
  };
  interviewer: {
    id: string;
    email: string;
    full_name: string;
  };
  status: string;
}

const Interviews = () => {
  const navigate = useNavigate();
  const { interviews, fetchInterviews, loading, error } = useInterviews();
  const { getFeedback } = useFeedback();
  const [searchTerm, setSearchTerm] = useState("");
  const [positionFilter, setPositionFilter] = useState("all");
  const [interviewerFilter, setInterviewerFilter] = useState("all");
  const [selectedFeedback, setSelectedFeedback] = useState<any>();
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

  // Add state for profile modal
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<any>();
  const [isScheduleDrawerOpen, setIsScheduleDrawerOpen] = useState(false);
  // Create array of job titles from candidate.job field in interviews
  const jobTitles = [
    ...new Set(interviews.map(interview => interview.candidate?.job?.title).filter(Boolean)),
  ];

  // Create array of unique interviewer names from interviews
  const interviewerNames = [
    ...new Set(interviews.map(interview => interview.interviewer?.full_name).filter(Boolean)),
  ];

  useEffect(() => {
    fetchInterviews();
  }, [fetchInterviews]);

  // Filter interviews by job title and interviewer name
  let filteredInterviews =
    positionFilter === "all"
      ? interviews
      : interviews.filter(interview => interview.candidate?.job?.title === positionFilter);

  filteredInterviews =
    interviewerFilter === "all"
      ? filteredInterviews
      : filteredInterviews.filter(
          interview => interview.interviewer?.full_name === interviewerFilter
        );
  const upcomingInterviews = filteredInterviews.filter(
    interview => interview.status.toLowerCase() === "upcoming"
  );
  const pastInterviews = filteredInterviews.filter(
    interview => interview.status.toLowerCase() === "completed"
  );
  const pendingFeedback = filteredInterviews.filter(
    interview => interview.status.toLowerCase() === "feedback_pending"
  );

  const handleScheduleInterview = () => {
    setIsScheduleDrawerOpen(true);
  };

  const handleViewFeedback = async (interview: Interview) => {
    const feedback = await getFeedback(interview.id);
    const feedbackData=
    setSelectedFeedback(feedback.data.feedback);
    setIsFeedbackModalOpen(true);
  };

  // Add handler for viewing profile
  const handleViewProfile = (candidateId: string) => {
    setSelectedCandidate(candidateId);
    setIsProfileModalOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Interviews</h1>
          <p className="text-muted-foreground">Manage and schedule candidate interviews</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleScheduleInterview}>
            <CalendarIcon size={16} className="mr-2" />
            Schedule Interview
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search interviews..."
            className="pl-8"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2 w-full md:w-auto">
          <Select value={positionFilter} onValueChange={setPositionFilter}>
            <SelectTrigger className="w-full md:w-[180px]">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <span>{positionFilter === "all" ? "All Positions" : positionFilter}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Positions</SelectItem>
              {jobTitles.map(title => (
                <SelectItem key={title} value={title}>
                  {title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={interviewerFilter} onValueChange={setInterviewerFilter}>
            <SelectTrigger className="w-full md:w-[180px]">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>{interviewerFilter === "all" ? "All Interviewers" : interviewerFilter}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Interviewers</SelectItem>
              {interviewerNames.map(name => (
                <SelectItem key={name} value={name}>
                  {name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="upcoming">
        <TabsList className="mb-4">
          <TabsTrigger value="upcoming" className="flex items-center gap-2">
            Upcoming
            <span className="flex items-center justify-center rounded-full bg-blue-100 text-blue-900 text-xs font-medium px-2 py-0.5">
              {upcomingInterviews.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value="past" className="flex items-center gap-2">
            Past Interviews
            <span className="flex items-center justify-center rounded-full bg-gray-200 text-gray-900 text-xs font-medium px-2 py-0.5">
              {pastInterviews.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value="feedback" className="flex items-center gap-2">
            Pending Feedback
            <span className="flex items-center justify-center rounded-full bg-red-100 text-red-900 text-xs font-medium px-2 py-0.5">
              {pendingFeedback.length}
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">This Week's Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-24 bg-muted animate-pulse rounded-md" />
                  ))}
                </div>
              ) : (
                <div className="space-y-6">
                  {upcomingInterviews.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      No upcoming interviews scheduled
                    </div>
                  ) : (
                    // Group interviews by scheduled_date and render them
                    Object.entries(
                      upcomingInterviews.reduce((acc, interview) => {
                        const date = new Date(interview.scheduled_date).toLocaleDateString();
                        if (!acc[date]) acc[date] = [];
                        acc[date].push(interview);
                        return acc;
                      }, {} as Record<string, Interview[]>)
                    ).map(([date, dayInterviews]) => (
                      <div key={date}>
                        <h3 className="font-medium text-sm text-muted-foreground mb-3">{date}</h3>
                        <div className="space-y-3">
                          {dayInterviews.map(interview => (
                            <div
                              key={interview.id}
                              className="bg-muted/50 rounded-lg p-4 flex flex-col md:flex-row md:items-center justify-between gap-3"
                            >
                              <div>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline" className="bg-blue-50 text-blue-700">
                                    {interview.scheduled_time}
                                  </Badge>
                                  <Badge variant="outline" className="bg-green-50 text-green-700">
                                    {interview.stage}
                                  </Badge>
                                </div>
                                <h4 className="font-medium mt-2">
                                  {interview.candidate.name} - {interview.candidate.job.title}
                                </h4>
                                <p className="text-sm text-muted-foreground mt-1">
                                  Interviewer: {interview.interviewer.full_name}
                                </p>
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleViewProfile(interview.candidate)}
                                >
                                  View Profile
                                </Button>
                                {interview.video_link && (
                                  <Button
                                    size="sm"
                                    onClick={() => window.open(interview.video_link, "_blank")}
                                  >
                                    <Video className="h-4 w-4 mr-1" />
                                    Join Meeting
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="past">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Past Interviews</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pastInterviews.map(interview => (
                  <div
                    key={interview.id}
                    className="bg-slate-50 rounded-lg p-4 flex flex-col md:flex-row md:items-center justify-between gap-3"
                  >
                    <div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-gray-50 text-gray-700">
                          {new Date(interview.scheduled_date).toLocaleDateString()}
                        </Badge>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          {interview.stage}
                        </Badge>
                      </div>
                      <h4 className="font-medium mt-2">
                        {interview.candidate.name} - {interview.candidate.job.title}
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Interviewer: {interview.interviewer.full_name}
                      </p>
                    </div>
                    <div className="flex gap-2 items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewProfile(interview.candidate)}
                      >
                        View Profile
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewFeedback(interview)}
                      >
                        View Feedback
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="feedback">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Pending Feedback</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pendingFeedback.map(interview => (
                  <div
                    key={interview.id}
                    className="bg-muted/50 rounded-lg p-4 flex flex-col md:flex-row md:items-center justify-between gap-3"
                  >
                    <div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-red-50 text-red-700">
                          Due Today
                        </Badge>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          {interview.stage}
                        </Badge>
                      </div>
                      <h4 className="font-medium mt-2">
                        {interview.candidate.name} - {interview.candidate.job.title}
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Interviewed: {new Date(interview.scheduled_date).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewProfile(interview.candidate)}
                      >
                        View Profile
                      </Button>
                      <FeedbackModal
                        interview={interview}
                        trigger={<Button size="sm">Submit Feedback</Button>}
                        useFullPage={true}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* View Feedback Modal */}
      <ViewFeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        feedback={selectedFeedback}
      />

      {/* View Profile Modal */}
      <ViewProfileModal
        isOpen={isProfileModalOpen && !!selectedCandidate}
        onClose={() => {
          setIsProfileModalOpen(false);
          setSelectedCandidate(undefined);
        }}
        candidate={selectedCandidate}
        key={selectedCandidate || "profile-modal"}
      />

      {/* Schedule Interview Drawer */}
      <ScheduleInterview isOpen={isScheduleDrawerOpen} onOpenChange={setIsScheduleDrawerOpen} />
    </div>
  );
};

export default Interviews;
