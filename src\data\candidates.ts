
import { CandidateStatus } from "@/hooks/useCandidates";

// Candidates data model (mimics a database table)

export interface Candidate {
  id: string;
  name: string;
  jobTitle: string;
  jobId: string; // Reference to job ID for relations
  location: string;
  status: CandidateStatus;
  matchScore: number;
  appliedDate: string;
  lastUpdated: string;
  skills: string[];
  email: string;
  phone: string;
  experience?: string;
  education?: string[];
  resumeUrl?: string;
  source?: string;
  notes?: string;
  photo?: string;
  suitability_summary: string;
  unsuitability_summary: string;
  latest_stage: string;
}

// Mock candidates data
export const candidates: Candidate[] = [
  {
    id: '1',
    name: '<PERSON>',
    jobTitle: 'Senior Frontend Developer',
    jobId: '1',
    location: 'San Francisco, CA',
    status: CandidateStatus.Interview,
    matchScore: 89,
    appliedDate: '2023-05-05',
    lastUpdated: '2023-05-10',
    skills: ['React', 'TypeScript', 'CSS', 'Node.js'],
    email: '<EMAIL>',
    phone: '******-555-1234',
    experience: '7 years of frontend development experience',
    education: ['BS in Computer Science, Stanford University'],
    resumeUrl: 'https://example.com/resume.pdf',
    source: 'LinkedIn',
    photo: 'https://i.pravatar.cc/150?img=1',
    suitability_summary: 'Strong React and TypeScript skills. Experience with Node.js is a plus.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Interview Scheduled',
  },
  {
    id: '2',
    name: 'Michael Chen',
    jobTitle: 'Product Designer',
    jobId: '2',
    location: 'New York, NY',
    status: CandidateStatus.Screening,
    matchScore: 76,
    appliedDate: '2023-05-07',
    lastUpdated: '2023-05-09',
    skills: ['UI/UX', 'Figma', 'User Research', 'Prototyping'],
    email: '<EMAIL>',
    phone: '******-555-5678',
    experience: '5 years in product design',
    education: ['MFA in Design, RISD'],
    source: 'Indeed',
    photo: 'https://i.pravatar.cc/150?img=11',
    suitability_summary: 'Proficient in Figma and user research. Strong prototyping skills.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Resume Reviewed',
  },
  {
    id: '3',
    name: 'Sophia Garcia',
    jobTitle: 'DevOps Engineer',
    jobId: '3',
    location: 'Austin, TX',
    status: CandidateStatus.Applied,
    matchScore: 94,
    appliedDate: '2023-05-10',
    lastUpdated: '2023-05-10',
    skills: ['AWS', 'Kubernetes', 'Docker', 'CI/CD'],
    email: '<EMAIL>',
    phone: '******-555-9012',
    experience: '4 years of DevOps engineering',
    education: ['MS in Computer Engineering, UT Austin'],
    source: 'Referral',
    photo: 'https://i.pravatar.cc/150?img=5',
    suitability_summary: 'Excellent AWS and Kubernetes experience. Strong CI/CD pipeline knowledge.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Application Received',
  },
  {
    id: '4',
    name: 'David Kim',
    jobTitle: 'Product Manager',
    jobId: '4',
    location: 'Seattle, WA',
    status: CandidateStatus.Offer,
    matchScore: 92,
    appliedDate: '2023-04-25',
    lastUpdated: '2023-05-11',
    skills: ['Product Strategy', 'Agile', 'User Stories', 'Roadmapping'],
    email: '<EMAIL>',
    phone: '******-555-3456',
    experience: '8 years in product management',
    education: ['MBA, University of Washington'],
    source: 'LinkedIn',
    photo: 'https://i.pravatar.cc/150?img=12',
    suitability_summary: 'Strong product strategy and agile methodology experience. Excellent at roadmapping.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Offer Extended',
  },
  {
    id: '5',
    name: 'Sarah Martinez',
    jobTitle: 'Backend Developer',
    jobId: '5',
    location: 'Chicago, IL',
    status: CandidateStatus.Interview,
    matchScore: 85,
    appliedDate: '2023-05-03',
    lastUpdated: '2023-05-08',
    skills: ['Python', 'Django', 'SQL', 'API Design'],
    email: '<EMAIL>',
    phone: '******-555-7890',
    experience: '6 years of backend development',
    education: ['BS in Computer Science, University of Illinois'],
    source: 'Direct Application',
    photo: 'https://i.pravatar.cc/150?img=24',
    suitability_summary: 'Proficient in Python/Django and SQL. Strong API design skills.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Interview Scheduled',
  },
  {
    id: '6',
    name: 'James Wilson',
    jobTitle: 'Data Analyst',
    jobId: '6',
    location: 'Boston, MA',
    status: CandidateStatus.Applied,
    matchScore: 72,
    appliedDate: '2023-05-09',
    lastUpdated: '2023-05-09',
    skills: ['SQL', 'Python', 'Tableau', 'Excel'],
    email: '<EMAIL>',
    phone: '******-555-2345',
    experience: '3 years in data analysis',
    education: ['MS in Data Science, Boston University'],
    source: 'Indeed',
    photo: 'https://i.pravatar.cc/150?img=15',
    suitability_summary: 'Good SQL and Python skills for data analysis. Experience with Tableau.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Application Received',
  },
  {
    id: '7',
    name: 'Lisa Taylor',
    jobTitle: 'Senior Frontend Developer',
    jobId: '1',
    location: 'Remote',
    status: CandidateStatus.Screening,
    matchScore: 81,
    appliedDate: '2023-05-06',
    lastUpdated: '2023-05-08',
    skills: ['JavaScript', 'React', 'GraphQL', 'CSS'],
    email: '<EMAIL>',
    phone: '******-555-6789',
    experience: '5 years of frontend development',
    education: ['BS in Web Development, Full Sail University'],
    source: 'LinkedIn',
    photo: 'https://i.pravatar.cc/150?img=29',
    suitability_summary: 'Strong JavaScript and React skills. Experience with GraphQL.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Resume Reviewed',
  },
  {
    id: '8',
    name: 'Robert Brown',
    jobTitle: 'Backend Developer',
    jobId: '5',
    location: 'Denver, CO',
    status: CandidateStatus.Rejected,
    matchScore: 65,
    appliedDate: '2023-04-28',
    lastUpdated: '2023-05-06',
    skills: ['Java', 'Spring', 'Microservices', 'SQL'],
    email: '<EMAIL>',
    phone: '******-555-0123',
    experience: '4 years of Java development',
    education: ['BS in Computer Science, Colorado State University'],
    source: 'Indeed',
    photo: 'https://i.pravatar.cc/150?img=18',
    suitability_summary: 'Proficient in Java and Spring. Experience with microservices.',
    unsuitability_summary: 'Limited experience with Python, which is preferred for this role.',
    latest_stage: 'Rejected',
  },
  {
    id: '9',
    name: 'Jennifer Lopez',
    jobTitle: 'DevOps Engineer',
    jobId: '3',
    location: 'Portland, OR',
    status: CandidateStatus.Hired,
    matchScore: 90,
    appliedDate: '2023-04-20',
    lastUpdated: '2023-05-12',
    skills: ['AWS', 'Terraform', 'Linux', 'Python'],
    email: '<EMAIL>',
    phone: '******-555-4567',
    experience: '7 years in cloud infrastructure',
    education: ['MS in Information Systems, Portland State University'],
    source: 'Referral',
    photo: 'https://i.pravatar.cc/150?img=9',
    suitability_summary: 'Extensive experience with AWS and Terraform. Strong Linux and Python skills.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Hired',
  },
  {
    id: '10',
    name: 'Daniel Smith',
    jobTitle: 'Product Designer',
    jobId: '2',
    location: 'Los Angeles, CA',
    status: CandidateStatus.Interview,
    matchScore: 88,
    appliedDate: '2023-05-02',
    lastUpdated: '2023-05-09',
    skills: ['Figma', 'Adobe XD', 'Wireframing', 'Visual Design'],
    email: '<EMAIL>',
    phone: '******-555-8901',
    experience: '6 years in UI/UX design',
    education: ['BA in Graphic Design, RISD'],
    source: 'Direct Application',
    photo: 'https://i.pravatar.cc/150?img=14',
    suitability_summary: 'Strong Figma and Adobe XD skills. Experienced in wireframing and visual design.',
    unsuitability_summary: 'No significant unsuitability.',
    latest_stage: 'Interview Scheduled',
  },
];

// Helper function to get a candidate by ID
export const getCandidateById = (id: string): Candidate | undefined => {
  return candidates.find(candidate => candidate.id === id);
};

// Helper function to get candidates by job ID
export const getCandidatesByJobId = (jobId: string): Candidate[] => {
  return candidates.filter(candidate => candidate.jobId === jobId);
};

// Helper function to get candidates by status
export const getCandidatesByStatus = (status: CandidateStatus): Candidate[] => {
  return candidates.filter(candidate => candidate.status === status);
};
