import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/components/ui/sonner';
import { API_CONFIG } from '@/utils/Constant';

// Define the structure of an interviewer as received from your backend API
export interface BackendInterviewerDetail {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  phone: string;
  job_title: string;
  department: string;
  location: string;
  employee_id: string;
  employment_type: string;
  designation: string;
  join_date: string; // Date string from backend
  areas_of_expertise: string[];
  bio: string;
  education_degree?: string;
  education_institution?: string;
  education_year?: string;
  work_history_position?: string;
  work_history_company?: string;
  work_history_duration?: string;
  is_available: boolean;
  interview_types: string[];
  max_interviews_per_day: number;
  preferred_time_slots: Record<string, string[]>;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  flag_active: boolean;
  flag_deleted: boolean;
}

// For the frontend display, we might need a slightly different structure
// This maps backend data to what Interviewer<PERSON><PERSON>/Drawer currently expects
export interface InterviewerDetail {
  id: string;
  name: string; // Maps to full_name
  email: string;
  phone: string;
  title: string; // Maps to job_title
  department: string;
  location: string;
  expertise: string[]; // Maps to areas_of_expertise
  availability: { day: string; slots: string[] }[]; // This needs transformation
  totalInterviews: number; // Placeholder, need to fetch from backend or derive
  upcomingInterviews: number; // Placeholder
  bio: string;
  joinDate: string; // Date string from backend
  lastActive: string; // Placeholder
  employeeId: string;
  employmentType: string;
  designation: string;
  education: { degree: string; institution: string; year: string }[];
  workHistory: { position: string; company: string; duration: string }[];
  isAvailable: boolean;
  maxInterviewsPerDay: number;
  interviewTypes: string[];
}

// Helper to transform backend data to frontend display format
const transformToFrontendInterviewer = (backendInterviewer: BackendInterviewerDetail): InterviewerDetail => {
  console.log('Transforming backend interviewer:', backendInterviewer);

  const educationArray = [];
  if (backendInterviewer.education_degree || backendInterviewer.education_institution || backendInterviewer.education_year) {
    educationArray.push({
      degree: backendInterviewer.education_degree || '',
      institution: backendInterviewer.education_institution || '',
      year: backendInterviewer.education_year || '',
    });
  }

  const workHistoryArray = [];
  if (backendInterviewer.work_history_position || backendInterviewer.work_history_company || backendInterviewer.work_history_duration) {
    workHistoryArray.push({
      position: backendInterviewer.work_history_position || '',
      company: backendInterviewer.work_history_company || '',
      duration: backendInterviewer.work_history_duration || '',
    });
  }

  const availabilityArray = Object.entries(backendInterviewer.preferred_time_slots || {}).map(([day, slots]) => ({
    day: day.charAt(0).toUpperCase() + day.slice(1), // Capitalize day name
    slots: slots,
  }));
  console.log('Transformed availabilityArray:', availabilityArray);

  return {
    id: backendInterviewer.id,
    name: backendInterviewer.full_name,
    email: backendInterviewer.email,
    phone: backendInterviewer.phone || '',
    title: backendInterviewer.job_title || '',
    department: backendInterviewer.department || '',
    location: backendInterviewer.location || '',
    expertise: backendInterviewer.areas_of_expertise || [],
    availability: availabilityArray,
    totalInterviews: 0, // Placeholder
    upcomingInterviews: 0, // Placeholder
    bio: backendInterviewer.bio || '',
    joinDate: backendInterviewer.join_date || '',
    lastActive: new Date().toISOString().split('T')[0], // Placeholder
    employeeId: backendInterviewer.employee_id || '',
    employmentType: backendInterviewer.employment_type || '',
    designation: backendInterviewer.designation || '',
    education: educationArray,
    workHistory: workHistoryArray,
    isAvailable: backendInterviewer.is_available,
    maxInterviewsPerDay: backendInterviewer.max_interviews_per_day,
    interviewTypes: backendInterviewer.interview_types,
  };
};

export const useInterviewers = () => {
  const [interviewers, setInterviewers] = useState<InterviewerDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to get the auth token (replace with your actual auth logic)
  const getAuthToken = useCallback(() => {
    // FIXED: Use 'token' as the localStorage key
    return localStorage.getItem('token');
  }, []);

  const fetchInterviewers = useCallback(async () => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWERS}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch interviewers');
      }

      const data = await response.json();
      setInterviewers(data.interviewers.map(transformToFrontendInterviewer)); // Transform data
    } catch (err: any) {
      setError(err.message);
      toast.error('Error', {
        description: `Failed to load interviewers: ${err.message}`,
      });
    } finally {
      setLoading(false);
    }
  }, [getAuthToken]);

  const addInterviewer = useCallback(async (newInterviewerData: any) => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return { success: false, message: 'Authentication token not found.' };
    }

    try {
      // Ensure join_date is in YYYY-MM-DD format for backend if it's a Date object
      if (newInterviewerData.join_date instanceof Date) {
        newInterviewerData.join_date = newInterviewerData.join_date.toISOString().split('T')[0];
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWERS}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(newInterviewerData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add interviewer');
      }

      const data = await response.json();
      toast.success('Interviewer Added', {
        description: `${data.interviewer.full_name} has been added.`,
      });
      fetchInterviewers(); // Re-fetch to update the list
      return { success: true, interviewer: transformToFrontendInterviewer(data.interviewer) };
    } catch (err: any) {
      setError(err.message);
      toast.error('Error', {
        description: `Failed to add interviewer: ${err.message}`,
      });
      return { success: false, message: err.message };
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, fetchInterviewers]);

  const deleteInterviewer = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return { success: false, message: 'Authentication token not found.' };
    }

    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWERS}/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete interviewer');
      }

      toast.success('Interviewer Deleted', {
        description: 'Interviewer has been removed.',
      });
      fetchInterviewers(); // Re-fetch to update the list
      return { success: true };
    } catch (err: any) {
      setError(err.message);
      toast.error('Error', {
        description: `Failed to delete interviewer: ${err.message}`,
      });
      return { success: false, message: err.message };
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, fetchInterviewers]);

  const updateInterviewer = useCallback(async (id: string, updatedData: Partial<BackendInterviewerDetail>) => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return { success: false, message: 'Authentication token not found.' };
    }

    try {
      console.log('Sending update request with data:', updatedData);

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWERS}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updatedData),
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Update failed:', data);
        throw new Error(data.message || 'Failed to update interviewer');
      }

      console.log('Update successful, data from backend:', data.interviewer); // Debug log
      
      // Use setTimeout to avoid state updates during render
      setTimeout(() => {
        toast.success('Interviewer Updated', {
          description: `${data.interviewer.full_name} has been updated.`,
        });
        fetchInterviewers(); // Re-fetch to update the list
      }, 0);

      return { success: true, interviewer: transformToFrontendInterviewer(data.interviewer) };
    } catch (err: any) {
      console.error('Update error:', err);
      setError(err.message);
      // Don't show toast here, let the component handle it
      return { success: false, message: err.message };
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, fetchInterviewers]);

  useEffect(() => {
    fetchInterviewers();
  }, [fetchInterviewers]);

  return { interviewers, loading, error, addInterviewer, deleteInterviewer, updateInterviewer, fetchInterviewers };
}; 