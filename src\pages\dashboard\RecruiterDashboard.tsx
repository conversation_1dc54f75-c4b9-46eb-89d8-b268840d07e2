import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { StatsCard } from "@/components/dashboard/StatsCard";
import { ActivityItem } from "@/components/dashboard/ActivityItem";
import { AICard } from "@/components/dashboard/AICard";
import { toast } from "@/components/ui/sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Users, Briefcase, Calendar, Clock, Sparkles } from "lucide-react";
import { useAuth } from "@/components/auth/AuthProvider";
import { AddCandidateModal } from "@/components/candidates/AddCandidateModal";
import { Link } from "react-router-dom";
// Remove the NewJob import
// import NewJob from "../NewJob";

const RecruiterDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const handleGenerateJobDescription = () => {
    toast.success("Opening Job Description Generator", {
      description:
        "This feature would open the AI job description generator tool.",
    });
  };

  const handleParseResume = () => {
    toast.success("Opening Resume Parser", {
      description: "This feature would open the AI resume parsing tool.",
    });
  };

  const handleJobCreated = () => {
    toast.success("Job posted successfully", {
      description: "Your new job posting has been created and is now live.",
    });
  };

  const handleCandidateAdded = () => {
    toast.success("Candidate added successfully", {
      description: "The new candidate has been added to the system.",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.full_name || "Recruiter"}!
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link to="/jobs/new">
              <Briefcase size={16} className="mr-2" />
              Post New Job
            </Link>
          </Button>
          <AddCandidateModal
            onCandidateAdded={handleCandidateAdded}
            icon={Users}
            buttonText="Add Candidate"
            buttonVariant="secondary"
          />
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Active Jobs"
          value={loading ? "..." : "12"}
          description="2 need attention"
          icon={<Briefcase size={16} />}
        />
        <StatsCard
          title="New Candidates"
          value={loading ? "..." : "48"}
          trend="up"
          trendValue="12% from last week"
          icon={<Users size={16} />}
        />
        <StatsCard
          title="Interviews this Week"
          value={loading ? "..." : "28"}
          description="6 scheduled today"
          icon={<Calendar size={16} />}
        />
        <StatsCard
          title="Average Time to Hire"
          value={loading ? "..." : "18 days"}
          trend="down"
          trendValue="3 days improvement"
          icon={<Clock size={16} />}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-7">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest recruiting activities</CardDescription>
          </CardHeader>
          <CardContent className="px-2">
            {loading ? (
              <div className="space-y-2">
                <div className="h-12 bg-muted animate-pulse rounded-md" />
                <div className="h-12 bg-muted animate-pulse rounded-md" />
                <div className="h-12 bg-muted animate-pulse rounded-md" />
              </div>
            ) : (
              <div className="space-y-0">
                <ActivityItem
                  avatarFallback="JD"
                  title={
                    <>
                      Interview scheduled with <strong>Julie Davis</strong> for
                      Senior Developer
                    </>
                  }
                  description="Tomorrow at 10:00 AM"
                  timestamp="Just now"
                  status="success"
                />
                <ActivityItem
                  avatarFallback="RM"
                  title={
                    <>
                      Resume matched for <strong>Robert Ming</strong>
                    </>
                  }
                  description="95% match for Frontend Developer"
                  timestamp="2 hours ago"
                  status="warning"
                />
                <ActivityItem
                  avatarFallback="SD"
                  title={
                    <>
                      Feedback received from <strong>Sarah Dayan</strong>
                    </>
                  }
                  description="Technical interview for UX Designer"
                  timestamp="Yesterday"
                  status="default"
                />
                <ActivityItem
                  avatarFallback="TK"
                  title={
                    <>
                      Offer accepted by <strong>Tim Kirkland</strong>
                    </>
                  }
                  description="Starting as Product Manager on June 15"
                  timestamp="2 days ago"
                  status="success"
                />
              </div>
            )}
          </CardContent>
        </Card>
        <div className="md:col-span-3 grid gap-4">
          <AICard
            title="Job Description Generator"
            description="Generate compelling job descriptions from keywords and requirements"
            action={handleGenerateJobDescription}
            actionLabel="Generate Description"
            icon={<Sparkles size={18} className="text-violet-500" />}
          />
          <AICard
            title="Resume Parser"
            description="Extract key information from resumes automatically"
            action={handleParseResume}
            actionLabel="Parse Resume"
            icon={<Sparkles size={18} className="text-violet-500" />}
          />
        </div>
      </div>

      <Tabs defaultValue="upcoming">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Your Tasks</h2>
          <TabsList>
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="assigned">Assigned</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="upcoming" className="mt-4">
          {loading ? (
            <div className="space-y-2">
              <div className="h-12 bg-muted animate-pulse rounded-md" />
              <div className="h-12 bg-muted animate-pulse rounded-md" />
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Review Frontend Developer candidates
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-muted-foreground">Due today</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    4 candidates require evaluation
                  </p>
                  <div className="mt-2 flex justify-end">
                    <Button size="sm" variant="default">
                      Start Review
                    </Button>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Schedule interviews for UX Designer
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-muted-foreground">Due tomorrow</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    2 candidates awaiting scheduling
                  </p>
                  <div className="mt-2 flex justify-end">
                    <Button size="sm" variant="default">
                      Schedule
                    </Button>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Update Product Manager job posting
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-muted-foreground">Due in 3 days</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Add revised requirements
                  </p>
                  <div className="mt-2 flex justify-end">
                    <Button size="sm" variant="default">
                      Edit Post
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="assigned" className="mt-4">
          <div className="text-center text-muted-foreground py-8">
            No tasks currently assigned to others
          </div>
        </TabsContent>

        <TabsContent value="completed" className="mt-4">
          <div className="text-center text-muted-foreground py-8">
            Recently completed tasks will appear here
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RecruiterDashboard;
