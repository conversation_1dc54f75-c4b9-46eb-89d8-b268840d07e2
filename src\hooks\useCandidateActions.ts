import { useState } from 'react';
import { USE_CANDIDATES_CONSTANTS, API_CONFIG } from '@/utils/Constant';
import { useToast } from '@/components/ui/use-toast';

const { AUTH_ERROR, ACCEPT_SUCCESS, ACCEPT_ERROR, DECLINE_SUCCESS, DECLINE_ERROR } = USE_CANDIDATES_CONSTANTS.TOAST;

export const useCandidateActions = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const getAuthToken = () => localStorage.getItem('token');

  const acceptCandidate = async (candidateId: string) => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError(AUTH_ERROR);
      toast({ variant: 'destructive', title: AUTH_ERROR });
      setLoading(false);
      return false;
    }
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWS}/${candidateId}/approve`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      const result = await response.json();
      if (!response.ok || result.success === false) {
        throw new Error(result.message || ACCEPT_ERROR);
      }
      toast({ variant: 'default', title: ACCEPT_SUCCESS });
      return true;
    } catch (err: any) {
      setError(err.message || ACCEPT_ERROR);
      toast({ variant: 'destructive', title: ACCEPT_ERROR, description: err.message });
      return false;
    } finally {
      setLoading(false);
    }
  };

  const declineCandidate = async (candidateId: string) => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError(AUTH_ERROR);
      toast({ variant: 'destructive', title: AUTH_ERROR });
      setLoading(false);
      return false;
    }
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWS}/${candidateId}/decline`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      const result = await response.json();
      if (!response.ok || result.success === false) {
        throw new Error(result.message || DECLINE_ERROR);
      }
      toast({ variant: 'default', title: DECLINE_SUCCESS });
      return true;
    } catch (err: any) {
      setError(err.message || DECLINE_ERROR);
      toast({ variant: 'destructive', title: DECLINE_ERROR, description: err.message });
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { acceptCandidate, declineCandidate, loading, error };
}; 