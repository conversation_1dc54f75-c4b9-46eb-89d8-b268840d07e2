import axios from 'axios';
import { logger } from './logger.js';

const aiClient = axios.create({
  baseURL: process.env.AI_SERVICE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': process.env.AI_SERVICE_API_KEY
  }
});

aiClient.interceptors.response.use(
  response => response.data,
  error => {
    logger.error('AI Service request failed:', error);
    throw new Error(error.response?.data?.message || 'AI Service request failed');
  }
);

export const aiClientUtils = {
  async callResumeParser(fileUrl) {
    return aiClient.post('/parse-resume', { file_url: fileUrl });
  },

  async callJDGenerator(prompt) {
    return aiClient.post('/generate-jd', { prompt });
  },

  async getMatchScore(jobDescription, resumeData) {
    return aiClient.post('/match-score', {
      job_description: jobDescription,
      resume_data: resumeData
    });
  },

  async getSalarySuggestion(jobId, candidateId) {
    return aiClient.post('/recommend-salary', {
      job_id: jobId,
      candidate_id: candidateId
    });
  }
};