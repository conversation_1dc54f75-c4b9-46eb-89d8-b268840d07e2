import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { AddCandidateModal } from "@/components/candidates/AddCandidateModal";
import { BulkUploadCandidatesModal } from "@/components/candidates/BulkUploadCandidatesModal";
import {
  Search,
  Filter,
  SlidersHorizontal,
  Upload,
  Calendar,
  ArrowUpDown,
  Users,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  CandidateDrawer,
  CandidateDetail,
  Interview,
} from "@/components/candidates/CandidateDrawer/CandidateDrawer";
import { cn } from "@/lib/utils";
import { candidates as allCandidates, Candidate } from "@/data/candidates";
import { CandidateStatus } from "@/hooks/useCandidates";
import { getInterviewsForCandidate } from "@/data/relations";
import { useCandidates } from "@/hooks/useCandidates";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
} from "@/components/ui/pagination";
import { InterviewStage } from "@/hooks/useInterviews";

const Candidates = () => {
  const [loading, setLoading] = useState(true);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortField, setSortField] = useState<keyof Candidate>("lastUpdated");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  // State for candidate drawer
  const [selectedCandidate, setSelectedCandidate] = useState<CandidateDetail | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const [activeInterviewStageTab, setActiveInterviewStageTab] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const { fetchAllCandidates, loading: useCandidatesLoading, error } = useCandidates();

  useEffect(() => {
    let isMounted = true;
    const loadCandidates = async () => {
      const result = await fetchAllCandidates();
      if (isMounted && result && result.candidates) {
        setCandidates(result.candidates);
      } else if (isMounted) {
        setCandidates([]);
      }
      setLoading(false);
    };
    loadCandidates();
    return () => {
      isMounted = false;
    };
  }, []);

  const updateSelectedCandidate = (candidate: CandidateDetail | null) => {
    setSelectedCandidate(candidate);
  };

  const handleParseResume = () => {
    toast.success("Parse Resume", {
      description: "This would open the resume upload and parsing tool",
    });
  };

  const handleMatchResume = () => {
    toast.success("Match Resume", {
      description: "This would open the resume matching tool to find the best job fit",
    });
  };

  const handleAddCandidate = () => {
    toast.success("Add Candidate", {
      description: "This would open the candidate creation form",
    });
  };

  const handleViewCandidate = (id: string) => {
    // Find the candidate by ID
    const candidate = candidates.find(c => c.id === id);
    if (!candidate) return;

    // Get interviews for this candidate
    const interviews = getInterviewsForCandidate(id);

    // Create the detailed candidate data
    const detailedCandidate: CandidateDetail = {
      ...candidate,
      interviews: interviews.map(interview => ({
        round: parseInt(interview.id),
        title: interview.stage,
        date: interview.date,
        interviewer: "Various interviewers", // This would ideally map to interviewer names
        feedback: interview.feedback || "Pending feedback",
        result: interview.status === "completed" ? "pass" : "pending",
      })),
      // Ensure required properties have default values if they might be undefined
      experience: candidate.experience || "",
      education: candidate.education || [], // Make sure education has a default value
      source: candidate.source || "Direct Application", // Ensure source has a default value
    };

    setSelectedCandidate(detailedCandidate);
    setDrawerOpen(true);
  };

  // Filter candidates by search query and active tab
  const filteredCandidates = candidates.filter(candidate => {
    // Filter by search query
    const matchesSearch =
      searchQuery.length === 0 ||
      candidate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.jobTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));

    // Filter by active tab
    const matchesTab =
      activeTab === "all" ||
      candidate.status === CandidateStatus[activeTab as keyof typeof CandidateStatus];

    return matchesSearch && matchesTab;
  });

  // Filter candidates by status
  const appliedCandidates = candidates.filter(
    candidate => candidate.status === CandidateStatus.Applied
  );
  const screeningCandidates = candidates.filter(
    candidate => candidate.status === CandidateStatus.Screening
  );
  const interviewCandidates = candidates.filter(
    candidate => candidate.status === CandidateStatus.Interview
  );
  const offerCandidates = candidates.filter(
    candidate => candidate.status === CandidateStatus.Offer
  );
  const hiredCandidates = candidates.filter(
    candidate => candidate.status === CandidateStatus.Hired
  );
  const rejectedCandidates = candidates.filter(
    candidate => candidate.status === CandidateStatus.Rejected
  );

  const technicalRoundCandidates = interviewCandidates.filter(
    candidate =>
      candidate.latest_stage === InterviewStage.Technical || candidate.latest_stage === "technical"
  );
  const firstRoundCandidates = interviewCandidates.filter(
    candidate => candidate.latest_stage === InterviewStage.FirstRound
  );
  const secondRoundCandidates = interviewCandidates.filter(
    candidate => candidate.latest_stage === InterviewStage.SecondRound
  );
  const thirdRoundCandidates = interviewCandidates.filter(
    candidate => candidate.latest_stage === InterviewStage.ThirdRound
  );
  const finalRoundCandidates = interviewCandidates.filter(
    candidate => candidate.latest_stage === InterviewStage.FinalRound
  );
  const hrRoundCandidates = interviewCandidates.filter(
    candidate => candidate.latest_stage === InterviewStage.HRRound
  );
  const practicalRoundCandidates = interviewCandidates.filter(
    candidate => candidate.latest_stage === InterviewStage.PracticalRound
  );

  // Sort candidates
  const sortedCandidates = [...filteredCandidates].sort((a, b) => {
    if (sortField === "matchScore") {
      return sortDirection === "desc" ? b.matchScore - a.matchScore : a.matchScore - b.matchScore;
    }
    if (sortField === "appliedDate" || sortField === "lastUpdated") {
      const dateA = new Date(a[sortField]).getTime();
      const dateB = new Date(b[sortField]).getTime();
      return sortDirection === "desc" ? dateB - dateA : dateA - dateB;
    }
    const valueA = a[sortField]?.toString().toLowerCase() || "";
    const valueB = b[sortField]?.toString().toLowerCase() || "";
    return sortDirection === "desc" ? valueB.localeCompare(valueA) : valueA.localeCompare(valueB);
  });

  // Create date formatter
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "short",
      day: "numeric",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Toggle sort direction
  const toggleSort = (field: keyof Candidate) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // Count candidates by status
  const getCandidateCountByStatus = (status: string) => {
    if (status === "all") {
      return candidates.length;
    } else {
      return candidates.filter(
        c => c.status === CandidateStatus[status as keyof typeof CandidateStatus]
      ).length;
    }
  };

  const renderInterviewCandidates = (stage: InterviewStage | "all") => {
    if (stage === "all") {
      return interviewCandidates;
    }
    switch (stage) {
      case InterviewStage.Technical:
        return technicalRoundCandidates;
      case InterviewStage.FirstRound:
        return firstRoundCandidates;
      case InterviewStage.SecondRound:
        return secondRoundCandidates;
      case InterviewStage.ThirdRound:
        return thirdRoundCandidates;
      case InterviewStage.FinalRound:
        return finalRoundCandidates;
      case InterviewStage.HRRound:
        return hrRoundCandidates;
      case InterviewStage.PracticalRound:
        return practicalRoundCandidates;
      default:
        return interviewCandidates;
    }
  };

  const getCandidateCountByStage = (stage: InterviewStage) => {
    switch (stage) {
      case InterviewStage.Technical:
        return technicalRoundCandidates.length;
      case InterviewStage.FirstRound:
        return firstRoundCandidates.length;
      case InterviewStage.SecondRound:
        return secondRoundCandidates.length;
      case InterviewStage.ThirdRound:
        return thirdRoundCandidates.length;
      case InterviewStage.FinalRound:
        return finalRoundCandidates.length;
      case InterviewStage.HRRound:
        return hrRoundCandidates.length;
      case InterviewStage.PracticalRound:
        return practicalRoundCandidates.length;
      default:
        return 0;
    }
  };

  console.log("Candidates:", candidates);

  // Status badge colors
  const getStatusBadge = (status: string) => {
    switch (status) {
      case CandidateStatus.Applied:
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
            Applied
          </Badge>
        );
      case CandidateStatus.Screening:
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-50">
            Screening
          </Badge>
        );
      case CandidateStatus.Interview:
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50">
            Interview
          </Badge>
        );
      case CandidateStatus.Offer:
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
            Offer
        </Badge>
        );
      case CandidateStatus.Hired:
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            Hired
          </Badge>
        );
      case CandidateStatus.Rejected:
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50">
            Rejected
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Match score color
  const getMatchScoreColor = (score: number) => {
    if (score >= 85) return "text-green-600";
    if (score >= 70) return "text-amber-600";
    return "text-red-600";
  };

  const handleCandidateAdded = () => {
    toast.success("Candidate added successfully", {
      description: "The new candidate has been added to the system.",
    });
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Reset interview stage tab to "all" when changing main tab
    setActiveInterviewStageTab("all");
  };

  // Get badge color for tab counts
  const getTabCountBadgeColor = (status: string) => {
    switch (status) {
      case CandidateStatus.Applied:
        return "bg-blue-100 text-blue-900";
      case CandidateStatus.Screening:
        return "bg-purple-100 text-purple-900";
      case CandidateStatus.Interview:
        return "bg-amber-100 text-amber-900";
      case CandidateStatus.Offer:
        return "bg-green-100 text-green-900";
      case CandidateStatus.Hired:
        return "bg-green-200 text-green-900";
      case CandidateStatus.Rejected:
        return "bg-red-100 text-red-900";
      default:
        return "bg-gray-200 text-gray-900";
    }
  };

  const totalPages = Math.ceil(sortedCandidates.length / itemsPerPage);
  const paginatedCandidates = sortedCandidates.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, searchQuery]);

  // Get the candidates to display based on current tab and interview stage
  const getCandidatesToDisplay = (status: string) => {
    if (status === "all") {
      return paginatedCandidates;
    } else if (status === CandidateStatus.Applied) {
      return appliedCandidates;
    } else if (status === CandidateStatus.Screening) {
      return screeningCandidates;
    } else if (status === CandidateStatus.Interview) {
      return renderInterviewCandidates(activeInterviewStageTab as InterviewStage);
    } else if (status === CandidateStatus.Offer) {
      return offerCandidates;
    } else if (status === CandidateStatus.Hired) {
      return hiredCandidates;
    } else {
      return rejectedCandidates;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Candidates</h1>
          <p className="text-muted-foreground">Manage and review job applicants</p>
        </div>
        <div className="flex gap-2">
          <AddCandidateModal
            onCandidateAdded={handleCandidateAdded}
            icon={Users}
            buttonText="Add Candidate"
          />
          <BulkUploadCandidatesModal />
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search candidates by name, job, skills..."
            className="pl-8"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2 w-full md:w-auto">
          <Select>
            <SelectTrigger className="w-[160px]">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <span>Job Title</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Positions</SelectItem>
              <SelectItem value="senior-frontend">Senior Frontend</SelectItem>
              <SelectItem value="product-designer">Product Designer</SelectItem>
              <SelectItem value="backend">Backend Developer</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="mb-4">
          <TabsTrigger value="all" className="flex items-center gap-2">
            All
            <span className="flex items-center justify-center rounded-full bg-gray-200 text-gray-900 text-xs font-medium px-2 py-0.5">
              {candidates.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value={CandidateStatus.Applied} className="flex items-center gap-2">
            Applied
            <span className="flex items-center justify-center rounded-full bg-blue-100 text-blue-900 text-xs font-medium px-2 py-0.5">
              {appliedCandidates.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value={CandidateStatus.Screening} className="flex items-center gap-2">
            Screening
            <span className="flex items-center justify-center rounded-full bg-purple-100 text-purple-900 text-xs font-medium px-2 py-0.5">
              {screeningCandidates.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value={CandidateStatus.Interview} className="flex items-center gap-2">
            Interview
            <span className="flex items-center justify-center rounded-full bg-amber-100 text-amber-900 text-xs font-medium px-2 py-0.5">
              {interviewCandidates.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value={CandidateStatus.Offer} className="flex items-center gap-2">
            Offer
            <span className="flex items-center justify-center rounded-full bg-green-100 text-green-900 text-xs font-medium px-2 py-0.5">
              {offerCandidates.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value={CandidateStatus.Hired} className="flex items-center gap-2">
            Hired
            <span className="flex items-center justify-center rounded-full bg-green-200 text-green-900 text-xs font-medium px-2 py-0.5">
              {hiredCandidates.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value={CandidateStatus.Rejected} className="flex items-center gap-2">
            Rejected
            <span className="flex items-center justify-center rounded-full bg-red-100 text-red-900 text-xs font-medium px-2 py-0.5">
              {rejectedCandidates.length}
            </span>
          </TabsTrigger>
        </TabsList>

        {[
          "all",
          CandidateStatus.Applied,
          CandidateStatus.Screening,
          CandidateStatus.Interview,
          CandidateStatus.Offer,
          CandidateStatus.Hired,
          CandidateStatus.Rejected,
        ].map(status => (
          <TabsContent key={status} value={status}>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {status === "all"
                    ? "All Candidates"
                    : `${status.charAt(0).toUpperCase() + status.slice(1)} Candidates`}
                </CardTitle>
              </CardHeader>
              {status === CandidateStatus.Interview && (
                <CardContent className="pt-0">
                  <div className="flex items-center justify-start gap-1 mb-4 p-1 bg-muted rounded-lg overflow-x-auto">
                    <button
                      onClick={() => setActiveInterviewStageTab("all")}
                      className={`inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                        activeInterviewStageTab === "all"
                          ? "bg-background text-foreground shadow-sm"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground"
                      }`}
                    >
                      All
                      <span className="flex items-center justify-center rounded-full bg-blue-100 text-blue-900 text-xs font-medium px-2 py-0.5">
                        {interviewCandidates.length}
                      </span>
                    </button>
                    {Object.values(InterviewStage).map(stage => (
                      <button
                        key={stage}
                        onClick={() => setActiveInterviewStageTab(stage)}
                        className={`inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                          activeInterviewStageTab === stage
                            ? "bg-background text-foreground shadow-sm"
                            : "text-muted-foreground hover:bg-muted hover:text-foreground"
                        }`}
                      >
                        {stage.replace(/([A-Z])/g, ' $1').trim()}
                        <span className="flex items-center justify-center rounded-full bg-blue-100 text-blue-900 text-xs font-medium px-2 py-0.5">
                          {getCandidateCountByStage(stage)}
                        </span>
                      </button>
                    ))}
                  </div>
                </CardContent>
              )}
              <CardContent className={status === CandidateStatus.Interview ? "pt-0" : ""}>
                {loading ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="h-24 bg-muted animate-pulse rounded-md" />
                    ))}
                  </div>
                ) : (
                  <>
                    <div className="space-y-4">
                      {getCandidatesToDisplay(status).map(candidate => (
                        <div
                          key={candidate.id}
                          className="group bg-muted/50 rounded-lg p-4 flex flex-col md:flex-row md:items-center justify-between gap-3 cursor-pointer hover:bg-muted transition-colors duration-200"
                          onClick={() => handleViewCandidate(candidate.id)}
                        >
                          <div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(candidate.status)}
                              <Badge
                                variant="outline"
                                className={cn(
                                  "bg-blue-50 text-blue-700",
                                  getMatchScoreColor(candidate.matchScore)
                                )}
                              >
                                {candidate.matchScore}% Match
                              </Badge>
                            </div>
                            <h4 className="font-medium mt-2 group-hover:text-primary transition-colors duration-200">
                              {candidate.name} - {candidate.jobTitle}
                            </h4>
                            <p className="text-sm text-muted-foreground mt-1">
                              {candidate.location} • Applied {formatDate(candidate.appliedDate)}
                            </p>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                              onClick={e => {
                                e.stopPropagation();
                                handleViewCandidate(candidate.id);
                              }}
                            >
                              View Profile
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                    {/* Pagination for All tab */}
                    {status === "all" && totalPages > 1 && (
                      <div className="flex justify-center mt-6">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={e => {
                                  e.preventDefault();
                                  if (currentPage > 1) setCurrentPage(currentPage - 1);
                                }}
                                aria-disabled={currentPage === 1}
                                className={
                                  currentPage === 1 ? "pointer-events-none opacity-50" : ""
                                }
                              />
                            </PaginationItem>
                            {Array.from({ length: totalPages }, (_, i) => (
                              <PaginationItem key={i}>
                                <PaginationLink
                                  href="#"
                                  isActive={currentPage === i + 1}
                                  onClick={e => {
                                    e.preventDefault();
                                    setCurrentPage(i + 1);
                                  }}
                                >
                                  {i + 1}
                                </PaginationLink>
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={e => {
                                  e.preventDefault();
                                  if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                                }}
                                aria-disabled={currentPage === totalPages}
                                className={
                                  currentPage === totalPages ? "pointer-events-none opacity-50" : ""
                                }
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Candidate Details Drawer */}
      <CandidateDrawer
        setCandidates={setCandidates}
        isOpen={drawerOpen}
        onOpenChange={setDrawerOpen}
        candidate={selectedCandidate}
        candidates={candidates}
        setSelectedCandidate={updateSelectedCandidate}
      />
    </div>
  );
};

export default Candidates;