import { Job } from "@/hooks/useJobs";
import React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useJobs } from "@/hooks/useJobs";
import { FormatableTextarea } from "../ui/formatable-textarea";

interface JobFormSheetProps {
  onClose: () => void;
  job?: Job; // Make job prop optional
}

const jobFormSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  description: z.string().min(10, { message: "Please provide more details" }),
  department: z.string().min(1, { message: "Department is required" }),
  location: z.string().min(1, { message: "Work location is required" }),
  office_location: z.string().optional(),
  employment_type: z.enum(["full-time", "part-time", "contract", "internship"], {
    message: "Work type is required",
  }),
  salary_min: z.preprocess(
    val => Number(val),
    z.number().min(0, { message: "Minimum salary must be a positive number" })
  ),
  salary_max: z.preprocess(
    val => Number(val),
    z.number().min(0, { message: "Maximum salary must be a positive number" })
  ),
  salary_frequency: z.enum(["monthly", "yearly"], { message: "Salary frequency is required" }),
  tools_and_tech: z.string().optional(),
  good_to_have: z.string().optional(),
  experience_years: z.preprocess(
    val => Number(val),
    z.number().min(0, { message: "Experience years is required" })
  ),
  requirements: z.array(z.string()).optional(),
  responsibilities: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
});

const jobFormInputSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  description: z.string().min(10, { message: "Please provide more details" }),
  department: z.string().min(1, { message: "Department is required" }),
  location: z.string().min(1, { message: "Work location is required" }),
  office_location: z.string().optional(),
  employment_type: z.enum(["full-time", "part-time", "contract", "internship"], {
    message: "Work type is required",
  }),
  salary_min: z.preprocess(
    val => Number(val),
    z.number().min(0, { message: "Minimum salary must be a positive number" })
  ),
  salary_max: z.preprocess(
    val => Number(val),
    z.number().min(0, { message: "Maximum salary must be a positive number" })
  ),
  salary_frequency: z.enum(["monthly", "yearly"], { message: "Salary frequency is required" }),
  tools_and_tech: z.string().optional(),
  good_to_have: z.string().optional(),
  experience_years: z.preprocess(
    val => Number(val),
    z.number().min(0, { message: "Experience years is required" })
  ),
  requirements: z.string().optional(),
  responsibilities: z.string().optional(),
  skills: z.string().optional(),
});

type JobFormValues = z.infer<typeof jobFormSchema>;
type JobFormInputValues = z.infer<typeof jobFormInputSchema>;

const JobFormSheet: React.FC<JobFormSheetProps> = ({ job, onClose }) => {
  const form = useForm<JobFormInputValues>({
    resolver: zodResolver(jobFormInputSchema),
    defaultValues: {
      title: job.title || "",
      description: job.description || "",
      department: job.department || "",
      location: job.location || "",
      office_location: job.office_location || "",
      employment_type: job.employment_type || "full-time",
      salary_min: job.salary_min || 0,
      salary_max: job.salary_max || 0,
      salary_frequency: job.salary_frequency || "monthly",
      tools_and_tech: job.tools_and_tech || "",
      good_to_have: job.good_to_have || "",
      experience_years: job.experience_years || 0,
      requirements: job.requirements?.join("\n") || "",
      responsibilities: job.responsibilities?.join("\n") || "",
      skills: job.skills?.join("\n") || "",
    },
  });

  const { updateJob } = useJobs();

  React.useEffect(() => {
    if (job) {
      form.reset({
        title: job.title || "",
        description: job.description || "",
        department: job.department || "",
        location: job.location || "",
        office_location: job.office_location || "",
        employment_type: job.employment_type || "full-time",
        salary_min: job.salary_min || 0,
        salary_max: job.salary_max || 0,
        salary_frequency: job.salary_frequency || "monthly",
        tools_and_tech: job.tools_and_tech || "",
        good_to_have: job.good_to_have || "",
        experience_years: job.experience_years || 0,
        requirements: job.requirements?.join("\n") || "",
        responsibilities: job.responsibilities?.join("\n") || "",
        skills: job.skills?.join("\n") || "",
      });
    }
  }, [job, form]);

  const onSubmit = async (data: JobFormInputValues) => {
    const formattedData = {
      ...data,
      requirements: data.requirements?.split("\n").filter(s => s.trim() !== "") || [],
      responsibilities: data.responsibilities?.split("\n").filter(s => s.trim() !== "") || [],
      skills: data.skills?.split("\n").filter(s => s.trim() !== "") || [],
    };
    // Here you would typically send the data to an API to update the job
    const success = await updateJob(job.id, formattedData);
    if (success) {
      onClose();
    }
  };

  return (
    <div className="p-4 overflow-y-auto w-full">
      <h2 className="text-xl font-bold mb-4">Edit Job Details</h2>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Title</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Java Architect" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="department"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Engineering" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Job Description</FormLabel>
                <FormControl>
                  <FormatableTextarea
                    placeholder="Describe the job, responsibilities, and requirements..."
                    toolbarPosition="top"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Work Location</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select work location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="on-site">On-Site</SelectItem>
                        <SelectItem value="remote">Remote</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="office_location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Office Location</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., New York" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="employment_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Work Type</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select work type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full-time">Full-Time</SelectItem>
                        <SelectItem value="part-time">Part-Time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="internship">Internship</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="experience_years"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Experience (Years)</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="e.g., 5" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="salary_min"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum Salary</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="e.g., 50000" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="salary_max"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Maximum Salary</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="e.g., 80000" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="salary_frequency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Salary Frequency</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="tools_and_tech"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tools and Technologies</FormLabel>
                <FormControl>
                  <Textarea placeholder="List required tools..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="good_to_have"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Good to Have Skills</FormLabel>
                <FormControl>
                  <Textarea placeholder="List preferred skills..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="requirements"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Requirements</FormLabel>
                <FormControl>
                  <Textarea placeholder="List job requirements..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="responsibilities"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Responsibilities</FormLabel>
                <FormControl>
                  <Textarea placeholder="List job responsibilities..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="skills"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Skills</FormLabel>
                <FormControl>
                  <Textarea placeholder="List required skills..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Save</Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default JobFormSheet;
