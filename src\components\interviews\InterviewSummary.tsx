
import { format } from 'date-fns';
import { CalendarIcon, Clock, MapPin, Video, Users, FileText } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface Interviewer {
  id: string;
  name: string;
  role: string;
  available: boolean;
}

interface InterviewSummaryProps {
  formData: any;
  selfScheduling: boolean;
  interviewers: Interviewer[];
}

const InterviewSummary = ({ formData, selfScheduling, interviewers }: InterviewSummaryProps) => {
  const interviewTypeText = {
    'face-to-face': 'Face to Face',
    'phone': 'Phone',
    'video': 'Video'
  };
  
  const interviewStageText = {
    'screening': 'Initial Screening',
    'technical': 'Technical Round',
    'manager': 'Manager Round',
    'hr': 'HR Round',
    'final': 'Final Round'
  };

  return (
    <div className="border rounded-lg p-4 space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
          <Users className="h-4 w-4" />
          Interview Details
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <div>
            <p className="text-xs text-muted-foreground">Type</p>
            <p className="text-sm">
              {interviewTypeText[formData.interviewType as keyof typeof interviewTypeText]}
            </p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Stage</p>
            <p className="text-sm">
              {interviewStageText[formData.interviewStage as keyof typeof interviewStageText]}
            </p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Duration</p>
            <p className="text-sm">{formData.duration} minutes</p>
          </div>
          {formData.isRescheduled && (
            <div className="col-span-2">
              <Badge variant="outline" className="bg-amber-100 text-amber-800">
                Rescheduled
              </Badge>
              {formData.rescheduleNote && (
                <p className="text-xs mt-1 text-muted-foreground">
                  Note: {formData.rescheduleNote}
                </p>
              )}
            </div>
          )}
          
          {formData.instructions && (
            <div className="col-span-2 mt-2">
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <FileText className="h-3 w-3" />
                Instructions
              </p>
              <p className="text-sm mt-1">{formData.instructions}</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="border-t pt-3">
        <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
          <CalendarIcon className="h-4 w-4" />
          Schedule
        </h3>
        
        {selfScheduling ? (
          <div>
            <Badge className="mb-2 bg-blue-100 text-blue-700 hover:bg-blue-100">
              Candidate Self-Scheduling
            </Badge>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <p className="text-xs text-muted-foreground">Available From</p>
                <p className="text-sm">
                  {formData.availableFrom ? format(formData.availableFrom, 'MMM dd, yyyy') : '-'}
                </p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">Available To</p>
                <p className="text-sm">
                  {formData.availableTo ? format(formData.availableTo, 'MMM dd, yyyy') : '-'}
                </p>
              </div>
              <div className="col-span-2">
                <p className="text-xs text-muted-foreground">Time Slots</p>
                <p className="text-sm">9:00 AM - 6:00 PM (Weekdays only)</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3">
            <div>
              <p className="text-xs text-muted-foreground">Date</p>
              <p className="text-sm">
                {formData.date ? format(formData.date, 'MMM dd, yyyy') : '-'}
              </p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Time</p>
              <p className="text-sm">
                {formData.time || '-'}
              </p>
            </div>
            {formData.interviewType === 'face-to-face' && (
              <div className="col-span-2">
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  Location
                </p>
                <p className="text-sm">{formData.location || 'Not specified'}</p>
              </div>
            )}
            {formData.interviewType === 'video' && (
              <div className="col-span-2">
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <Video className="h-3 w-3" />
                  Video Link
                </p>
                <p className="text-sm break-all">{formData.videoLink || 'Auto-generated link'}</p>
              </div>
            )}
          </div>
        )}
      </div>
      
      <div className="border-t pt-3">
        <h3 className="text-sm font-medium mb-2">Interviewers</h3>
        <div className="space-y-2">
          {interviewers.map((interviewer) => (
            <div 
              key={interviewer.id}
              className="flex items-center justify-between p-2 border rounded-md"
            >
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarFallback>
                    {interviewer.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm">{interviewer.name}</span>
              </div>
              <span className="text-xs text-muted-foreground">{interviewer.role}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default InterviewSummary;

