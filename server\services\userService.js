import { supabase } from '../config/db.js';
import bcrypt from 'bcryptjs';
import { logger } from '../utils/logger.js';

export const userService = {
  async getUsers(query) {
    const { page = 1, limit = 10, ...filters } = query;
    const start = (page - 1) * limit;
    const end = start + limit - 1;

    try {
      let query = supabase
        .from('users')
        .select('*, roles(*)', { count: 'exact' })
        .eq('flag_deleted', false)
        .range(start, end);

      // Apply filters if any
      Object.entries(filters).forEach(([key, value]) => {
        if (value) query = query.eq(key, value);
      });

      const { data: users, count, error } = await query;

      if (error) throw error;

      return {
        users: users.map(user => {
          const { password_hash, ...sanitizedUser } = user;
          return sanitizedUser;
        }),
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to get users:', error);
      throw error;
    }
  },

  async getUserById(id) {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select('*, roles(*)')
        .eq('id', id)
        .eq('flag_deleted', false)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'User not found' };
        }
        throw error;
      }

      const { password_hash, ...sanitizedUser } = user;
      return { success: true, user: sanitizedUser };
    } catch (error) {
      logger.error('Failed to get user by id:', error);
      throw error;
    }
  },

  async createUser(userData) {
    try {
      // Check if email exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', userData.email)
        .single();

      if (existingUser) {
        return { success: false, message: 'Email already registered' };
      }

      // Hash password
      const password_hash = await bcrypt.hash(userData.password, 10);

      // Create user
      const { data: user, error } = await supabase
        .from('users')
        .insert([{
          ...userData,
          password_hash,
          flag_active: true
        }])
        .select('*, roles(*)')
        .single();

      if (error) throw error;

      const { password_hash: _, ...sanitizedUser } = user;
      return { success: true, user: sanitizedUser };
    } catch (error) {
      logger.error('Failed to create user:', error);
      throw error;
    }
  },

  async updateUser(id, updateData) {
    try {
      if (updateData.email) {
        const { data: existingUser } = await supabase
          .from('users')
          .select('id')
          .eq('email', updateData.email)
          .neq('id', id)
          .single();

        if (existingUser) {
          return { success: false, message: 'Email already in use' };
        }
      }

      // Hash password if it's being updated
      if (updateData.password) {
        updateData.password_hash = await bcrypt.hash(updateData.password, 10);
        delete updateData.password;
      }

      const { data: user, error } = await supabase
        .from('users')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('flag_deleted', false)
        .select('*, roles(*)')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'User not found', status: 404 };
        }
        throw error;
      }

      const { password_hash, ...sanitizedUser } = user;
      return { success: true, user: sanitizedUser };
    } catch (error) {
      logger.error('Failed to update user:', error);
      throw error;
    }
  },

  async deleteUser(id) {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .update({
          flag_deleted: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('flag_deleted', false)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'User not found', status: 404 };
        }
        throw error;
      }

      return { success: true };
    } catch (error) {
      logger.error('Failed to delete user:', error);
      throw error;
    }
  },

  async findUserByEmail(email) {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select('*, roles(*)')
        .eq('email', email)
        .eq('flag_deleted', false)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'User not found' };
        }
        throw error;
      }

      return { success: true, user };
    } catch (error) {
      logger.error('Failed to find user by email:', error);
      throw error;
    }
  },

  async comparePassword(plainPassword, hashedPassword) {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
};