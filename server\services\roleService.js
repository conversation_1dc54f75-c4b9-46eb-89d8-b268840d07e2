import { supabase } from '../config/db.js';
import { logger } from '../utils/logger.js';

const handleServiceError = (error, message) => {
  logger.error(`${message}:`, error);
  throw new Error(message);
};

export const roleService = {
  async getRoles() {
    try {
      const { data: roles, error } = await supabase
        .from('roles')
        .select('*')
        .eq('flag_deleted', false)
        .order('name');

      if (error) throw error;

      return {
        success: true,
        roles
      };
    } catch (error) {
      handleServiceError(error, 'Failed to retrieve roles');
    }
  },

  async createRole(roleData) {
    try {
      // Validate role type
      if (!['admin', 'recruiter', 'candidate', 'hr', 'manager', 'interviewer'].includes(roleData.name)) {
        return { 
          success: false, 
          message: 'Invalid role type. Must be one of: admin, recruiter, candidate, hr, manager, interviewer' 
        };
      }

      // Check if role exists
      const { data: existingRole } = await supabase
        .from('roles')
        .select('id')
        .eq('name', roleData.name)
        .eq('flag_deleted', false)
        .single();

      if (existingRole) {
        return { success: false, message: 'Role already exists' };
      }

      // Create role
      const { data: role, error } = await supabase
        .from('roles')
        .insert([{
          name: roleData.name,
          flag_active: true,
          flag_deleted: false,
          created_at: new Date(),
          updated_at: new Date()
        }])
        .select()
        .single();

      if (error) throw error;

      return { success: true, role };
    } catch (error) {
      handleServiceError(error, 'Failed to create role');
    }
  }
};