import { useState, useEffect } from "react";
import ScheduleInterview from "@/pages/ScheduleInterview";
import { Candidate, CandidateStatus, useCandidates } from "@/hooks/useCandidates";
import { useCandidateActions } from "@/hooks/useCandidateActions";
import { getInterviewsByCandidateId } from "@/hooks/useInterviews";
import CandidateBasicInfo from "./CandidateBasicInfo";
import CandidateTabs from "./CandidateTabs";
import { Sheet, SheetContent, SheetHeader } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

export interface Interview {
  round: number;
  title: string;
  date: string;
  interviewer: string;
  feedback: string;
  result: "pass" | "fail" | "pending";
}

export interface CandidateDetail {
  id: string;
  name: string;
  email: string;
  phone: string;
  jobTitle: string;
  location: string;
  status: CandidateStatus;
  matchScore: number;
  appliedDate: string;
  lastUpdated: string;
  skills: string[];
  experience: string;
  education: string[];
  resumeUrl?: string;
  interviews: Interview[];
  notes?: string;
  source: string;
  suitability_summary?: string;
  unsuitability_summary?: string;
}

interface CandidateDrawerProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  candidate: CandidateDetail | null;
  setCandidates?: React.Dispatch<React.SetStateAction<Candidate[]>>;
  candidates?: CandidateDetail[];
  setSelectedCandidate?: (candidate: CandidateDetail | null) => void;
}

export const CandidateDrawer = ({
  isOpen,
  onOpenChange,
  candidate,
  setCandidates,
  candidates = [],
  setSelectedCandidate,
}: CandidateDrawerProps) => {
  const { getCandidateById, fetchAllCandidates } = useCandidates();
  const { acceptCandidate, declineCandidate, loading: actionLoading } = useCandidateActions();
  const [activeTab, setActiveTab] = useState("profile");
  const [isScheduleDrawerOpen, setIsScheduleDrawerOpen] = useState(false);
  const [candidateDetail, setCandidateDetail] = useState<CandidateDetail | null>(candidate);
  const [interviews, setInterviews] = useState<any[]>([]);
  const [interviewsLoading, setInterviewsLoading] = useState(false);
  const [interviewsError, setInterviewsError] = useState<string | null>(null);

  // Navigation handlers
  const currentId = candidateDetail?.id || candidate?.id;
  const currentIndex = candidates && currentId ? candidates.findIndex(c => c.id === currentId) : -1;

  useEffect(() => {
    if (isOpen && candidate?.id) {
      setCandidateDetail(null);
      getCandidateById(candidate.id).then(data => {
        if (data) setCandidateDetail(data as CandidateDetail);
      });
    }
  }, [isOpen, candidate?.id]);

  useEffect(() => {
    if (isOpen && candidate?.id) {
      setInterviewsLoading(true);
      setInterviewsError(null);
      getInterviewsByCandidateId(candidate.id)
        .then(data => setInterviews(data))
        .catch(err => setInterviewsError(err.message))
        .finally(() => setInterviewsLoading(false));
    }
  }, [isOpen, candidate?.id]);

  const handleResumeDownload = () => {
    if (candidateDetail?.resumeUrl) {
      window.open(candidateDetail.resumeUrl, "_blank");
    }
  };

  const closeDrawer = async () => {
    onOpenChange(false);
    // Fetch all candidates and update state after closing drawer
    const result = await fetchAllCandidates();
    if (result && result.candidates) {
      setCandidates(result.candidates);
    } else {
      setCandidates([]);
    }
  };

  const handlePrev = () => {
    if (!candidates || !setSelectedCandidate || currentIndex <= 0) return;
    setSelectedCandidate(candidates[currentIndex - 1]);
  };
  const handleNext = () => {
    if (!candidates || currentIndex === -1 || currentIndex >= candidates.length - 1) return;
    setSelectedCandidate(candidates[currentIndex + 1]);
  };

  const handleAccept = async () => {
    if (candidateDetail) {
      await acceptCandidate(candidateDetail.id);
      onOpenChange(false);
    }
  };

  const handleDecline = async () => {
    if (candidateDetail) {
      await declineCandidate(candidateDetail.id);
      onOpenChange(false);
    }
  };

  if (!candidateDetail) {
    return isOpen ? (
      <Sheet open={isOpen} onOpenChange={onOpenChange}>
        <SheetContent
          side="right"
          className="w-full max-w-[90%] sm:max-w-[80%] lg:max-w-[1100px] p-0"
        >
          <div className="h-full flex items-center justify-center text-lg text-gray-500">
            Loading candidate details...
          </div>
        </SheetContent>
      </Sheet>
    ) : null;
  }

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className="w-full max-w-[90%] sm:max-w-[80%] lg:max-w-[1100px] p-0"
      >
        <div className="h-full overflow-y-auto">
          <SheetHeader className="px-6 pt-6 pb-2">
            <div className="flex items-start flex-col md:flex-row gap-6">
              {/* Left column - Basic info */}
              <CandidateBasicInfo
                candidateDetail={candidateDetail}
                actionLoading={actionLoading}
                onResumeDownload={handleResumeDownload}
                onAccept={handleAccept}
                onDecline={handleDecline}
                setActiveTab={setActiveTab}
              />
              {/* Right column - Tabs */}
              <CandidateTabs
                closeDrawer={closeDrawer}
                candidateDetail={candidateDetail}
                interviews={interviews}
                interviewsLoading={interviewsLoading}
                interviewsError={interviewsError}
                setIsScheduleDrawerOpen={setIsScheduleDrawerOpen}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
              />
            </div>
            {candidates.length > 1 && (
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={handlePrev} disabled={currentIndex <= 0}>
                  Previous
                </Button>
                <Button
                  onClick={handleNext}
                  disabled={currentIndex === -1 || currentIndex >= candidates.length - 1}
                >
                  Next
                </Button>
              </div>
            )}
          </SheetHeader>
        </div>
      </SheetContent>
      <ScheduleInterview
        isOpen={isScheduleDrawerOpen}
        onOpenChange={setIsScheduleDrawerOpen}
        defaultCandidate={{
          id: candidateDetail.id,
          name: candidateDetail.name,
          email: candidateDetail.email,
          phone: candidateDetail.phone,
          role: candidateDetail.jobTitle,
          stage: candidateDetail.status,
          photo: "",
        }}
      />
    </Sheet>
  );
};

export default CandidateDrawer;
