import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/components/auth/AuthProvider';
import { cn } from '@/lib/utils';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from '@/components/ui/sidebar';
import {
  LayoutDashboard,
  Users,
  Briefcase,
  Calendar,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  User,
  ChevronRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';

interface MenuItem {
  name: string;
  href: string;
  icon: React.ElementType;
  roles: string[];
}

import { Users2 } from 'lucide-react';

export function AppSidebar() {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const menuItems: MenuItem[] = [
    {
      name: 'Dashboard',
      href: `/dashboard/${user?.role.name}`,
      icon: LayoutDashboard,
      roles: ['recruiter', 'manager', 'interviewer', 'hr'],
    },
    {
      name: 'Jobs',
      href: '/jobs',
      icon: Briefcase,
      roles: ['recruiter', 'manager', 'hr'],
    },
    {
      name: 'Candidates',
      href: '/candidates',
      icon: Users,
      roles: ['recruiter', 'manager', 'interviewer', 'hr'],
    },
    {
      name: 'Interviewers',
      href: '/interviewers',
      icon: User,
      roles: ['recruiter', 'manager'],
    },
    {
      name: 'Interviews',
      href: '/interviews',
      icon: Calendar,
      roles: ['recruiter', 'manager', 'interviewer'],
    },
    {
      name: 'Offers',
      href: '/offers',
      icon: FileText,
      roles: ['recruiter', 'manager', 'hr'],
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: BarChart3,
      roles: ['recruiter', 'manager', 'hr'],
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      roles: ['recruiter', 'manager', 'interviewer', 'hr'],
    },
  ];

  const filteredMenuItems = menuItems.filter(item =>
    item.roles.includes(user?.role.name || '')
  );

  const getInitials = (name?: string) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const roleColors: Record<string, string> = {
    recruiter: 'bg-recruiter',
    manager: 'bg-manager',
    interviewer: 'bg-interviewer',
    hr: 'bg-hr',
  };

  if (!user) return null;

  return (
    <Sidebar
      className={cn(
        "transition-width duration-300 h-screen flex flex-col",
        isCollapsed ? "w-[70px]" : "w-[250px]"
      )}
    >
      <SidebarHeader className="flex items-center justify-between p-4 flex-shrink-0">
        {!isCollapsed && (
          <div className="flex items-center gap-2">
            <div className="bg-primary rounded-md p-1">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary-foreground">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            </div>
            <h1 className="text-lg font-bold">RecruitAI</h1>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8"
        >
          {isCollapsed ? <ChevronRight size={18} /> : <Menu size={18} />}
        </Button>
      </SidebarHeader>

      <SidebarContent className="flex-1 overflow-hidden">
        <div className={cn("px-3 py-2 h-full", isCollapsed ? "px-2 py-2" : "")}>
          {filteredMenuItems.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2 mb-1 text-sidebar-foreground/80 hover:bg-sidebar-accent hover:text-sidebar-foreground transition-colors",
                location.pathname.startsWith(item.href) && "bg-sidebar-accent text-sidebar-foreground",
                isCollapsed && "justify-center px-2"
              )}
            >
              <item.icon size={18} />
              {!isCollapsed && <span>{item.name}</span>}
            </Link>
          ))}
        </div>
      </SidebarContent>

      <SidebarFooter className={cn("flex-shrink-0", isCollapsed ? "px-2 py-4" : "p-4")}>
        <Separator className="my-2 bg-sidebar-border/50" />
        <div className="flex items-center justify-between mt-2">
          <div className={cn("flex items-center gap-3", isCollapsed && "justify-center w-full")}>
            <Avatar className={cn("h-8 w-8", roleColors[user.role.name])}>
              <AvatarFallback className="text-white">
                {getInitials(user.full_name)}
              </AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <div>
                <p className="text-sm font-medium line-clamp-1">{user.full_name}</p>
                <p className="text-xs text-sidebar-foreground/70 capitalize">
                  {user.role.name}
                </p>
              </div>
            )}
          </div>
          {!isCollapsed && (
            <Button variant="ghost" size="icon" onClick={signOut} className="h-8 w-8">
              <LogOut size={18} />
            </Button>
          )}
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
