import { z } from 'zod';

export const jobValidation = {
  jobCreateSchema: z.object({
    body: z.object({
      title: z.string().min(3, 'Job title must be at least 3 characters'),
      description: z.string().min(50, 'Job description must be at least 50 characters'),
      requirements: z.array(z.string())
        .min(1, 'At least one requirement is needed')
        .max(20, 'Maximum 20 requirements allowed'),
      location: z.string().min(2, 'Location must be specified'),
      salary_range: z.object({
        min: z.number().positive('Minimum salary must be positive'),
        max: z.number().positive('Maximum salary must be positive')
      }).refine(data => data.max > data.min, {
        message: 'Maximum salary must be greater than minimum salary'
      }),
      status: z.enum(['draft', 'open', 'closed']).default('draft'),
      auto_generate_description: z.boolean().optional(),
      prompt: z.string().optional()
    })
  }),

  jobUpdateSchema: z.object({
    params: z.object({
      id: z.string().uuid('Invalid job ID')
    }),
    body: z.object({
      title: z.string().min(3, 'Job title must be at least 3 characters').optional(),
      description: z.string().min(50, 'Job description must be at least 50 characters').optional(),
      requirements: z.array(z.string()).optional(),
      location: z.string().min(2, 'Location must be specified').optional(),
      salary_range: z.object({
        min: z.number().positive('Minimum salary must be positive'),
        max: z.number().positive('Maximum salary must be positive')
      }).optional(),
      status: z.enum(['draft', 'open', 'closed']).optional()
    })
  })
};