
import React from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, X } from 'lucide-react';

interface RecommendationButtonsProps {
  value: string;
  onChange: (value: string) => void;
}

const RecommendationButtons = ({ value, onChange }: RecommendationButtonsProps) => {
  const options = [
    { id: 'strong_hire', label: 'Strong Hire', color: 'bg-green-500 hover:bg-green-600', icon: <CheckCircle className="h-4 w-4 mr-1" /> },
    { id: 'hire', label: 'Hire', color: 'bg-emerald-500 hover:bg-emerald-600', icon: <CheckCircle className="h-4 w-4 mr-1" /> },
    { id: 'neutral', label: 'Neutral', color: 'bg-amber-500 hover:bg-amber-600', icon: null },
    { id: 'no_hire', label: 'No Hire', color: 'bg-red-500 hover:bg-red-600', icon: <X className="h-4 w-4 mr-1" /> },
    { id: 'strong_no_hire', label: 'Strong No Hire', color: 'bg-rose-600 hover:bg-rose-700', icon: <X className="h-4 w-4 mr-1" /> },
  ];

  return (
    <div className="flex flex-wrap gap-2">
      {options.map((option) => (
        <Button
          key={option.id}
          type="button"
          variant={value === option.id ? "default" : "outline"}
          className={`${
            value === option.id 
              ? option.color + ' text-white' 
              : 'border-gray-300'
          }`}
          onClick={() => onChange(option.id)}
        >
          {option.icon}
          {option.label}
        </Button>
      ))}
    </div>
  );
};

export default RecommendationButtons;
