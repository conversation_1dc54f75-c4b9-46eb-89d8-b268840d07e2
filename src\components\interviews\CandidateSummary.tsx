import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mail } from 'lucide-react';
import { CandidateStatus } from '@/hooks/useCandidates';

interface Candidate {
  id: string;
  name: string;
  photo?: string;
  role: string;
  stage: string;
  email: string;
}

interface CandidateSummaryProps {
  candidate: any;
  interviews?: any[];
}

const CandidateSummary = ({ candidate, interviews = [] }: CandidateSummaryProps) => {
  if (!candidate) {
    return (
      <div className="space-y-4 sticky top-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Candidate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center text-center space-y-3">
              <p className="text-muted-foreground">Candidate not found</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Build timeline events
  const timeline = [
    {
      label: "Application Received",
      date: candidate.appliedDate,
      type: "applied",
    },
  ];

  // Status order for progression
  const statusOrder = [
    CandidateStatus.Applied,
    CandidateStatus.Screening,
    CandidateStatus.Interview,
    CandidateStatus.Offer,
    CandidateStatus.Hired,
    CandidateStatus.Rejected
  ];
  const currentStatusIndex = statusOrder.indexOf(candidate.status);

  // Add screening event if status is screening or beyond
  if (currentStatusIndex >= 1) {
    timeline.push({
      label: "Screening",
      date: candidate.lastUpdated, // Use lastUpdated for now; ideally use a specific screening date
      type: "screening",
    });
  }

  // Add interview events
  if (interviews && interviews.length > 0) {
    interviews.forEach(interview => {
      timeline.push({
        label: `${interview.stage || interview.title || 'Interview'} Interview`,
        date: interview.date,
        type: "interview"
      });
    });
  }

  // Add offer/hired/rejected events
  if (candidate.status === CandidateStatus.Offer) {
    timeline.push({
      label: "Offer Extended",
      date: candidate.lastUpdated,
      type: "offer",
    });
  }
  if (candidate.status === CandidateStatus.Hired) {
    timeline.push({
      label: "Hired",
      date: candidate.lastUpdated,
      type: "hired",
    });
  }
  if (candidate.status === CandidateStatus.Rejected) {
    timeline.push({
      label: "Rejected",
      date: candidate.lastUpdated,
      type: "rejected",
    });
  }

  // Sort timeline by date
  timeline.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  return (
    <div className="space-y-4 sticky top-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Candidate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center text-center space-y-3">
            <Avatar className="h-20 w-20">
              <AvatarImage src={candidate.photo} alt={candidate.name} />
              <AvatarFallback>
                {candidate.name.split(' ').map((n: string) => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-medium text-lg">{candidate.name}</h3>
              <p className="text-sm text-muted-foreground">{candidate.jobTitle}</p>
              <div className="flex items-center justify-center gap-2 mt-1">
                <Badge variant="secondary">{candidate.status}</Badge>
              </div>
              <div className="flex items-center justify-center mt-3 text-sm gap-1">
                <Mail className="h-3 w-3" />
                <span className="text-muted-foreground">{candidate.email}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Application Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {timeline.map((event, idx) => (
              <div key={idx} className="relative pl-6 pb-3 border-l-2 border-muted">
                <div className={`absolute left-[-0.35rem] top-1 h-3 w-3 rounded-full ${
                  event.type === CandidateStatus.Applied ? 'bg-blue-500' :
                  event.type === CandidateStatus.Screening ? 'bg-purple-500' :
                  event.type === CandidateStatus.Interview ? 'bg-amber-500' :
                  event.type === CandidateStatus.Offer ? 'bg-green-500' :
                  event.type === CandidateStatus.Hired ? 'bg-green-700' :
                  event.type === CandidateStatus.Rejected ? 'bg-red-500' : 'bg-gray-400'
                }`} />
                <h4 className="text-sm font-medium">{event.label}</h4>
                <p className="text-xs text-muted-foreground">
                  {event.date ? new Date(event.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) : ''}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CandidateSummary;
