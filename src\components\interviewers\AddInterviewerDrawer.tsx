import { useState } from 'react';
import { Save, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetFooter,
} from "@/components/ui/sheet";
import { BackendInterviewerDetail, InterviewerDetail } from '@/hooks/useInterviewers';

interface AddInterviewerDrawerProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (interviewer: Partial<BackendInterviewerDetail>) => void;
}

export const AddInterviewerDrawer = ({
  isOpen,
  onOpenChange,
  onSave,
}: AddInterviewerDrawerProps) => {
  const [formData, setFormData] = useState<Partial<BackendInterviewerDetail>>({
    full_name: '',
    email: '',
    phone: '',
    job_title: '',
    department: '',
    location: '',
    areas_of_expertise: [],
    bio: '',
    join_date: new Date().toISOString().split('T')[0],
    employee_id: '',
    employment_type: '',
    designation: '',
    education_degree: '',
    education_institution: '',
    education_year: '',
    work_history_position: '',
    work_history_company: '',
    work_history_duration: '',
    is_available: true,
    interview_types: [],
    max_interviews_per_day: 3,
    preferred_time_slots: {},
  });

  const handleInputChange = (field: keyof BackendInterviewerDetail, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleExpertiseChange = (value: string) => {
    const expertiseArray = value.split(',').map(item => item.trim());
    setFormData(prev => ({
      ...prev,
      areas_of_expertise: expertiseArray
    }));
  };

  const handlePreferredTimeSlotsChange = (day: string, slotsString: string) => {
    const slotsArray = slotsString.split(',').map(s => s.trim());
    setFormData(prev => ({
      ...prev,
      preferred_time_slots: {
        ...(prev.preferred_time_slots || {}),
        [day]: slotsArray
      }
    }));
  };

  const handleSubmit = () => {
    const dataToSend: Partial<BackendInterviewerDetail> = {};

    // Conditionally add fields to dataToSend only if they have a value
    if (formData.full_name) dataToSend.full_name = formData.full_name;
    if (formData.email) dataToSend.email = formData.email;
    if (formData.phone) dataToSend.phone = formData.phone;
    if (formData.job_title) dataToSend.job_title = formData.job_title;
    if (formData.department) dataToSend.department = formData.department;
    if (formData.location) dataToSend.location = formData.location;
    if (formData.employee_id) dataToSend.employee_id = formData.employee_id;
    if (formData.employment_type) dataToSend.employment_type = formData.employment_type;
    if (formData.designation) dataToSend.designation = formData.designation;
    if (formData.join_date) dataToSend.join_date = formData.join_date;
    if (formData.bio) dataToSend.bio = formData.bio;
    if (formData.education_degree) dataToSend.education_degree = formData.education_degree;
    if (formData.education_institution) dataToSend.education_institution = formData.education_institution;
    if (formData.education_year) dataToSend.education_year = formData.education_year;
    if (formData.work_history_position) dataToSend.work_history_position = formData.work_history_position;
    if (formData.work_history_company) dataToSend.work_history_company = formData.work_history_company;
    if (formData.work_history_duration) dataToSend.work_history_duration = formData.work_history_duration;
    if (formData.is_available !== undefined) dataToSend.is_available = formData.is_available;
    if (formData.max_interviews_per_day !== undefined) dataToSend.max_interviews_per_day = formData.max_interviews_per_day;

    // Handle arrays and objects separately, allow empty arrays/objects if no data
    if (formData.areas_of_expertise && formData.areas_of_expertise.length > 0) {
      dataToSend.areas_of_expertise = formData.areas_of_expertise;
    }
    if (formData.interview_types && formData.interview_types.length > 0) {
      dataToSend.interview_types = formData.interview_types;
    }
    if (formData.preferred_time_slots && Object.keys(formData.preferred_time_slots).length > 0) {
      dataToSend.preferred_time_slots = formData.preferred_time_slots;
    }

    onSave(dataToSend);
    setFormData({
      full_name: '',
      email: '',
      phone: '',
      job_title: '',
      department: '',
      location: '',
      areas_of_expertise: [],
      bio: '',
      join_date: new Date().toISOString().split('T')[0],
      employee_id: '',
      employment_type: '',
      designation: '',
      education_degree: '',
      education_institution: '',
      education_year: '',
      work_history_position: '',
      work_history_company: '',
      work_history_duration: '',
      is_available: true,
      interview_types: [],
      max_interviews_per_day: 3,
      preferred_time_slots: {},
    });
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent 
        side="right" 
        className="min-w-[70vw] p-6 overflow-y-auto"
      >
        <SheetHeader className="mb-6">
          <SheetTitle>Add New Interviewer</SheetTitle>
        </SheetHeader>
        
        <div className="space-y-6 overflow-y-auto max-h-[calc(100vh-200px)]">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="full_name">Full Name *</Label>
                <Input 
                  id="full_name" 
                  value={formData.full_name || ''} 
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  placeholder="Enter full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input 
                  id="email" 
                  type="email" 
                  value={formData.email || ''} 
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="Enter email address"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone *</Label>
                <Input 
                  id="phone" 
                  value={formData.phone || ''} 
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="Enter phone number"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="job_title">Job Title *</Label>
                <Input 
                  id="job_title" 
                  value={formData.job_title || ''} 
                  onChange={(e) => handleInputChange('job_title', e.target.value)}
                  placeholder="Enter job title"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">Department *</Label>
                <Input 
                  id="department" 
                  value={formData.department || ''} 
                  onChange={(e) => handleInputChange('department', e.target.value)}
                  placeholder="Enter department"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">Location *</Label>
                <Input 
                  id="location" 
                  value={formData.location || ''} 
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder="Enter location"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Employment Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employee_id">Employee ID *</Label>
                <Input 
                  id="employee_id" 
                  value={formData.employee_id || ''} 
                  onChange={(e) => handleInputChange('employee_id', e.target.value)}
                  placeholder="Enter employee ID"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="employment_type">Employment Type *</Label>
                <Input 
                  id="employment_type" 
                  value={formData.employment_type || ''} 
                  onChange={(e) => handleInputChange('employment_type', e.target.value.toLowerCase())}
                  placeholder="Full-time, Part-time, Contract"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="designation">Designation *</Label>
                <Input 
                  id="designation" 
                  value={formData.designation || ''} 
                  onChange={(e) => handleInputChange('designation', e.target.value)}
                  placeholder="Enter designation"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="join_date">Join Date *</Label>
                <Input 
                  id="join_date" 
                  type="date" 
                  value={formData.join_date || ''} 
                  onChange={(e) => handleInputChange('join_date', e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Skills & Bio</h3>
            <div className="space-y-2">
              <Label htmlFor="areas_of_expertise">Areas of Expertise (comma separated) *</Label>
              <Input 
                id="areas_of_expertise" 
                value={formData.areas_of_expertise?.join(', ') || ''} 
                onChange={(e) => handleExpertiseChange(e.target.value)}
                placeholder="JavaScript, React, Node.js"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bio">Bio</Label>
              <Textarea 
                id="bio" 
                value={formData.bio || ''} 
                onChange={(e) => handleInputChange('bio', e.target.value)}
                placeholder="Enter professional bio"
              />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Education</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="education_degree">Degree</Label>
                <Input 
                  id="education_degree" 
                  value={formData.education_degree || ''} 
                  onChange={(e) => handleInputChange('education_degree', e.target.value)}
                  placeholder="Enter degree"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="education_institution">Institution</Label>
                <Input 
                  id="education_institution" 
                  value={formData.education_institution || ''} 
                  onChange={(e) => handleInputChange('education_institution', e.target.value)}
                  placeholder="Enter institution"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="education_year">Year</Label>
                <Input 
                  id="education_year" 
                  value={formData.education_year || ''} 
                  onChange={(e) => handleInputChange('education_year', e.target.value)}
                  placeholder="Enter year"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Work History</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="work_history_position">Position</Label>
                <Input 
                  id="work_history_position" 
                  value={formData.work_history_position || ''} 
                  onChange={(e) => handleInputChange('work_history_position', e.target.value)}
                  placeholder="Enter position"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="work_history_company">Company</Label>
                <Input 
                  id="work_history_company" 
                  value={formData.work_history_company || ''} 
                  onChange={(e) => handleInputChange('work_history_company', e.target.value)}
                  placeholder="Enter company"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="work_history_duration">Duration</Label>
                <Input 
                  id="work_history_duration" 
                  value={formData.work_history_duration || ''} 
                  onChange={(e) => handleInputChange('work_history_duration', e.target.value)}
                  placeholder="2019 - Present"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Availability and Interview Preferences</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="is_available">Is Available</Label>
                <Input 
                  id="is_available" 
                  type="checkbox" 
                  checked={formData.is_available} 
                  onChange={(e) => handleInputChange('is_available', e.target.checked)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="interview_types">Interview Types (comma separated)</Label>
                <Input 
                  id="interview_types" 
                  value={formData.interview_types?.join(', ') || ''} 
                  onChange={(e) => handleInputChange('interview_types', e.target.value.split(',').map(s => s.trim()))}
                  placeholder="technical, HR, behavioral"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max_interviews_per_day">Max Interviews Per Day</Label>
                <Input 
                  id="max_interviews_per_day" 
                  type="number" 
                  value={formData.max_interviews_per_day || 0} 
                  onChange={(e) => handleInputChange('max_interviews_per_day', parseInt(e.target.value))}
                  placeholder="3"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="preferred_time_slots">Preferred Time Slots (e.g., monday: 9:00-12:00, 14:00-16:00)</Label>
              <Input 
                id="preferred_time_slots_monday" 
                placeholder="Monday: 9:00-12:00, 14:00-16:00"
                value={formData.preferred_time_slots?.monday?.join(', ') || ''}
                onChange={(e) => handlePreferredTimeSlotsChange('monday', e.target.value)}
              />
              <Input 
                id="preferred_time_slots_friday" 
                placeholder="Friday: 09:00-11:00"
                value={formData.preferred_time_slots?.friday?.join(', ') || ''}
                onChange={(e) => handlePreferredTimeSlotsChange('friday', e.target.value)}
              />
            </div>
          </div>
        </div>
        
        <SheetFooter className="mt-6 flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            <Save className="h-4 w-4 mr-2" />
            Save Interviewer
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
