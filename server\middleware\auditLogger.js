import { logger } from '../utils/logger.js';

export const auditLogger = (req, res, next) => {
  const startTime = Date.now();

  // Capture the original end function
  const originalEnd = res.end;

  // Override the end function
  res.end = function(...args) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    logger.info('AUDIT', {
      userId: req.user?.id,
      action: req.method,
      resource: req.originalUrl,
      status: res.statusCode,
      duration,
      metadata: {
        ip: req.ip,
        userAgent: req.get('user-agent'),
        params: req.params,
        query: req.query,
        // Don't log sensitive body data
        body: req.method === 'GET' ? undefined : '***'
      }
    });

    // Call the original end function
    originalEnd.apply(res, args);
  };

  next();
};