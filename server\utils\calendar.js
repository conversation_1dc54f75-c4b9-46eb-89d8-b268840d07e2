import { google } from 'googleapis';
import { logger } from './logger.js';

const calendar = google.calendar({
  version: 'v3',
  auth: new google.auth.JWT(
    process.env.GOOGLE_CLIENT_EMAIL,
    null,
    process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    ['https://www.googleapis.com/auth/calendar']
  )
});

export const calendarUtils = {
  async createGoogleEvent(eventDetails) {
    try {
      const event = {
        summary: eventDetails.summary,
        description: eventDetails.description,
        start: {
          dateTime: eventDetails.startTime,
          timeZone: eventDetails.timeZone || 'UTC'
        },
        end: {
          dateTime: eventDetails.endTime,
          timeZone: eventDetails.timeZone || 'UTC'
        },
        attendees: eventDetails.attendees.map(email => ({ email })),
        conferenceData: {
          createRequest: {
            requestId: `${Date.now()}-${Math.random().toString(36).substring(7)}`,
            conferenceSolutionKey: { type: 'hangoutsMeet' }
          }
        }
      };

      const response = await calendar.events.insert({
        calendarId: 'primary',
        resource: event,
        conferenceDataVersion: 1,
        sendNotifications: true
      });

      return {
        success: true,
        eventId: response.data.id,
        meetLink: response.data.hangoutLink
      };
    } catch (error) {
      logger.error('Google Calendar event creation failed:', error);
      throw new Error('Failed to create calendar event');
    }
  },

  async cancelGoogleEvent(eventId) {
    try {
      await calendar.events.delete({
        calendarId: 'primary',
        eventId
      });

      return { success: true };
    } catch (error) {
      logger.error('Google Calendar event cancellation failed:', error);
      throw new Error('Failed to cancel calendar event');
    }
  }
};