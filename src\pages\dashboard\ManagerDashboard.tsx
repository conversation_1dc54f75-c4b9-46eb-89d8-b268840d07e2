import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StatsCard } from '@/components/dashboard/StatsCard';
import { ActivityItem } from '@/components/dashboard/ActivityItem';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Users, Briefcase, CheckCircle, AlertCircle, BarChart3 } from 'lucide-react';
import { useAuth } from '@/components/auth/AuthProvider';
import { Badge } from '@/components/ui/badge';

const ManagerDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Manager Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.full_name || 'Manager'}!
          </p>
        </div>
        <div className="flex gap-2">
          <Button size="sm">
            <Users size={16} className="mr-2" />
            Review Candidates
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard 
          title="Open Positions" 
          value={loading ? "..." : "8"}
          description="4 in your department"
          icon={<Briefcase size={16} />}
        />
        <StatsCard 
          title="Candidates To Review" 
          value={loading ? "..." : "14"}
          trend="up"
          trendValue="5 new today"
          icon={<Users size={16} />}
        />
        <StatsCard 
          title="Approved Hires" 
          value={loading ? "..." : "6"}
          description="This quarter"
          icon={<CheckCircle size={16} />}
        />
        <StatsCard 
          title="Time to Approve" 
          value={loading ? "..." : "2.5 days"}
          trend="down"
          trendValue="1.2 days improvement"
          icon={<AlertCircle size={16} />}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-7">
        <Card className="md:col-span-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Pending Approvals</CardTitle>
              <CardDescription>Candidates awaiting your review</CardDescription>
            </div>
            <Button variant="outline" size="sm">View All</Button>
          </CardHeader>
          <CardContent className="px-2">
            {loading ? (
              <div className="space-y-2">
                <div className="h-12 bg-muted animate-pulse rounded-md" />
                <div className="h-12 bg-muted animate-pulse rounded-md" />
                <div className="h-12 bg-muted animate-pulse rounded-md" />
              </div>
            ) : (
              <div className="space-y-0">
                <div className="flex items-center justify-between p-3 hover:bg-muted rounded-md">
                  <div className="flex items-center space-x-4">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Emily Lee</p>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">Senior Developer</Badge>
                        <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 text-xs">87% Match</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">View</Button>
                    <Button variant="default" size="sm">Approve</Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 hover:bg-muted rounded-md">
                  <div className="flex items-center space-x-4">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Daniel Kim</p>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">Product Manager</Badge>
                        <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 text-xs">92% Match</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">View</Button>
                    <Button variant="default" size="sm">Approve</Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 hover:bg-muted rounded-md">
                  <div className="flex items-center space-x-4">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Michael Chen</p>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">UX Designer</Badge>
                        <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 text-xs">73% Match</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">View</Button>
                    <Button variant="default" size="sm">Approve</Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Team Hiring Status</CardTitle>
            <CardDescription>Current recruitment progress</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="h-[240px] bg-muted animate-pulse rounded-md" />
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Engineering</div>
                    <div className="text-sm text-muted-foreground">3 of 5 filled</div>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div className="bg-blue-500 h-full rounded-full" style={{ width: '60%' }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Product</div>
                    <div className="text-sm text-muted-foreground">2 of 2 filled</div>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div className="bg-green-500 h-full rounded-full" style={{ width: '100%' }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Design</div>
                    <div className="text-sm text-muted-foreground">1 of 3 filled</div>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div className="bg-amber-500 h-full rounded-full" style={{ width: '33%' }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Marketing</div>
                    <div className="text-sm text-muted-foreground">0 of 2 filled</div>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div className="bg-red-500 h-full rounded-full" style={{ width: '0%' }}></div>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full mt-4" size="sm">
                  <BarChart3 size={16} className="mr-2" />
                  View Detailed Report
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="recent">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Activity</h2>
          <TabsList>
            <TabsTrigger value="recent">Recent</TabsTrigger>
            <TabsTrigger value="approvals">Your Approvals</TabsTrigger>
            <TabsTrigger value="meetings">Upcoming Meetings</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="recent" className="mt-4 space-y-4">
          {loading ? (
            <div className="space-y-2">
              <div className="h-12 bg-muted animate-pulse rounded-md" />
              <div className="h-12 bg-muted animate-pulse rounded-md" />
            </div>
          ) : (
            <div className="bg-white rounded-md border">
              <ActivityItem
                avatarFallback="JL"
                title={<>You approved <strong>Jessica Lee</strong> for final interview</>}
                description="Senior Product Manager position"
                timestamp="2 hours ago"
                status="success"
              />
              <ActivityItem
                avatarFallback="TR"
                title={<>You requested more information for <strong>Tom Roberts</strong></>}
                description="Frontend Developer application"
                timestamp="Yesterday"
                status="warning"
              />
              <ActivityItem
                avatarFallback="AW"
                title={<>You approved offer for <strong>Alex Wong</strong></>}
                description="UX Designer position - Starts June 1st"
                timestamp="3 days ago"
                status="success"
              />
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="approvals" className="mt-4">
          <div className="text-center text-muted-foreground py-8">
            No pending approvals in this category
          </div>
        </TabsContent>
        
        <TabsContent value="meetings" className="mt-4">
          <div className="text-center text-muted-foreground py-8">
            No upcoming meetings scheduled
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ManagerDashboard;
