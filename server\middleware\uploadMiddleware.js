import multer from 'multer';

const upload = multer({ 
    limits: { fileSize: 10 * 1024 * 1024 }, // Limit file size to 10MB
    storage: multer.memoryStorage(), // Store files in memory
    fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.(pdf|doc|docx)$/)) {
            return cb(new Error('Only PDF, DOC, and DOCX files are allowed!'), false);
        }
        cb(null, true);
    }
 });

export default upload;
