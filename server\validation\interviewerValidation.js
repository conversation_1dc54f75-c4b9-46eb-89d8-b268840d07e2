import { z } from 'zod';

const educationItemSchema = z.object({
  degree: z.string().min(1),
  institution: z.string().min(1),
  year: z.string().min(1)
});

const workHistoryItemSchema = z.object({
  position: z.string().min(1),
  company: z.string().min(1),
  duration: z.string().min(1)
});

export const createInterviewerSchema = z.object({
  user_id: z.string().uuid().optional(),
  full_name: z.string().max(100).optional(),
  email: z.string().email(),
  phone: z.string().regex(/^[+\d\s-]{10,}$/).optional(),
  job_title: z.string().max(100).optional(),
  department: z.string().max(100).optional(),
  location: z.string().max(255).optional(),
  employee_id: z.string().max(50).optional(),
  employment_type: z.enum(['full-time', 'part-time', 'contract', 'internship']).optional(),
  designation: z.string().max(100).optional(),
  join_date: z.string().min(1).optional(),
  areas_of_expertise: z.array(z.string()).optional(),
  bio: z.string().optional(),
  education_degree: z.string().max(100).optional(),
  education_institution: z.string().max(200).optional(),
  education_year: z.string().max(10).optional(),
  work_history_position: z.string().max(100).optional(),
  work_history_company: z.string().max(200).optional(),
  work_history_duration: z.string().max(50).optional(),
  is_available: z.boolean().optional(),
  interview_types: z.array(z.string()).optional(),
  max_interviews_per_day: z.number().int().min(1).max(10).optional(),
  preferred_time_slots: z.record(z.any()).optional()
});

export const updateInterviewerSchema = createInterviewerSchema.partial({
  user_id: true,
  email: true
});

export const updateAvailabilitySchema = z.object({
  is_available: z.boolean(),
  preferred_time_slots: z.record(z.any()),
  max_interviews_per_day: z.number().int().min(1).max(10)
}); 