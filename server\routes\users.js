import express from 'express';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { userController } from '../controllers/UserController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/users
 * @desc    Get all users with pagination
 * @access  Private/Admin
 */
router.get('/', 
  authenticate, 
  authorize(['admin']), 
  async<PERSON>andler(userController.getUsers)
);

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  Private/Admin
 */
router.get('/:id', 
  authenticate, 
  authorize(['admin']), 
  asyncHandler(userController.getUserById)
);

/**
 * @route   POST /api/users
 * @desc    Create a new user
 * @access  Private/Admin
 */
router.post('/', 
  authenticate, 
  authorize(['admin']), 
  async<PERSON><PERSON><PERSON>(userController.createUser)
);

/**
 * @route   PUT /api/users/:id
 * @desc    Update user
 * @access  Private/Admin
 */
router.put('/:id', 
  authenticate, 
  authorize(['admin']), 
  asyncHandler(userController.updateUser)
);

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete user
 * @access  Private/Admin
 */
router.delete('/:id', 
  authenticate, 
  authorize(['admin']), 
  asyncHandler(userController.deleteUser)
);

export default router;