import express from 'express';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { notificationController } from '../controllers/NotificationController.js';
import { authenticate } from '../middleware/authMiddleware.js';
import { authorize } from '../middleware/roleMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/notifications
 * @desc    Get all notifications with pagination
 * @access  Private
 */
router.get('/',
  authenticate,
  asyncHandler(notificationController.getNotifications)
);

/**
 * @route   POST /api/notifications/send
 * @desc    Send a new notification
 * @access  Private/Admin
 */
router.post('/send',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHand<PERSON>(notificationController.sendNotification)
);

export default router;