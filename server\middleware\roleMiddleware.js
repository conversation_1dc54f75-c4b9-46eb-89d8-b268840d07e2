import { responseHandler } from '../utils/responseHandler.js';
import { logger } from '../utils/logger.js';

export const authorize = (allowedRoles) => {
  return (req, res, next) => {
    try {
      if (!req.user || !req.user.role) {
        return responseHandler.unauthorized(res, 'User role not found');
      }

      if (!allowedRoles.includes(req.user.role)) {
        logger.warn(`Access denied for user ${req.user.id} with role ${req.user.role}`);
        return responseHandler.forbidden(res, 'Insufficient permissions');
      }

      next();
    } catch (error) {
      logger.error('Role check failed:', error);
      return responseHandler.error(res, 'Role verification failed');
    }
  };
};