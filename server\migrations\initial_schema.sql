-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create ENUMs for fixed value fields
CREATE TYPE job_status AS ENUM ('draft', 'published', 'closed', 'open');
CREATE TYPE employment_type AS ENUM ('full-time', 'part-time', 'contract', 'internship');
CREATE TYPE role_type AS ENUM ('admin', 'recruiter', 'candidate', 'hr', 'manager');
CREATE TYPE interview_mode AS ENUM ('online', 'offline');
CREATE TYPE notification_type AS ENUM ('email', 'slack', 'sms');
CREATE TYPE file_type AS ENUM ('resume', 'offer');
CREATE TYPE calendar_platform AS ENUM ('Google', 'Outlook');
CREATE TYPE application_status AS ENUM ('pending', 'screening', 'interview', 'offer', 'hired', 'rejected');
CREATE TYPE offer_status AS ENUM ('pending', 'accepted', 'rejected');
CREATE TYPE overall_recommendation_enum AS ENUM ('strong_hire', 'hire','neutral', 'no_hire','strong_no_hire');
CREATE TYPE sal_frequency AS ENUM ('monthly', 'yearly');

-- Base tables
CREATE TABLE roles (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    name role_type NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES roles(id),
    updated_by uuid REFERENCES roles(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

CREATE TABLE languages (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES roles(id),
    updated_by uuid REFERENCES roles(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

CREATE TABLE users (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role_id uuid REFERENCES roles(id) NOT NULL,
    language_preference VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES roles(id),
    updated_by uuid REFERENCES roles(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

-- Job related tables
CREATE TABLE job_posts (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT[] DEFAULT '{}',
    responsibilities TEXT[] DEFAULT '{}',
    department VARCHAR(100),
    location VARCHAR(255),
    employment_type employment_type DEFAULT 'full-time',
    status job_status DEFAULT 'draft',
    salary_min NUMERIC(12,2),
    salary_max NUMERIC(12,2),
    salary_frequency sal_frequency NOT NULL,
    tools_and_tech TEXT,
    good_to_have TEXT,
    experience_years INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id) NOT NULL,
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);
ALTER TABLE job_posts ADD COLUMN office_location VARCHAR(255);
ALTER TABLE job_posts ADD COLUMN creator_email VARCHAR(255);
ALTER TABLE job_posts ADD COLUMN skills TEXT[] DEFAULT '{}';

CREATE TABLE job_board_posts (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    job_post_id uuid REFERENCES job_posts(id) NOT NULL,
    platform VARCHAR(50) NOT NULL,
    external_url TEXT NOT NULL,
    posted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

-- Candidate related tables
CREATE TABLE applications (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    job_post_id uuid REFERENCES job_posts(id) NOT NULL,
    user_id uuid REFERENCES users(id) NOT NULL,
    status application_status DEFAULT 'pending',
    match_score INTEGER CHECK (match_score BETWEEN 0 AND 100),
    source VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false,
    UNIQUE (job_post_id, user_id) -- Prevent duplicate applications
);

CREATE TABLE files (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES users(id) NOT NULL,
    file_type file_type NOT NULL,
    file_url TEXT NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

CREATE TABLE resumes (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES users(id) NOT NULL,
    file_id uuid REFERENCES files(id) NOT NULL,
    parsed_data JSONB,
    skills TEXT[] DEFAULT '{}',
    experience TEXT,
    education TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

-- Interview related tables
CREATE TABLE interviews (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    application_id uuid REFERENCES applications(id) NOT NULL,
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
    interviewer_id uuid REFERENCES users(id) NOT NULL,
    mode interview_mode NOT NULL,
    feedback TEXT,
    stage VARCHAR(50),
    duration INTEGER CHECK (duration >= 15 AND duration <= 180),
    location TEXT,
    special_requirements TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

-- Feedback tables for interview feedback and criteria

CREATE TABLE feedbacks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  interview_id UUID NOT NULL REFERENCES interviews(id),
  candidate_id UUID NOT NULL REFERENCES users(id),
  overall_recommendation overall_recommendation_enum NOT NULL,
  overall_comments TEXT,
  interviewer TEXT,
  interview_date DATE,
  stage TEXT,
  recommendation_label TEXT,
  recommendation_color TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE feedback_criteria (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  feedback_id UUID NOT NULL REFERENCES feedbacks(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  score INTEGER NOT NULL CHECK (score >= 0),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE calendar_events (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    interview_id uuid REFERENCES interviews(id) NOT NULL,
    calendar_id VARCHAR(255),
    event_url TEXT,
    platform calendar_platform NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

CREATE TABLE feedback_forms (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    interview_id uuid REFERENCES interviews(id) NOT NULL,
    reviewer_id uuid REFERENCES users(id) NOT NULL,
    rating INTEGER CHECK (rating BETWEEN 1 AND 5) NOT NULL,
    skills_assessment JSONB,
    strengths TEXT[] DEFAULT '{}',
    weaknesses TEXT[] DEFAULT '{}',
    recommendation VARCHAR(50),
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

-- Other tables
CREATE TABLE notifications (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES users(id) NOT NULL,
    type notification_type NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

CREATE TABLE offers (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES users(id) NOT NULL,
    job_post_id uuid REFERENCES job_posts(id) NOT NULL,
    salary NUMERIC(12,2) NOT NULL,
    status offer_status DEFAULT 'pending',
    offer_letter_url TEXT,
    department VARCHAR(100),
    designation VARCHAR(100),
    employment_type employment_type,
    location VARCHAR(255),
    fixed_component NUMERIC(12,2),
    variable_component NUMERIC(12,2),
    joining_bonus NUMERIC(12,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

CREATE TABLE salary_recommendations (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    job_post_id uuid REFERENCES job_posts(id) NOT NULL,
    candidate_id uuid REFERENCES users(id) NOT NULL,
    recommended_salary NUMERIC(12,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

CREATE TABLE audit_logs (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES users(id) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

CREATE TABLE chat_logs (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES users(id) NOT NULL,
    message TEXT NOT NULL,
    sender VARCHAR(50) CHECK (sender IN ('user', 'bot')) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);


-- Add triggers for timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

DO $$
DECLARE
    t text;
BEGIN
    FOR t IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE' 
    LOOP
        EXECUTE format('
            CREATE TRIGGER set_timestamp
            BEFORE UPDATE ON %I
            FOR EACH ROW
            EXECUTE PROCEDURE update_updated_at_timestamp();
        ', t);
    END LOOP;
END
$$ language 'plpgsql';

-- Disable Row Level Security (RLS) for all tables
ALTER TABLE roles DISABLE ROW LEVEL SECURITY;
ALTER TABLE languages DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE job_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE job_board_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE applications DISABLE ROW LEVEL SECURITY;
ALTER TABLE files DISABLE ROW LEVEL SECURITY;
ALTER TABLE resumes DISABLE ROW LEVEL SECURITY;
ALTER TABLE interviews DISABLE ROW LEVEL SECURITY;
ALTER TABLE calendar_events DISABLE ROW LEVEL SECURITY;
ALTER TABLE feedback_forms DISABLE ROW LEVEL SECURITY;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE offers DISABLE ROW LEVEL SECURITY;
ALTER TABLE salary_recommendations DISABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE chat_logs DISABLE ROW LEVEL SECURITY;

-- Grant all privileges to authenticated users
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;


-- Add 'interviewer' to the existing role_type enum
ALTER TYPE role_type ADD VALUE 'interviewer';

-- Create interviewer table based on the form fields shown in the images
CREATE TABLE interviewers (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES users(id) NOT NULL UNIQUE, -- Links to users table
    
    -- Basic Information
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    job_title VARCHAR(100),
    department VARCHAR(100),
    location VARCHAR(255),
    
    -- Employment Details
    employee_id VARCHAR(50) UNIQUE,
    employment_type employment_type DEFAULT 'full-time',
    designation VARCHAR(100),
    join_date DATE,
    
    -- Skills & Bio
    areas_of_expertise TEXT[] DEFAULT '{}', -- Array for comma-separated skills
    bio TEXT,
    
    -- Education (stored as JSONB for flexibility)
    education JSONB DEFAULT '[]', -- Array of education objects: [{"degree": "", "institution": "", "year": ""}]
    
    -- Work History (stored as JSONB for flexibility)
    work_history JSONB DEFAULT '[]', -- Array of work history objects: [{"position": "", "company": "", "duration": ""}]
    
    -- Availability and Interview Preferences
    is_available BOOLEAN DEFAULT true,
    interview_types TEXT[] DEFAULT '{}', -- Types of interviews they can conduct
    max_interviews_per_day INTEGER DEFAULT 3,
    preferred_time_slots JSONB DEFAULT '{}', -- Store time preferences
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES users(id),
    updated_by uuid REFERENCES users(id),
    flag_active BOOLEAN DEFAULT true,
    flag_deleted BOOLEAN DEFAULT false
);

-- Add trigger for automatic timestamp updates
CREATE TRIGGER set_timestamp_interviewers
    BEFORE UPDATE ON interviewers
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_timestamp();

-- Disable Row Level Security for the new table
ALTER TABLE interviewers DISABLE ROW LEVEL SECURITY;

-- Grant privileges
GRANT ALL PRIVILEGES ON TABLE interviewers TO authenticated;

-- Create indexes for better performance
CREATE INDEX idx_interviewers_user_id ON interviewers(user_id);
CREATE INDEX idx_interviewers_employee_id ON interviewers(employee_id);
CREATE INDEX idx_interviewers_department ON interviewers(department);
CREATE INDEX idx_interviewers_is_available ON interviewers(is_available);
CREATE INDEX idx_interviewers_flag_active ON interviewers(flag_active);
CREATE INDEX idx_feedbacks_interview_id ON feedbacks(interview_id);
CREATE INDEX idx_feedbacks_candidate_id ON feedbacks(candidate_id);
CREATE INDEX idx_criteria_feedback_id ON feedback_criteria(feedback_id);

-- Remove the existing JSONB columns
ALTER TABLE interviewers DROP COLUMN IF EXISTS education;
ALTER TABLE interviewers DROP COLUMN IF EXISTS work_history;

-- Add individual Education columns
ALTER TABLE interviewers ADD COLUMN education_degree VARCHAR(100);
ALTER TABLE interviewers ADD COLUMN education_institution VARCHAR(200);
ALTER TABLE interviewers ADD COLUMN education_year VARCHAR(10);

-- Add individual Work History columns
ALTER TABLE interviewers ADD COLUMN work_history_position VARCHAR(100);
ALTER TABLE interviewers ADD COLUMN work_history_company VARCHAR(200);
ALTER TABLE interviewers ADD COLUMN work_history_duration VARCHAR(50);
