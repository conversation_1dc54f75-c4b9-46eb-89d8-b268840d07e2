import { google } from 'googleapis';
import { supabase } from '../config/db.js';
import { messages } from '../utils/messages.utils.js';

const CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI ;
const SCOPES = [
  'https://www.googleapis.com/auth/calendar',
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/userinfo.profile',
];

const oauth2Client = new google.auth.OAuth2(
  CLIENT_ID,
  CLIENT_SECRET,
  REDIRECT_URI
);

function generateAvailableSlots(timeMin, timeMax, busySlots, durationMinutes = 30) {
  const slots = [];
  const start = new Date(timeMin);
  const end = new Date(timeMax);
  let current = new Date(start);

  while (current < end) {
    const slotStart = new Date(current);
    const slotEnd = new Date(current.getTime() + durationMinutes * 60000);
    if (slotEnd > end) break;

    // Check if slot is within 11:00 AM to 2:00 PM local time
    const localStart = new Date(slotStart);
    const localEnd = new Date(slotEnd);
    const startHour = localStart.getHours();
    const endHour = localEnd.getHours();
    if (startHour < 11 || endHour > 14 || (endHour === 14 && localEnd.getMinutes() > 0)) {
      current.setTime(current.getTime() + durationMinutes * 60000);
      continue;
    }

    // Check for overlap with busy slots
    const overlaps = busySlots.some(busy => {
      const busyStart = new Date(busy.start);
      const busyEnd = new Date(busy.end);
      return slotStart < busyEnd && slotEnd > busyStart;
    });
    if (!overlaps) {
      slots.push(slotStart.toISOString());
    }
    current.setTime(current.getTime() + durationMinutes * 60000);
  }
  return slots;
}

export const googleService = {
  getAuthUrl: (recruiterId, service) => {
    // State can be used to pass recruiterId and service for later use
    return oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: SCOPES,
      prompt: 'consent',
      state: JSON.stringify({ recruiterId, service }),
    });
  },

  exchangeCodeForTokens: async (code, recruiterId) => {
    const { tokens } = await oauth2Client.getToken(code);
    const expiresAt = new Date(Date.now() + (tokens.expiry_date - Date.now()));
    // Upsert credentials
    const { error } = await supabase
      .from('google_credentials')
      .upsert({
        recruiter_id: recruiterId,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        expires_at: expiresAt.toISOString(),
      }, { onConflict: ['recruiter_id'] });
    if (error) throw error;
    return tokens;
  },

  async getValidAccessToken(recruiterId) {
    const { data, error } = await supabase
      .from('google_credentials')
      .select('access_token, refresh_token, expires_at')
      .eq('recruiter_id', recruiterId)
      .single();
    if (error || !data) throw new Error('No Google credentials found for this recruiter.');

    let { access_token, refresh_token, expires_at } = data;

    if (new Date() < new Date(expires_at)) {
      return access_token;
    }

    const oauth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);
    oauth2Client.setCredentials({ refresh_token });
    const { credentials } = await oauth2Client.refreshAccessToken();
    access_token = credentials.access_token;
    expires_at = new Date(Date.now() + credentials.expiry_date - Date.now());

    const { error: updateError } = await supabase
      .from('google_credentials')
      .update({ access_token, expires_at: expires_at.toISOString() })
      .eq('recruiter_id', recruiterId);
    if (updateError) throw updateError;

    return access_token;
  },

  async getAvailableSlots(recruiterId, timeMin, timeMax, durationMinutes = 30) {
    // Fetch google_credentials from users table
    const { data: user, error } = await supabase
      .from('users')
      .select('google_credentials')
      .eq('id', recruiterId)
      .single();
    if (error || !user || !user.google_credentials) {
      throw new Error(messages.GOOGLE_OAUTH_SAVE_ERROR);
    }
    let creds;
    try {
      creds = typeof user.google_credentials === 'string'
        ? JSON.parse(user.google_credentials)
        : user.google_credentials;
    } catch (e) {
      throw new Error(messages.GOOGLE_OAUTH_SAVE_ERROR);
    }
    const { access_token, refresh_token } = creds;
    if (!access_token) throw new Error(messages.GOOGLE_OAUTH_SAVE_ERROR);

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);
    oauth2Client.setCredentials({ access_token, refresh_token });

    // Use OAuth2 client for calendar
    const calendar = google.calendar({ version: 'v3', auth: oauth2Client });
    const { data } = await calendar.freebusy.query({
      requestBody: {
        timeMin,
        timeMax,
        items: [{ id: 'primary' }],
      },
    });
    const busySlots = data.calendars.primary.busy;
    // Generate 30-min slots between 11:00 AM and 2:00 PM, excluding busy slots
    return generateAvailableSlots(timeMin, timeMax, busySlots, durationMinutes);
  },

  async createGoogleMeetEvent({ recruiterId, candidateEmail, interviewerEmail, scheduledTime, duration = 30, summary, description }) {
    // Fetch recruiter credentials
    const { data: user, error } = await supabase
      .from('users')
      .select('google_credentials, email, full_name')
      .eq('id', recruiterId)
      .single();
    if (error || !user || !user.google_credentials) {
      throw new Error('No Google credentials found for this recruiter.');
    }
    let creds;
    try {
      creds = typeof user.google_credentials === 'string'
        ? JSON.parse(user.google_credentials)
        : user.google_credentials;
    } catch (e) {
      throw new Error('Invalid Google credentials format.');
    }
    const { access_token, refresh_token } = creds;
    if (!access_token) throw new Error('No access token found in Google credentials.');

    // Create OAuth2 client
    const { google } = await import('googleapis');
    const oauth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);
    oauth2Client.setCredentials({ access_token, refresh_token });

    // Create the event
    const calendar = google.calendar({ version: 'v3', auth: oauth2Client });
    const start = new Date(scheduledTime);
    const end = new Date(start.getTime() + duration * 60000);
    const event = {
      summary: summary || 'Interview',
      description: description || 'Interview scheduled via AI Recruiter',
      start: { dateTime: start.toISOString() },
      end: { dateTime: end.toISOString() },
      attendees: [
        { email: candidateEmail },
        { email: interviewerEmail },
        { email: user.email }
      ],
      conferenceData: {
        createRequest: { requestId: `meet-${Date.now()}` }
      }
    };
    const { data: eventData } = await calendar.events.insert({
      calendarId: 'primary',
      resource: event,
      conferenceDataVersion: 1
    });
    const meetLink = eventData.conferenceData?.entryPoints?.find(e => e.entryPointType === 'video')?.uri;
    return meetLink;
  }
}; 