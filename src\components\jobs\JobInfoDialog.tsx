import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Calendar, MapPin, Briefcase, Users, DollarSign, FileText } from "lucide-react";
import { type Job } from "@/hooks/useJobs";
import { formatWorkLocation } from "@/lib/utils";

interface JobInfoDialogProps {
  job: Job | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function JobInfoDialog({ job, open, onOpenChange }: JobInfoDialogProps) {
  if (!job) return null;

  // Capitalize first letter of status
  const capitalizedStatus = job.status 
    ? job.status.charAt(0).toUpperCase() + job.status.slice(1) 
    : "Unknown";

  // Format salary range
  const formatSalary = () => {
    if (job.salary_min && job.salary_max) {
      return `$${job.salary_min.toLocaleString()} - $${job.salary_max.toLocale<PERSON>tring()}`;
    } else if (job.salary_min) {
      return `$${job.salary_min.toLocaleString()}+`;
    } else if (job.salary_max) {
      return `Up to $${job.salary_max.toLocaleString()}`;
    }
    return "Competitive";
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">{job.title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 mt-2">
          {/* Department */}
          {job.department && (
            <div className="flex items-start gap-3">
              <Briefcase className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-sm font-medium">Department</p>
                <p className="text-sm text-muted-foreground">{job.department}</p>
              </div>
            </div>
          )}
          
          {/* Location */}
          <div className="flex items-start gap-3">
            <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <p className="text-sm font-medium">Location</p>
              <p className="text-sm text-muted-foreground">{formatWorkLocation(job.location)}</p>
              {job.office_location && job.office_location !== job.location && (
                <p className="text-sm text-muted-foreground mt-1">
                  Office: {job.office_location}
                </p>
              )}
            </div>
          </div>
          
          {/* Employment Type */}
          {job.employment_type && (
            <div className="flex items-start gap-3">
              <Users className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-sm font-medium">Employment Type</p>
                <p className="text-sm text-muted-foreground capitalize">
                  {job.employment_type.replace('-', ' ')}
                </p>
              </div>
            </div>
          )}
          
          {/* Experience Years */}
          {job.experience_years && (
            <div className="flex items-start gap-3">
              <Briefcase className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-sm font-medium">Experience Required</p>
                <p className="text-sm text-muted-foreground">
                  {job.experience_years} year{job.experience_years !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
          )}
          
          {/* Salary */}
          <div className="flex items-start gap-3">
            <DollarSign className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <p className="text-sm font-medium">Salary Range</p>
              <p className="text-sm text-muted-foreground">{formatSalary()}</p>
            </div>
          </div>
          
          {/* Tools and Tech */}
          {job.tools_and_tech && (
            <div className="flex items-start gap-3">
              <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-sm font-medium">Tools & Technology</p>
                <p className="text-sm text-muted-foreground">{job.tools_and_tech}</p>
              </div>
            </div>
          )}
          
          {/* Good to Have */}
          {job.good_to_have && (
            <div className="flex items-start gap-3">
              <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-sm font-medium">Good to Have</p>
                <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
                  {job.good_to_have.split(',').filter(item => item.trim()).map((item, i) => (
                    <li key={i}>{item.trim().replace(/["'()\[\]{}]/g, '')}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}
          
          {/* Date Posted */}
          <div className="flex items-start gap-3">
            <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <p className="text-sm font-medium">Posted On</p>
              <p className="text-sm text-muted-foreground">
                {formatDate(job.created_at)}
              </p>
            </div>
          </div>
          
          {/* Status */}
          {job.status && (
            <div className="flex items-center gap-2 mt-4">
              <p className="text-sm font-medium">Status:</p>
              <Badge variant="outline" className="capitalize">
                {capitalizedStatus}
              </Badge>
            </div>
          )}
          
          {/* Applicants (placeholder for now) */}
          <div className="flex items-center gap-2">
            <p className="text-sm font-medium">Applicants:</p>
            <Badge variant="secondary">0</Badge>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}