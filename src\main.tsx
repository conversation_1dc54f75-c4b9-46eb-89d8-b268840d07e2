
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
// import { ContactFormModal } from './components/examples/ContactFormModal.tsx'

// Just for demo purposes to show the reusable modals on the home page
// if (window.location.pathname === '/') {
//   // Add a small demo of the reusable modal to the home page
//   const demoEl = document.createElement('div')
//   demoEl.className = 'fixed bottom-4 right-4'
//   document.body.appendChild(demoEl)
//   // createRoot(demoEl).render(<ContactFormModal />)
// }

createRoot(document.getElementById("root")!).render(<App />)
