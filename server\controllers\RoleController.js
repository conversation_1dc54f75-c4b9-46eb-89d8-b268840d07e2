import { roleService } from '../services/roleService.js';

export const roleController = {
  async getRoles(req, res) {
    const result = await roleService.getRoles();
    res.json(result);
  },

  async createRole(req, res) {
    const result = await roleService.createRole(req.body);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.status(201).json(result);
  }
};