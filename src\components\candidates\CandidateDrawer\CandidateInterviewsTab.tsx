import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Video, Calendar, Clock, User, Link2 } from "lucide-react";
import { useState, useRef } from "react";
import { USE_CANDIDATES_CONSTANTS } from '@/utils/Constant';
import { CandidateStatus, useCandidates } from "@/hooks/useCandidates";

const StatusOptions = [
  { label: "Applied", value: CandidateStatus.Applied },
  { label: "Screening", value: CandidateStatus.Screening },
  { label: "Interview", value: CandidateStatus.Interview },
  { label: "Offer", value: CandidateStatus.Offer },
  { label: "Rejected", value: CandidateStatus.Rejected },
  { label: "Hired", value: CandidateStatus.Hired },
];

interface CandidateInterviewsTabProps {
  candidateId: string;
  closeDrawer: () => void;
  interviews: any[];
  interviewsLoading: boolean;
  interviewsError: string | null;
  setIsScheduleDrawerOpen: (open: boolean) => void;
  toCamelCase: (str: string) => string;
}

const CandidateInterviewsTab: React.FC<CandidateInterviewsTabProps> = ({
  candidateId,
  closeDrawer,
  interviews,
  interviewsLoading,
  interviewsError,
  setIsScheduleDrawerOpen,
  toCamelCase,
})=> {
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<string | null>(null);

  const handleUpdateStatus = (newStatus: string) => {
    setPendingStatus(newStatus);
    setIsConfirmModalOpen(true);
  };

  const { updateCandidateStatus } = useCandidates();

  const confirmStatusChange = async () => {
    if (pendingStatus && candidateId) {
      const success = await updateCandidateStatus(candidateId, pendingStatus as CandidateStatus);
      if (success) {
        console.log(`Candidate status confirmed to: ${pendingStatus}`);
        setPendingStatus(null);
        setIsConfirmModalOpen(false);
        closeDrawer();
      }
    }
  };
  return (
    <Card className="border border-gray-100 shadow-sm">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base font-medium">Interview Rounds</h3>
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => setIsScheduleDrawerOpen(true)}
          >
            Schedule Interview
          </Button>
          <AlertDialog open={isConfirmModalOpen} onOpenChange={setIsConfirmModalOpen}>
            <Select onValueChange={handleUpdateStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Change Status" />
              </SelectTrigger>
              <SelectContent>
                {StatusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to move candidate to {" "}
                  <span className="font-semibold text-blue-600">
                    {StatusOptions.find(opt => opt.value === pendingStatus)?.label || pendingStatus}
                  </span>
                  ?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={confirmStatusChange}>Confirm</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
        <div className="space-y-4">
          {interviewsLoading ? (
            <p className="text-sm text-gray-500 italic">{USE_CANDIDATES_CONSTANTS.TOAST.FETCH_ALL_ERROR}</p>
          ) : interviewsError ? (
            <p className="text-sm text-red-500 italic">{interviewsError}</p>
          ) : interviews.length > 0 ? (
            interviews.map((interview, idx) => (
              <div key={idx} className="p-4 border rounded-md bg-slate-50">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-2">
                  <div className="flex items-center gap-2">
                    <Video className="w-4 h-4 text-blue-500" />
                    <span className="font-semibold text-blue-700">{toCamelCase(interview.type)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-amber-100 text-amber-800"><User className="w-3 h-3 mr-1" />{interview.stage}</Badge>
                  </div>
                </div>
                <div className="flex flex-wrap gap-4 text-sm text-gray-700 mb-2">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">Date:</span>
                    <span>{interview.scheduled_date}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">Time:</span>
                    <span>{interview.scheduled_time}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">Interviewer:</span>
                    <span>{interview.interviewerName}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2 mt-2">
                  <Link2 className="w-4 h-4 text-green-600" />
                  <span className="font-medium">Meet Link:</span>
                  <a href={interview.meetLink} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline break-all">{interview.meetLink}</a>
                </div>
              </div>
            ))
          ) : (
            <p className="text-sm text-gray-500 italic">No interviews scheduled yet.</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CandidateInterviewsTab;