import { resumeService } from '../services/resumeService.js';

export const resumeController = {
  async uploadResume(req, res) {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const result = await resumeService.uploadResume(req.file, req.user.id);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.status(201).json(result);
  },

  async getResumes(req, res) {
    const result = await resumeService.getResumes(req.query);
    res.json(result);
  },

  async getResumeById(req, res) {
    const result = await resumeService.getResumeById(req.params.id);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  },

  async deleteResume(req, res) {
    const result = await resumeService.deleteResume(req.params.id);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  }
};