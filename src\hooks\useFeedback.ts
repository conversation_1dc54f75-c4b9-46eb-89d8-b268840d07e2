import { API_CONFIG } from "@/utils/Constant";
import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface FeedbackCriteria {
  id: string;
  title: string;
  description: string;
  score: number;
  notes: string;
}

export interface FeedbackData {
  stage: string;
  overall_recommendation: string;
  overall_comments: string;
  criteria: FeedbackCriteria[];
}

export function useFeedback() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const getAuthToken = () => {
    return localStorage.getItem("token");
  };

  // Get feedback for a specific interview
  const getFeedback = useCallback(async (interviewId: string) => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError("Auth token not found");
      toast.error("Authentication error");
      setLoading(false);
      return null;
    }
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.FEEDBACK}/${interviewId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || "Failed to fetch feedback");
      }
      return result;
    } catch (err: any) {
      setError(err.message || "Failed to fetch feedback");
      toast.error("Failed to fetch feedback", { description: err.message });
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Submit feedback for an interview
  const submitFeedback = useCallback(async (interviewId: string, feedback: FeedbackData) => {
    setLoading(true);
    setError(null);
    setSuccess(false);
    const token = getAuthToken();
    if (!token) {
      setError("Auth token not found");
      toast.error("Authentication error");
      setLoading(false);
      return null;
    }
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.FEEDBACK}/${interviewId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(feedback),
      });
      const result = await response.json();
      if (!response.ok || result.success === false) {
        throw new Error(result.message || "Failed to submit feedback");
      }
      setSuccess(true);
      toast.success("Feedback submitted successfully");
      return result;
    } catch (err: any) {
      setError(err.message || "Failed to submit feedback");
      toast.error("Failed to submit feedback", { description: err.message });
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { getFeedback, submitFeedback, loading, error, success };
}
