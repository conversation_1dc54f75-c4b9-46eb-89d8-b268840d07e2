import React, { useRef, useState, useEffect, useCallback } from "react";
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Link,
  Image,
  Video,
  Code,
  Quote,
  RemoveFormatting,
} from "lucide-react";

type FormatType =
  | "bold"
  | "italic"
  | "underline"
  | "strike"
  | "ul"
  | "ol"
  | "code"
  | "link"
  | "remove"
  | "video"
  | "image"
  | "blockquote"
  | "align-left"
  | "align-center"
  | "align-right";

interface FormatableTextareaProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  toolbarPosition?: "top" | "bottom";
  showToolbar?: boolean;
  readOnly?: boolean;
}

export const FormatableTextarea: React.FC<FormatableTextareaProps> = ({
  toolbarPosition = "top",
  showToolbar = true,
  className = "",
  value = "",
  onChange,
  placeholder = "Start typing...",
  readOnly = false,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Save and restore cursor position
  const saveCursorPosition = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return null;

    const range = selection.getRangeAt(0);
    const editor = editorRef.current;
    if (!editor) return null;

    // Create a temporary marker to save position
    const marker = document.createElement("span");
    marker.id = "cursor-marker-temp";
    marker.style.display = "none";

    try {
      range.insertNode(marker);
      return marker;
    } catch {
      return null;
    }
  }, []);

  const restoreCursorPosition = useCallback((marker: HTMLElement | null) => {
    if (!marker) return;

    const selection = window.getSelection();
    const range = document.createRange();

    try {
      range.setStartAfter(marker);
      range.collapse(true);
      selection?.removeAllRanges();
      selection?.addRange(range);
      marker.remove();
    } catch {
      // Fallback: place cursor at end
      const editor = editorRef.current;
      if (editor) {
        range.selectNodeContents(editor);
        range.collapse(false);
        selection?.removeAllRanges();
        selection?.addRange(range);
      }
      marker.remove();
    }
  }, []);

  // Convert markdown/HTML-like syntax to rich HTML for display
  const convertToRichHtml = (text: string): string => {
    if (!text.trim()) return "<p><br></p>";

    const html = text
      // Convert bold
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      // Convert italic (but not if it's part of bold)
      .replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, "<em>$1</em>")
      // Convert underline
      .replace(/<u>(.*?)<\/u>/g, "<u>$1</u>")
      // Convert strikethrough
      .replace(/~~(.*?)~~/g, "<del>$1</del>")
      // Convert inline code
      .replace(
        /`([^`]+)`/g,
        '<code style="background-color: #f3f4f6; padding: 2px 4px; border-radius: 3px; font-family: monospace; font-size: 0.9em;">$1</code>'
      )
      // Convert links
      .replace(
        /\[([^\]]+)\]\(([^)]+)\)/g,
        '<a href="$2" style="color: #2563eb; text-decoration: underline;" target="_blank" rel="noopener noreferrer">$1</a>'
      )
      // Convert images
      .replace(
        /!\[([^\]]*)\]\(([^)]+)\)/g,
        '<img src="$2" alt="$1" style="max-width: 100%; height: auto; border-radius: 4px; border: 1px solid #e5e7eb;" />'
      )
      // Convert videos
      .replace(
        /<video controls src="([^"]+)"><\/video>/g,
        '<video controls src="$1" style="max-width: 100%; height: auto; border-radius: 4px; border: 1px solid #e5e7eb;"></video>'
      )
      // Convert block quotes
      .replace(
        /^> (.+)$/gm,
        '<blockquote style="border-left: 4px solid #d1d5db; padding-left: 16px; margin: 8px 0; background-color: #f9fafb; font-style: italic;">$1</blockquote>'
      );

    // Handle lists more carefully
    const lines = html.split("\n");
    const processedLines: string[] = [];
    let inList = false;
    let listType = "";

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      // Handle unordered lists
      if (trimmedLine.match(/^[-*+] /)) {
        if (!inList || listType !== "ul") {
          if (inList) processedLines.push(`</${listType}>`);
          processedLines.push(
            '<ul style="margin: 8px 0; padding-left: 24px; list-style-type: disc;">'
          );
          listType = "ul";
          inList = true;
        }
        processedLines.push(`<li style="margin: 2px 0;">${trimmedLine.substring(2)}</li>`);
      }
      // Handle ordered lists
      else if (trimmedLine.match(/^\d+\. /)) {
        if (!inList || listType !== "ol") {
          if (inList) processedLines.push(`</${listType}>`);
          processedLines.push(
            '<ol style="margin: 8px 0; padding-left: 24px; list-style-type: decimal;">'
          );
          listType = "ol";
          inList = true;
        }
        processedLines.push(
          `<li style="margin: 2px 0;">${trimmedLine.replace(/^\d+\. /, "")}</li>`
        );
      }
      // Handle regular content
      else {
        if (inList) {
          processedLines.push(`</${listType}>`);
          inList = false;
          listType = "";
        }
        if (trimmedLine) {
          processedLines.push(`<p style="margin: 8px 0;">${line}</p>`);
        } else if (index < lines.length - 1) {
          processedLines.push("<br />");
        }
      }
    });

    if (inList) {
      processedLines.push(`</${listType}>`);
    }

    return processedLines.join("");
  };

  // Convert rich HTML back to markdown-like syntax for storage
  const convertToMarkdown = (element: HTMLElement): string => {
    // Clone the element to avoid modifying the original
    const clone = element.cloneNode(true) as HTMLElement;

    // Remove cursor markers if any
    const markers = clone.querySelectorAll("#cursor-marker-temp");
    markers.forEach(marker => marker.remove());

    // Get the text content and convert HTML to markdown
    const markdown = clone.innerHTML
      // Convert back to markdown
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, "**$1**")
      .replace(/<em[^>]*>(.*?)<\/em>/gi, "*$1*")
      .replace(/<u[^>]*>(.*?)<\/u>/gi, "<u>$1</u>")
      .replace(/<del[^>]*>(.*?)<\/del>/gi, "~~$1~~")
      .replace(/<code[^>]*>(.*?)<\/code>/gi, "`$1`")
      .replace(/<a[^>]+href="([^"]*)"[^>]*>(.*?)<\/a>/gi, "[$2]($1)")
      .replace(/<img[^>]+src="([^"]*)"[^>]*alt="([^"]*)"[^>]*\/?>/gi, "![$2]($1)")
      .replace(/<video[^>]+src="([^"]*)"[^>]*><\/video>/gi, '<video controls src="$1"></video>')
      .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, "> $1")
      .replace(/<li[^>]*>(.*?)<\/li>/gi, "- $1\n")
      .replace(/<\/?[uo]l[^>]*>/gi, "")
      .replace(/<p[^>]*>/gi, "")
      .replace(/<\/p>/gi, "\n")
      .replace(/<br\s*\/?>/gi, "\n")
      .replace(/\n+/g, "\n")
      .trim();

    return markdown;
  };

  // Update editor content when value prop changes
  useEffect(() => {
    if (editorRef.current && !isUpdating) {
      const currentMarkdown = convertToMarkdown(editorRef.current);
      if (currentMarkdown !== value) {
        const richHtml = convertToRichHtml(value);
        editorRef.current.innerHTML = richHtml;
      }
    }
  }, [value, isUpdating]);

  // Handle input changes with proper cursor preservation
  const handleInput = useCallback(() => {
    const editor = editorRef.current;
    if (!editor || readOnly || isUpdating) return;

    setIsUpdating(true);

    // Save cursor position
    const marker = saveCursorPosition();

    // Convert back to markdown and trigger onChange
    const markdown = convertToMarkdown(editor);

    if (onChange) {
      onChange(markdown);
    }

    // Restore cursor position after a brief delay
    setTimeout(() => {
      restoreCursorPosition(marker);
      setIsUpdating(false);
    }, 0);
  }, [readOnly, isUpdating, onChange, saveCursorPosition, restoreCursorPosition]);

  // Handle focus
  const handleFocus = useCallback(() => {
    const editor = editorRef.current;
    if (!editor || readOnly) return;

    // Initialize with empty paragraph if completely empty
    if (!editor.innerHTML.trim() || editor.innerHTML === "<p><br></p>") {
      editor.innerHTML = "<p><br></p>";
      const range = document.createRange();
      const selection = window.getSelection();
      if (editor.firstChild?.firstChild) {
        range.setStart(editor.firstChild.firstChild, 0);
        range.collapse(true);
        selection?.removeAllRanges();
        selection?.addRange(range);
      }
    }
  }, [readOnly]);

  // Handle file selection for images
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      const url = e.target?.result as string;
      if (url) {
        insertImage(url, file.name);
      }
    };
    reader.readAsDataURL(file);
  };

  // Handle file selection for videos
  const handleVideoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      const url = e.target?.result as string;
      if (url) {
        insertVideo(url);
      }
    };
    reader.readAsDataURL(file);
  };

  // Insert image into editor
  const insertImage = (src: string, alt: string = "Image") => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.focus();
    const selection = window.getSelection();
    const range = selection?.getRangeAt(0) || document.createRange();

    const img = document.createElement("img");
    img.src = src;
    img.alt = alt;
    img.style.cssText =
      "max-width: 100%; height: auto; border-radius: 4px; border: 1px solid #e5e7eb; margin: 4px 0; display: block;";

    range.deleteContents();
    range.insertNode(img);

    // Move cursor after image
    range.setStartAfter(img);
    range.collapse(true);
    selection?.removeAllRanges();
    selection?.addRange(range);

    handleInput();
  };

  // Insert video into editor
  const insertVideo = (src: string) => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.focus();
    const selection = window.getSelection();
    const range = selection?.getRangeAt(0) || document.createRange();

    const video = document.createElement("video");
    video.src = src;
    video.controls = true;
    video.style.cssText =
      "max-width: 100%; height: auto; border-radius: 4px; border: 1px solid #e5e7eb; margin: 4px 0; display: block;";

    range.deleteContents();
    range.insertNode(video);

    // Move cursor after video
    range.setStartAfter(video);
    range.collapse(true);
    selection?.removeAllRanges();
    selection?.addRange(range);

    handleInput();
  };

  // Modern formatting functions to replace deprecated execCommand
  const wrapSelectedText = (tagName: string, attributes?: Record<string, string>) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString();

    if (!selectedText) return;

    const element = document.createElement(tagName);
    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value);
      });
    }
    element.textContent = selectedText;

    range.deleteContents();
    range.insertNode(element);

    // Move cursor after the inserted element
    range.setStartAfter(element);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);
  };

  // Apply formatting to selected text
  const applyFormat = useCallback(
    (type: FormatType) => {
      const editor = editorRef.current;
      if (!editor || readOnly) return;

      editor.focus();

      // Handle different format types
      if (type === "bold") {
        wrapSelectedText("strong");
      } else if (type === "italic") {
        wrapSelectedText("em");
      } else if (type === "underline") {
        wrapSelectedText("u");
      } else if (type === "strike") {
        wrapSelectedText("del");
      } else if (type === "code") {
        wrapSelectedText("code", {
          style:
            "background-color: #f3f4f6; padding: 2px 4px; border-radius: 3px; font-family: monospace; font-size: 0.9em;",
        });
      } else if (type === "link") {
        const selection = window.getSelection();
        const selectedText = selection?.toString();
        const url = window.prompt("Enter URL:", "https://");
        if (url && selectedText) {
          wrapSelectedText("a", {
            href: url,
            target: "_blank",
            rel: "noopener noreferrer",
            style: "color: #2563eb; text-decoration: underline;",
          });
        }
      } else if (type === "image") {
        fileInputRef.current?.click();
        return;
      } else if (type === "video") {
        videoInputRef.current?.click();
        return;
      } else if (type === "ul") {
        insertList("ul");
      } else if (type === "ol") {
        insertList("ol");
      } else if (type === "blockquote") {
        wrapSelectedText("blockquote", {
          style:
            "border-left: 4px solid #d1d5db; padding-left: 16px; margin: 8px 0; background-color: #f9fafb; font-style: italic;",
        });
      } else if (type === "align-left") {
        applyAlignment("left");
      } else if (type === "align-center") {
        applyAlignment("center");
      } else if (type === "align-right") {
        applyAlignment("right");
      } else if (type === "remove") {
        removeFormatting();
      }

      // Update content after formatting
      setTimeout(() => {
        handleInput();
      }, 10);
    },
    [readOnly, handleInput]
  );

  const insertList = (listType: "ul" | "ol") => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString() || "";

    const list = document.createElement(listType);
    list.style.cssText =
      "margin: 8px 0; padding-left: 24px; list-style-type: " +
      (listType === "ul" ? "disc" : "decimal") +
      ";";

    const listItem = document.createElement("li");
    listItem.style.cssText = "margin: 2px 0;";
    listItem.textContent = selectedText;

    list.appendChild(listItem);

    range.deleteContents();
    range.insertNode(list);

    // Move cursor to end of list item
    range.setStart(listItem, listItem.childNodes.length);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);
  };

  const applyAlignment = (align: "left" | "center" | "right") => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let element = range.commonAncestorContainer;

    // Find the closest block element
    while (element && element.nodeType !== Node.ELEMENT_NODE) {
      element = element.parentNode;
    }

    if (element && element.nodeType === Node.ELEMENT_NODE) {
      (element as HTMLElement).style.textAlign = align;
    }
  };

  const removeFormatting = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString();

    if (selectedText) {
      const textNode = document.createTextNode(selectedText);
      range.deleteContents();
      range.insertNode(textNode);

      // Move cursor after the text
      range.setStartAfter(textNode);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (readOnly) return;

      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case "b":
            e.preventDefault();
            applyFormat("bold");
            break;
          case "i":
            e.preventDefault();
            applyFormat("italic");
            break;
          case "u":
            e.preventDefault();
            applyFormat("underline");
            break;
          case "k":
            e.preventDefault();
            applyFormat("link");
            break;
          case "p":
            e.preventDefault();
            applyFormat("image");
            break;
        }
      }
    },
    [readOnly, applyFormat]
  );

  const ToolbarButton: React.FC<{
    title: string;
    onClick: () => void;
    children: React.ReactNode;
    active?: boolean;
    disabled?: boolean;
    className?: string;
  }> = ({
    title,
    onClick,
    children,
    active = false,
    disabled = false,
    className: btnClassName = "",
  }) => (
    <button
      type="button"
      title={title}
      onClick={onClick}
      disabled={disabled}
      className={`
        flex items-center justify-center w-8 h-8 text-sm font-medium transition-all duration-200 border-0 bg-transparent rounded hover:bg-gray-100 hover:shadow-sm
        ${active ? "bg-blue-50 text-blue-600 shadow-sm ring-1 ring-blue-200" : "text-gray-700"}
        ${disabled ? "opacity-50 cursor-not-allowed hover:bg-transparent" : "cursor-pointer"}
        ${btnClassName}
      `}
      aria-label={title}
    >
      {children}
    </button>
  );

  const ToolbarSeparator = () => <div className="w-px h-6 bg-gray-300 mx-1"></div>;

  const Toolbar = () => (
    <div className="flex gap-1 p-2 bg-gray-50 border-b border-gray-200 items-center flex-wrap">
      {/* Text formatting group */}
      <ToolbarButton title="Bold (Ctrl+B)" onClick={() => applyFormat("bold")} disabled={readOnly}>
        <Bold size={16} />
      </ToolbarButton>

      <ToolbarButton
        title="Italic (Ctrl+I)"
        onClick={() => applyFormat("italic")}
        disabled={readOnly}
      >
        <Italic size={16} />
      </ToolbarButton>

      <ToolbarButton
        title="Underline (Ctrl+U)"
        onClick={() => applyFormat("underline")}
        disabled={readOnly}
      >
        <Underline size={16} />
      </ToolbarButton>

      <ToolbarButton
        title="Strikethrough"
        onClick={() => applyFormat("strike")}
        disabled={readOnly}
      >
        <Strikethrough size={16} />
      </ToolbarButton>

      <ToolbarSeparator />

      {/* Lists group */}
      <ToolbarButton title="Bullet List" onClick={() => applyFormat("ul")} disabled={readOnly}>
        <List size={16} />
      </ToolbarButton>

      <ToolbarButton title="Numbered List" onClick={() => applyFormat("ol")} disabled={readOnly}>
        <ListOrdered size={16} />
      </ToolbarButton>

      <ToolbarSeparator />

      {/* Alignment group */}
      <ToolbarButton
        title="Align Left"
        onClick={() => applyFormat("align-left")}
        disabled={readOnly}
      >
        <AlignLeft size={16} />
      </ToolbarButton>

      <ToolbarButton
        title="Align Center"
        onClick={() => applyFormat("align-center")}
        disabled={readOnly}
      >
        <AlignCenter size={16} />
      </ToolbarButton>

      <ToolbarButton
        title="Align Right"
        onClick={() => applyFormat("align-right")}
        disabled={readOnly}
      >
        <AlignRight size={16} />
      </ToolbarButton>

      <ToolbarSeparator />

      {/* Insert group */}
      <ToolbarButton
        title="Insert Link (Ctrl+K)"
        onClick={() => applyFormat("link")}
        disabled={readOnly}
      >
        <Link size={16} />
      </ToolbarButton>

      <ToolbarButton
        title="Insert Image (Ctrl+P)"
        onClick={() => applyFormat("image")}
        disabled={readOnly}
      >
        <Image size={16} />
      </ToolbarButton>

      <ToolbarButton title="Insert Video" onClick={() => applyFormat("video")} disabled={readOnly}>
        <Video size={16} />
      </ToolbarButton>

      <ToolbarButton title="Code" onClick={() => applyFormat("code")} disabled={readOnly}>
        <Code size={16} />
      </ToolbarButton>

      <ToolbarButton title="Quote" onClick={() => applyFormat("blockquote")} disabled={readOnly}>
        <Quote size={16} />
      </ToolbarButton>

      <ToolbarSeparator />

      {/* Format removal */}
      <ToolbarButton
        title="Remove Formatting"
        onClick={() => applyFormat("remove")}
        disabled={readOnly}
      >
        <RemoveFormatting size={16} />
      </ToolbarButton>

      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageSelect}
        className="hidden"
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        onChange={handleVideoSelect}
        className="hidden"
      />
    </div>
  );

  return (
    <div className={`w-full border border-gray-200 rounded-lg bg-white shadow-sm ${className}`}>
      <style>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
        [contenteditable]:focus:empty:before {
          content: '';
        }
      `}</style>
      {showToolbar && toolbarPosition === "top" && <Toolbar />}
      <div
        ref={editorRef}
        contentEditable={!readOnly}
        className={`
          min-h-[200px] w-full bg-white px-4 py-3 text-sm text-black
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          ${showToolbar && toolbarPosition === "top" ? "rounded-t-none" : ""}
          ${showToolbar && toolbarPosition === "bottom" ? "rounded-b-none" : ""}
          ${readOnly ? "bg-gray-50 cursor-default" : "cursor-text"}
          prose prose-sm max-w-none
        `}
        onInput={handleInput}
        onFocus={handleFocus}
        onKeyDown={handleKeyDown}
        data-placeholder={placeholder}
        style={{
          lineHeight: "1.6",
          fontFamily:
            'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        }}
        suppressContentEditableWarning={true}
      />
      {showToolbar && toolbarPosition === "bottom" && <Toolbar />}
    </div>
  );
};

// Demo component to show the editor in action
export default function WysiwygDemo() {
  const [content, setContent] = useState(
    "**Welcome to the WYSIWYG Editor!**\n\nThis is a *rich text editor* with:\n\n- Bullet points\n- **Bold** and *italic* text\n- Links and images\n\n1. Numbered lists\n2. Code blocks\n3. And much more!\n\nTry selecting text and using the toolbar buttons above!"
  );

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Enhanced WYSIWYG Editor</h1>
        <p className="text-gray-600">
          A rich text editor with proper icons and working bullet/numbering features
        </p>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Interactive Editor</h2>
        <FormatableTextarea
          value={content}
          onChange={setContent}
          placeholder="Describe the job, responsibilities, and requirements..."
          toolbarPosition="top"
          showToolbar={true}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Markdown Output</h2>
        <div className="p-4 bg-gray-100 rounded-lg font-mono text-sm whitespace-pre-wrap border">
          {content || "No content yet..."}
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Text Formatting</h3>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• Bold, Italic, Underline, Strikethrough</li>
              <li>• Text color and highlighting</li>
              <li>• Inline code formatting</li>
            </ul>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-2">Lists & Structure</h3>
            <ul className="text-green-800 text-sm space-y-1">
              <li>• Bullet and numbered lists</li>
              <li>• Indent/outdent functionality</li>
              <li>• Block quotes</li>
            </ul>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold text-purple-900 mb-2">Alignment</h3>
            <ul className="text-purple-800 text-sm space-y-1">
              <li>• Left, center, right alignment</li>
              <li>• Proper text flow</li>
            </ul>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="font-semibold text-orange-900 mb-2">Media & Links</h3>
            <ul className="text-orange-800 text-sm space-y-1">
              <li>• Insert images and videos</li>
              <li>• Create hyperlinks</li>
              <li>• File upload support</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-800">Keyboard Shortcuts</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <p>
            <kbd className="bg-gray-200 px-1 rounded">Ctrl+B</kbd> - Bold
          </p>
          <p>
            <kbd className="bg-gray-200 px-1 rounded">Ctrl+I</kbd> - Italic
          </p>
          <p>
            <kbd className="bg-gray-200 px-1 rounded">Ctrl+U</kbd> - Underline
          </p>
          <p>
            <kbd className="bg-gray-200 px-1 rounded">Ctrl+K</kbd> - Insert Link
          </p>
          <p>
            <kbd className="bg-gray-200 px-1 rounded">Ctrl+P</kbd> - Insert Image
          </p>
        </div>
      </div>
    </div>
  );
}
