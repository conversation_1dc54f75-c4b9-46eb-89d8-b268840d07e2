import React, { useState, useRef, useMemo } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

interface FormatableTextareaProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  toolbarPosition?: "top" | "bottom";
  showToolbar?: boolean;
  readOnly?: boolean;
  minHeight?: number;
  maxHeight?: number;
}

export const FormatableTextarea: React.FC<FormatableTextareaProps> = ({
  toolbarPosition = "top",
  showToolbar = true,
  className = "",
  value = "",
  onChange,
  placeholder = "Start typing...",
  readOnly = false,
  minHeight = 250,
  maxHeight = 500,
}) => {
  const quillRef = useRef<ReactQuill>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Link dialog state
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [linkText, setLinkText] = useState("");
  const [linkUrl, setLinkUrl] = useState("https://");
  const [currentRange, setCurrentRange] = useState<{ index: number; length: number } | null>(null);

  // Custom link handler
  const handleLinkClick = () => {
    const quill = quillRef.current?.getEditor();
    if (!quill) return;

    const range = quill.getSelection();
    if (!range) return;

    const selectedText = quill.getText(range.index, range.length);

    // Store the current range and selected text
    setCurrentRange(range);
    setLinkText(selectedText || "");
    setLinkUrl("https://");
    setLinkDialogOpen(true);
  };

  // Handle link insertion
  const handleLinkInsert = () => {
    const quill = quillRef.current?.getEditor();
    if (!quill || !currentRange) return;

    if (linkText && linkUrl) {
      if (linkText === quill.getText(currentRange.index, currentRange.length)) {
        // If text was selected, just add the link
        quill.format("link", linkUrl);
      } else {
        // If no text selected or different text, insert new text with link
        quill.deleteText(currentRange.index, currentRange.length);
        quill.insertText(currentRange.index, linkText, "link", linkUrl);
      }
    }

    setLinkDialogOpen(false);
    setLinkText("");
    setLinkUrl("https://");
    setCurrentRange(null);
  };

  // Custom toolbar configuration
  const modules = useMemo(
    () => ({
      toolbar: showToolbar
        ? {
            container: [
              [{ header: [1, 2, 3, false] }],
              ["bold", "italic", "underline", "strike"],
              [{ list: "ordered" }, { list: "bullet" }],
              [{ align: [] }],
              ["link", "image", "video"],
              ["blockquote", "code-block"],
              ["clean"],
            ],
            handlers: {
              link: handleLinkClick,
            },
          }
        : false,
      clipboard: {
        matchVisual: false,
      },
    }),
    [showToolbar]
  );

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "align",
    "link",
    "image",
    "video",
    "blockquote",
    "code-block",
  ];

  // Handle file selection for images
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      const url = e.target?.result as string;
      if (url && quillRef.current) {
        const quill = quillRef.current.getEditor();
        const range = quill.getSelection();
        quill.insertEmbed(range?.index || 0, "image", url);
      }
    };
    reader.readAsDataURL(file);
  };

  // Handle file selection for videos
  const handleVideoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      const url = e.target?.result as string;
      if (url && quillRef.current) {
        const quill = quillRef.current.getEditor();
        const range = quill.getSelection();
        quill.insertEmbed(range?.index || 0, "video", url);
      }
    };
    reader.readAsDataURL(file);
  };

  // Custom styles for the editor
  const editorStyle = {
    minHeight: `${minHeight}px`,
    maxHeight: `${maxHeight}px`,
    backgroundColor: readOnly ? "#f9fafb" : "white",
  };

  const containerStyle = {
    border: "1px solid #d1d5db",
    borderRadius: "0.5rem",
    backgroundColor: "white",
    boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    resize: "vertical" as const,
    overflow: "hidden",
  };

  return (
    <div className={`w-full ${className}`} style={containerStyle}>
      <style>{`
        .ql-toolbar {
          border-top: none;
          border-left: none;
          border-right: none;
          border-bottom: 1px solid #d1d5db;
          ${toolbarPosition === "bottom" ? "order: 2;" : ""}
          background-color: #f8fafc;
        }
        .ql-container {
          border: none;
          font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
          ${toolbarPosition === "bottom" ? "order: 1;" : ""}
          max-height: ${maxHeight}px;
          overflow-y: auto;
        }
        .ql-editor {
          min-height: ${minHeight}px;
          max-height: ${maxHeight}px;
          line-height: 1.8;
          padding: 18px 24px;
          font-size: 16px;
          overflow-y: auto;
          resize: vertical;
          scroll-behavior: smooth;
        }
        .ql-editor.ql-blank::before {
          color: #9ca3af;
          font-style: normal;
          font-size: 16px;
        }
        .ql-editor p {
          margin-bottom: 8px;
        }
        .ql-editor h1, .ql-editor h2, .ql-editor h3 {
          margin-top: 16px;
          margin-bottom: 12px;
          font-weight: 600;
        }
        .ql-editor ul, .ql-editor ol {
          margin-bottom: 12px;
        }
        .ql-editor blockquote {
          margin: 16px 0;
          padding-left: 16px;
          border-left: 4px solid #e5e7eb;
          color: #6b7280;
          font-style: italic;
        }
        .ql-editor a {
          color: #2563eb;
          text-decoration: underline;
        }
        .ql-editor a:hover {
          color: #1d4ed8;
        }
        ${readOnly ? ".ql-editor { background-color: #f9fafb; cursor: default; }" : ""}
        ${!showToolbar ? ".ql-toolbar { display: none; }" : ""}
        ${toolbarPosition === "bottom" ? ".ql-snow { display: flex; flex-direction: column; }" : ""}

        /* Custom scrollbar styling */
        .ql-editor::-webkit-scrollbar {
          width: 8px;
        }
        .ql-editor::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 4px;
        }
        .ql-editor::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 4px;
        }
        .ql-editor::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }
      `}</style>

      <ReactQuill
        ref={quillRef}
        theme="snow"
        value={value}
        onChange={onChange}
        readOnly={readOnly}
        placeholder={placeholder}
        modules={modules}
        formats={formats}
        style={editorStyle}
      />

      {/* Hidden file inputs for image and video upload */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageSelect}
        className="hidden"
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        onChange={handleVideoSelect}
        className="hidden"
      />

      {/* Link Dialog */}
      <Dialog open={linkDialogOpen} onOpenChange={setLinkDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Insert Link</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="link-text">Link Text</Label>
              <Input
                id="link-text"
                placeholder="Enter the text to display"
                value={linkText}
                onChange={e => setLinkText(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="link-url">URL</Label>
              <Input
                id="link-url"
                placeholder="https://example.com"
                value={linkUrl}
                onChange={e => setLinkUrl(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setLinkDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleLinkInsert} disabled={!linkText || !linkUrl}>
              Insert Link
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Demo component to show the editor in action
export default function WysiwygDemo() {
  const [content, setContent] = useState(
    '<h2>Enhanced React Quill Editor!</h2><p>This improved editor now features:</p><ul><li><strong>Larger text size (16px)</strong> for better readability</li><li><strong>Improved line spacing (1.8)</strong> for comfortable reading</li><li><strong>Smart height management</strong> - minimum 250px with smooth scrolling</li><li><strong>Professional link dialog</strong> - try selecting text and clicking the link button!</li><li>Custom scrollbar styling for better UX</li></ul><p>Try the enhanced link functionality: <a href="https://example.com" rel="noopener noreferrer" target="_blank">Example Link</a></p><blockquote>The editor maintains consistent height while providing smooth scrolling for longer content. The text is now more readable with better spacing and sizing.</blockquote><p>Add more content to test the scrolling behavior. The editor will maintain its minimum height of 250px and show a custom-styled scrollbar when content overflows.</p><p>Additional features include:</p><ol><li>Resizable editor (drag the bottom-right corner)</li><li>Better typography with improved margins and padding</li><li>Professional dialog for link insertion with separate fields for text and URL</li><li>Smooth scroll behavior for better user experience</li></ol>'
  );

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">React Quill Editor</h1>
        <p className="text-gray-600">A simplified, powerful rich text editor using React Quill</p>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Interactive Editor</h2>
        <FormatableTextarea
          value={content}
          onChange={setContent}
          placeholder="Start typing your content here..."
          toolbarPosition="top"
          showToolbar={true}
          minHeight={250}
          maxHeight={400}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">HTML Output</h2>
        <div className="p-4 bg-gray-100 rounded-lg font-mono text-sm whitespace-pre-wrap border max-h-40 overflow-y-auto">
          {content || "No content yet..."}
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Enhanced Text</h3>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• Larger, more readable text (16px)</li>
              <li>• Better line spacing (1.8)</li>
              <li>• Professional typography & spacing</li>
            </ul>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-2">Smart Layout</h3>
            <ul className="text-green-800 text-sm space-y-1">
              <li>• Adjustable height (min 250px)</li>
              <li>• Scroll on overflow</li>
              <li>• Resizable editor</li>
            </ul>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold text-purple-900 mb-2">Professional Links</h3>
            <ul className="text-purple-800 text-sm space-y-1">
              <li>• Modal dialog for link creation</li>
              <li>• Separate fields for text & URL</li>
              <li>• Smart text selection handling</li>
            </ul>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="font-semibold text-orange-900 mb-2">User Experience</h3>
            <ul className="text-orange-800 text-sm space-y-1">
              <li>• Custom scrollbar styling</li>
              <li>• Better spacing & margins</li>
              <li>• Responsive design</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-800">Latest Improvements</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <p>
            ✅ <strong>Enhanced readability</strong> - Larger text size (16px) with improved line
            spacing (1.8)
          </p>
          <p>
            ✅ <strong>Smart height management</strong> - Minimum 250px height with smooth scrolling
          </p>
          <p>
            ✅ <strong>Professional link dialog</strong> - Modal with separate fields for text and
            URL
          </p>
          <p>
            ✅ <strong>Custom scrollbar styling</strong> - Beautiful, consistent scroll experience
          </p>
          <p>
            ✅ <strong>Resizable editor</strong> - Users can adjust height with smooth behavior
          </p>
          <p>
            ✅ <strong>Better typography</strong> - Improved margins, padding, and visual hierarchy
          </p>
          <p>
            ✅ <strong>Accessibility improvements</strong> - Better focus management and keyboard
            navigation
          </p>
        </div>
      </div>
    </div>
  );
}
