import React, { useState, useRef, useMemo } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

interface FormatableTextareaProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  toolbarPosition?: "top" | "bottom";
  showToolbar?: boolean;
  readOnly?: boolean;
}

export const FormatableTextarea: React.FC<FormatableTextareaProps> = ({
  toolbarPosition = "top",
  showToolbar = true,
  className = "",
  value = "",
  onChange,
  placeholder = "Start typing...",
  readOnly = false,
}) => {
  const quillRef = useRef<ReactQuill>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Custom toolbar configuration
  const modules = useMemo(
    () => ({
      toolbar: showToolbar
        ? {
            container: [
              [{ header: [1, 2, 3, false] }],
              ["bold", "italic", "underline", "strike"],
              [{ list: "ordered" }, { list: "bullet" }],
              [{ align: [] }],
              ["link", "image", "video"],
              ["blockquote", "code-block"],
              ["clean"],
            ],
          }
        : false,
      clipboard: {
        matchVisual: false,
      },
    }),
    [showToolbar]
  );

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "align",
    "link",
    "image",
    "video",
    "blockquote",
    "code-block",
  ];

  // Handle file selection for images
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      const url = e.target?.result as string;
      if (url && quillRef.current) {
        const quill = quillRef.current.getEditor();
        const range = quill.getSelection();
        quill.insertEmbed(range?.index || 0, "image", url);
      }
    };
    reader.readAsDataURL(file);
  };

  // Handle file selection for videos
  const handleVideoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      const url = e.target?.result as string;
      if (url && quillRef.current) {
        const quill = quillRef.current.getEditor();
        const range = quill.getSelection();
        quill.insertEmbed(range?.index || 0, "video", url);
      }
    };
    reader.readAsDataURL(file);
  };

  // Custom styles for the editor
  const editorStyle = {
    minHeight: "200px",
    backgroundColor: readOnly ? "#f9fafb" : "white",
  };

  const containerStyle = {
    border: "1px solid #d1d5db",
    borderRadius: "0.5rem",
    backgroundColor: "white",
    boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
  };

  return (
    <div className={`w-full ${className}`} style={containerStyle}>
      <style>{`
        .ql-toolbar {
          border-top: none;
          border-left: none;
          border-right: none;
          border-bottom: 1px solid #d1d5db;
          ${toolbarPosition === "bottom" ? "order: 2;" : ""}
        }
        .ql-container {
          border: none;
          font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
          ${toolbarPosition === "bottom" ? "order: 1;" : ""}
        }
        .ql-editor {
          min-height: 200px;
          line-height: 1.6;
          padding: 12px 16px;
        }
        .ql-editor.ql-blank::before {
          color: #9ca3af;
          font-style: normal;
        }
        ${readOnly ? ".ql-editor { background-color: #f9fafb; cursor: default; }" : ""}
        ${!showToolbar ? ".ql-toolbar { display: none; }" : ""}
        ${toolbarPosition === "bottom" ? ".ql-snow { display: flex; flex-direction: column; }" : ""}
      `}</style>

      <ReactQuill
        ref={quillRef}
        theme="snow"
        value={value}
        onChange={onChange}
        readOnly={readOnly}
        placeholder={placeholder}
        modules={modules}
        formats={formats}
        style={editorStyle}
      />

      {/* Hidden file inputs for image and video upload */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageSelect}
        className="hidden"
      />
      <input
        ref={videoInputRef}
        type="file"
        accept="video/*"
        onChange={handleVideoSelect}
        className="hidden"
      />
    </div>
  );
};

// Demo component to show the editor in action
export default function WysiwygDemo() {
  const [content, setContent] = useState(
    "<h2>Welcome to the React Quill Editor!</h2><p>This is a <strong>rich text editor</strong> powered by <em>React Quill</em> with:</p><ul><li>Bold, italic, underline formatting</li><li><strong>Lists</strong> and <em>alignment</em> options</li><li>Links and media support</li></ul><ol><li>Easy to use toolbar</li><li>Clean, modern interface</li><li>Simplified implementation</li></ol><blockquote>Much simpler than the previous custom implementation!</blockquote>"
  );

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">React Quill Editor</h1>
        <p className="text-gray-600">A simplified, powerful rich text editor using React Quill</p>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Interactive Editor</h2>
        <FormatableTextarea
          value={content}
          onChange={setContent}
          placeholder="Start typing your content here..."
          toolbarPosition="top"
          showToolbar={true}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">HTML Output</h2>
        <div className="p-4 bg-gray-100 rounded-lg font-mono text-sm whitespace-pre-wrap border max-h-40 overflow-y-auto">
          {content || "No content yet..."}
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Text Formatting</h3>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• Bold, Italic, Underline, Strikethrough</li>
              <li>• Headers (H1, H2, H3)</li>
              <li>• Code blocks</li>
            </ul>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-2">Lists & Structure</h3>
            <ul className="text-green-800 text-sm space-y-1">
              <li>• Bullet and numbered lists</li>
              <li>• Block quotes</li>
              <li>• Text alignment</li>
            </ul>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold text-purple-900 mb-2">Media & Links</h3>
            <ul className="text-purple-800 text-sm space-y-1">
              <li>• Insert links</li>
              <li>• Image and video support</li>
              <li>• File upload capability</li>
            </ul>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="font-semibold text-orange-900 mb-2">Simplified</h3>
            <ul className="text-orange-800 text-sm space-y-1">
              <li>• Built on React Quill</li>
              <li>• Much cleaner codebase</li>
              <li>• Better performance</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-800">Benefits</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <p>✅ Simplified implementation using React Quill</p>
          <p>✅ Better performance and reliability</p>
          <p>✅ Maintained same interface for compatibility</p>
          <p>✅ Built-in keyboard shortcuts and accessibility</p>
          <p>✅ Professional-grade editor with extensive features</p>
        </div>
      </div>
    </div>
  );
}
