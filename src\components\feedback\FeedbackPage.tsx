import { useState } from "react";
import { useFeedback } from "@/hooks/useFeedback";
import { useNavigate } from "react-router-dom";
import { toast } from "@/components/ui/sonner";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import FeedbackHeader from "./FeedbackHeader";
import FeedbackForm from "./FeedbackForm";
import { Label } from "@/components/ui/label";

interface Interview {
  id: string;
  candidateId: string;
  candidateName: string;
  position: string;
  date: string;
  interviewerId: string;
  type: string;
}

interface FeedbackPageProps {
  interview: Interview;
  onClose: () => void;
  isFullPage?: boolean;
}

const FeedbackPage = ({ interview, onClose, isFullPage = false }: FeedbackPageProps) => {
  const navigate = useNavigate();
  // Stage options
  const stageOptions = [
    { value: "initial", label: "Initial Interview" },
    { value: "technical", label: "Technical Assessment" },
    { value: "cultural", label: "Cultural Fit" },
    { value: "final", label: "Final Interview" },
  ];

  const [selectedStage, setSelectedStage] = useState(stageOptions[0].value);

  // Define evaluation criteria
  const [criteria, setCriteria] = useState([
    {
      id: "technical_skills",
      title: "Technical Skills",
      description:
        "Evaluate the candidate's technical knowledge and skills relevant to the position.",
      score: 0,
      notes: "",
    },
    {
      id: "interpersonal_skills",
      title: "Interpersonal Skills",
      description:
        "How good are they at striking up a conversation? Have they put you at your ease?",
      score: 0,
      notes: "",
    },
    {
      id: "problem_solving",
      title: "Problem Solving",
      description:
        "Assess the candidate's ability to analyze and solve problems related to the job.",
      score: 0,
      notes: "",
    },
    {
      id: "culture_fit",
      title: "Cultural Fit",
      description:
        "Evaluate how well the candidate would fit into the company culture and team dynamics.",
      score: 0,
      notes: "",
    },
  ]);

  const [overallRecommendation, setOverallRecommendation] = useState("");
  const [overallComments, setOverallComments] = useState("");
  const [showScorecard, setShowScorecard] = useState(false);

  // No extra form fields for stage, date, type, duration

  const handleCriteriaChange = (id: string, field: string, value: any) => {
    setCriteria(prev => prev.map(item => (item.id === id ? { ...item, [field]: value } : item)));
  };

  const { submitFeedback } = useFeedback();

  const handleSubmit = async () => {
    // Validate the form
    if (!overallRecommendation) {
      toast.error("Please provide an overall recommendation");
      return;
    }

    if (!selectedStage) {
      toast.error("Please select a stage");
      return;
    }

    if (criteria.some(criterion => criterion.score === 0)) {
      toast.error("Please rate all evaluation criteria");
      return;
    }

    const overall_score =
      criteria.reduce((acc, criterion) => acc + criterion.score, 0) / criteria.length;

    const finalFeedback = {
      interview_id: interview.id,
      candidate_id: interview.candidateId,
      reviewer_id: interview.interviewerId,
      stage: selectedStage,
      overall_recommendation: overallRecommendation,
      overall_comments: overallComments,
      criteria,
      overall_score,
    };

    try {
      const result = await submitFeedback(interview.id, finalFeedback);
      if (result && result.success !== false) {
        toast.success("Feedback submitted successfully");
        onClose();
        if (isFullPage) {
          navigate("/interviews");
        }
      }
    } catch (err: any) {
      toast.error("Failed to submit feedback", { description: err?.message });
    }
  };

  return (
    <>
      <FeedbackHeader
        candidateName={interview.candidateName}
        position={interview.position}
        interviewDate={interview.date}
        interviewType={interview.type}
        onClose={onClose}
        onSubmit={handleSubmit}
      />

      {/* Stage select field */}
      <div className="mb-6 max-w-md">
        <Label htmlFor="stage" className="mb-2 block">
          Select a stage <span className="text-red-500">*</span>
        </Label>
        <Select value={selectedStage} onValueChange={setSelectedStage}>
          <SelectTrigger id="stage" className="w-full">
            <SelectValue placeholder="Select a stage" />
          </SelectTrigger>
          <SelectContent>
            {stageOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {!showScorecard ? (
        <>
          <div className="bg-blue-50 p-4 rounded-md mb-6">
            <p className="text-sm text-blue-700">
              A scorecard has been created to help you structure your interview. Use this to submit
              your feedback after evaluation. Feedback is used internally and will not be shared
              with the candidates.
            </p>
          </div>

          <FeedbackForm
            criteria={criteria}
            onCriteriaChange={handleCriteriaChange}
            overallRecommendation={overallRecommendation}
            onRecommendationChange={setOverallRecommendation}
            overallComments={overallComments}
            onOverallCommentsChange={setOverallComments}
          />
        </>
      ) : (
        <div className="p-6 bg-white rounded-md shadow-sm border">
          <h3 className="text-lg font-medium mb-4">Candidate Scorecard</h3>
          <p className="text-muted-foreground mb-4">Review all scores and feedback in one place.</p>

          <div className="space-y-4">
            <div>
              <h4 className="font-medium">Overall Recommendation</h4>
              <p>
                {overallRecommendation
                  ? overallRecommendation.replace("_", " ").toUpperCase()
                  : "Not provided"}
              </p>
            </div>

            {criteria.map(item => (
              <div key={item.id} className="border-t pt-4">
                <h4 className="font-medium">{item.title}</h4>
                <p className="text-sm text-muted-foreground">Score: {item.score}/5</p>
                <p className="text-sm mt-1">{item.notes || "No notes provided"}</p>
              </div>
            ))}

            <div className="border-t pt-4">
              <h4 className="font-medium">Overall Comments</h4>
              <p>{overallComments || "No overall comments provided"}</p>
            </div>
          </div>
        </div>
      )}

      <div className="mt-8 flex justify-end">
        {!showScorecard ? (
          <Button onClick={() => setShowScorecard(true)}>Show Scorecard</Button>
        ) : (
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowScorecard(false)}>
              Back to Evaluation
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!overallRecommendation || criteria.some(criterion => criterion.score === 0)}
            >
              Submit Feedback
            </Button>
          </div>
        )}
      </div>
    </>
  );
};

export default FeedbackPage;
