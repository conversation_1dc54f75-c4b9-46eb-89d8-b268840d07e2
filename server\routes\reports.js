import express from 'express';
import { asyncHandler } from '../middleware/asyncHandler.js';
import { reportController } from '../controllers/reportController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/reports/time-to-hire
 * @desc    Get time-to-hire metrics report
 * @access  Private/Admin
 */
router.get('/time-to-hire',
  authenticate,
  authorize(['admin', 'recruiter']),
  asyncHandler(reportController.getTimeToHireReport)
);

/**
 * @route   GET /api/reports/panelist-feedback
 * @desc    Get panelist feedback analysis report
 * @access  Private/Admin
 */
router.get('/panelist-feedback',
  authenticate,
  authorize(['admin', 'recruiter']),
  async<PERSON><PERSON><PERSON>(reportController.getPanelistFeedbackReport)
);

/**
 * @route   GET /api/dashboard/recruiter
 * @desc    Get recruiter dashboard metrics
 * @access  Private/Recruiter
 */
router.get('/dashboard/recruiter',
  authenticate,
  authorize(['admin', 'recruiter']),
  async<PERSON><PERSON><PERSON>(reportController.getRecruiterDashboard)
);

export default router;