import { useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { FormModal } from '@/components/common/FormModal';
import { useJobs } from '@/hooks/useJobs';
import { BULK_UPLOAD_CANDIDATES_CONSTANTS } from '@/utils/Constant';
import { toast } from '@/components/ui/sonner';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { GoogleDrivePicker } from '@/components/GoogleDrivePicker';

const { VALIDATION, LABELS, PLACEHOLDERS, MODAL, TOAST } = BULK_UPLOAD_CANDIDATES_CONSTANTS;

type BulkUploadFormValues = {
  jobId: string;
  folder: FileList | null;
};

export function BulkUploadCandidatesModal({ onBulkUpload }: { onBulkUpload?: () => void }) {
  const { jobs, loading: jobsLoading } = useJobs();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [googleDriveFiles, setGoogleDriveFiles] = useState<any[]>([]);

  const form = useForm<BulkUploadFormValues>({
    defaultValues: {
      jobId: '',
      folder: null,
    },
  });

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
    watch,
  } = form;

  const folderFiles = watch('folder');

  const onSubmit = async (data: BulkUploadFormValues) => {
    if (!data.jobId) {
      form.setError('jobId', { message: VALIDATION.JOB_REQUIRED });
      return;
    }
    if (!data.folder || data.folder.length === 0) {
      form.setError('folder', { message: VALIDATION.FOLDER_REQUIRED });
      return;
    }
    setIsSubmitting(true);
    setTimeout(() => {
      setIsSubmitting(false);
      toast.success(TOAST.SUCCESS.TITLE, {
        description: TOAST.SUCCESS.DESCRIPTION(data.folder ? data.folder.length : 0),
      });
      reset();
      if (onBulkUpload) onBulkUpload();
    }, 1200);
  };

  return (
    <FormModal
      triggerText={MODAL.DEFAULT_BUTTON_TEXT}
      title={MODAL.TITLE}
      description={MODAL.DESCRIPTION}
      onSubmit={handleSubmit(onSubmit)}
      submitText={MODAL.SUBMIT_TEXT}
      isSubmitting={isSubmitting || jobsLoading}
    >
      <Form {...form}>
        <div className="space-y-6">
          <FormField
            control={control}
            name="jobId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{LABELS.JOB_MAPPING}</FormLabel>
                <Controller
                  control={control}
                  name="jobId"
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={jobsLoading || jobs.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={jobsLoading ? PLACEHOLDERS.LOADING_JOBS : (jobs.length === 0 ? PLACEHOLDERS.NO_JOBS : PLACEHOLDERS.SELECT_JOB)} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {jobs.map((job) => (
                          <SelectItem key={job.id} value={job.id}>
                            {job.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="folder"
            render={({ field: { value, onChange, ...fieldProps } }) => (
              <FormItem>
                <FormLabel>{LABELS.FOLDER_SELECT}</FormLabel>
                <FormControl>
                  <Input
                    type="file"
                    multiple
                    webkitdirectory=""
                    directory=""
                    onChange={(e) => {
                      onChange(e.target.files);
                    }}
                    className="cursor-pointer"
                    {...fieldProps}
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground mt-1">{PLACEHOLDERS.SELECT_FOLDER}</div>
                <FormMessage />
                {value && value.length > 0 && (
                  <div className="text-xs mt-2">{value.length} files selected</div>
                )}
              </FormItem>
            )}
          />

          {/* Google Drive Picker Integration */}
          <div className="space-y-2">
            <div className="font-medium">Or select files/folders from Google Drive:</div>
            <GoogleDrivePicker
              onPicked={(files) => {
                setGoogleDriveFiles(files);
                toast.success('Google Drive selection', {
                  description: `${files.length} item(s) selected from Drive.`,
                });
              }}
            />
            {googleDriveFiles.length > 0 && (
              <div className="mt-2 bg-muted rounded p-2 text-xs">
                <div className="font-semibold mb-1">Selected from Drive:</div>
                <ul className="list-disc ml-4">
                  {googleDriveFiles.map((file, idx) => (
                    <li key={file.id || idx}>{file.name || file.title}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </Form>
    </FormModal>
  );
} 