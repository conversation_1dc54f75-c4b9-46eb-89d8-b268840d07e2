import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { responseHandler } from '../utils/responseHandler.js';
import { supabase } from '../config/db.js';
import { authService } from '../services/authService.js';

export const authController = {
  async login(req, res) {
    try {
      const { email, password } = req.body;
      const result = await authService.login(email, password);

      if (!result.success) {
        return responseHandler.error(res, result.message);
      }

      // Set session expiry in response
      return responseHandler.success(res, {
        user: result.user,
        token: result.token,
        expiresIn: result.expiresIn
      });
    } catch (error) {
      return responseHandler.error(res, error.message);
    }
  },

  async register(req, res) {
    try {
      const result = await authService.register(req.body);

      if (!result.success) {
        return responseHandler.error(res, result.message);
      }

      // Set session expiry in response
      return responseHandler.success(res, {
        user: result.user,
        token: result.token,
        expiresIn: result.expiresIn
      });
    } catch (error) {
      return responseHandler.error(res, error.message);
    }
  },

  async profile(req, res) {
    try {
      const { id } = req.user;
      const { success, user, message } = await userService.getUserById(id);

      if (!success) {
        return responseHandler.notFound(res, message);
      }

      return responseHandler.success(res, { user });
    } catch (error) {
      logger.error('Get profile error:', error);
      return responseHandler.error(res, error);
    }
  }
};
