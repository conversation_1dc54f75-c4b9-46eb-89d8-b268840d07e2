import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import J<PERSON><PERSON><PERSON> from "jszip";
import { saveAs } from "file-saver";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Utility function to format work location display
export function formatWorkLocation(location: string): string {
  if (!location) return '';
  
  // Handle specific cases
  switch (location.toLowerCase()) {
    case 'on-site':
      return 'On-Site';
    case 'remote':
      return 'Remote';
    case 'hybrid':
      return 'Hybrid';
    default:
      // For other cases, capitalize first letter of each word
      return location
        .split(/[\s\-_]+/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join('-');
  }
}

type StatusColorMap = {
  [key: string]: string;
};

const jobStatusColors: StatusColorMap = {
  active: "bg-green-100 text-green-800 hover:bg-green-200",
  draft: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
  closed: "bg-gray-100 text-gray-800 hover:bg-gray-200",
  published: "bg-blue-100 text-blue-800 hover:bg-blue-200",
  "on hold": "bg-orange-100 text-orange-800 hover:bg-orange-200",
  private: "bg-purple-100 text-purple-800 hover:bg-purple-200",
} as const;

const candidateStatusColors: StatusColorMap = {
  applied: "bg-blue-500",
  screening: "bg-purple-500",
  interview: "bg-amber-500",
  offer: "bg-green-500",
  hired: "bg-green-700",
  rejected: "bg-red-500",
} as const;

export const getStatusColorClass = (status?: string) => {
  if (!status) return jobStatusColors.published;
  return jobStatusColors[status.toLowerCase()] || jobStatusColors.published;
};

export const getStatusColor = (status: string) => {
  return candidateStatusColors[status.toLowerCase()] || "bg-gray-500";
};

export const getCandidateCountColor = (applicants?: number) => {
  if (!applicants || applicants === 0) return "border-gray-300 text-gray-500";
  if (applicants < 50) return "border-red-400 text-red-600";
  if (applicants < 100) return "border-blue-500 text-blue-600";
  return "border-green-500 text-green-600";
};

export  const downloadAllResumes = async (candidates: Candidate[]) => {
    const zip = new JSZip();

    for (const candidate of candidates) {
      const url = candidate.resume_url;
      const fileName = candidate.name.replace(/\s+/g, "_") + ".pdf"; // sanitize name

      try {
        // Fetch file as blob
        const response = await fetch(url);
        const blob = await response.blob();

        // Add to zip
        zip.file(fileName, blob);
      } catch (error) {
        console.error(`Failed to fetch ${fileName}:`, error);
      }
    }

    // Generate and trigger download
    zip.generateAsync({ type: "blob" }).then(content => {
      saveAs(content, "candidates_resumes.zip");
    });
  };