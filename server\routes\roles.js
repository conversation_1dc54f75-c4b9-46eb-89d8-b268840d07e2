import express from 'express';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { roleController } from '../controllers/RoleController.js';

const router = express.Router();

/**
 * @route   GET /api/roles
 * @desc    Get all roles
 * @access  Public
 */
router.get('/', async<PERSON>and<PERSON>(roleController.getRoles));

/**
 * @route   POST /api/roles
 * @desc    Create a new role
 * @access  Public
 */
router.post('/', asyncHandler(roleController.createRole));

export default router;