import { google } from 'googleapis';
import { logger } from './logger.js';

const calendar = google.calendar({
  version: 'v3',
  auth: new google.auth.JWT(
    process.env.GOOGLE_CLIENT_EMAIL,
    null,
    process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    ['https://www.googleapis.com/auth/calendar']
  )
});

export const calendarUtils = {
  async createGoogleEvent({ summary, description, startTime, endTime, attendees, location }) {
    try {
      const event = {
        summary,
        description,
        start: {
          dateTime: startTime,
          timeZone: 'UTC'
        },
        end: {
          dateTime: endTime,
          timeZone: 'UTC'
        },
        attendees: attendees.map(email => ({ email })),
        location,
        conferenceData: {
          createRequest: {
            requestId: `${Date.now()}`
          }
        }
      };

      const { data } = await calendar.events.insert({
        calendarId: 'primary',
        resource: event,
        conferenceDataVersion: 1,
        sendUpdates: 'all'
      });

      logger.info('Calendar event created:', data.id);
      return {
        success: true,
        eventId: data.id,
        meetLink: data.hangoutLink
      };
    } catch (error) {
      logger.error('Calendar event creation failed:', error);
      throw new Error('Failed to create calendar event');
    }
  },

  async cancelEvent(eventId) {
    try {
      await calendar.events.delete({
        calendarId: 'primary',
        eventId,
        sendUpdates: 'all'
      });

      logger.info('Calendar event cancelled:', eventId);
      return { success: true };
    } catch (error) {
      logger.error('Calendar event cancellation failed:', error);
      throw new Error('Failed to cancel calendar event');
    }
  },

  async syncAvailability(userId, startDate, endDate) {
    try {
      const { data } = await calendar.freebusy.query({
        resource: {
          timeMin: startDate,
          timeMax: endDate,
          items: [{ id: 'primary' }]
        }
      });

      const busySlots = data.calendars.primary.busy;
      logger.info('Calendar availability synced for user:', userId);

      return {
        success: true,
        busySlots
      };
    } catch (error) {
      logger.error('Calendar sync failed:', error);
      throw new Error('Failed to sync calendar availability');
    }
  }
};