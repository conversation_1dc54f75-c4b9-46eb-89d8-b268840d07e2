import { googleService } from '../services/googleService.js';
import { messages } from '../utils/messages.utils.js';
import { google } from 'googleapis';

export const googleController = {
  auth: (req, res) => {
    const recruiterId = req.query.recruiter_id;
    const service = req.query.service || 'calendar';
    if (!recruiterId) return res.status(400).json({ error: messages.GOOGLE_OAUTH_MISSING_RECRUITER_ID });
    const url = googleService.getAuthUrl(recruiterId, service);
    res.redirect(url);
  },

  callback: async (req, res) => {
    const { code, state } = req.query;
    if (!code || !state) return res.status(400).json({ error: messages.GOOGLE_OAUTH_MISSING_CODE_OR_STATE });
    try {
      const stateObj = JSON.parse(state);
      const service = stateObj.service || 'calendar';
      await googleService.exchangeCodeForTokens(code, stateObj.recruiterId);
      // Redirect to minimal close page for popup
      return res.redirect(`http://localhost:5173/google-oauth-close.html?status=success&type=${service}`);
    } catch (err) {
      // Log error to backend console
      console.error('Google OAuth error:', err);
      // Redirect to minimal close page for popup with error
      const errorMsg = encodeURIComponent(err.message || messages.GOOGLE_OAUTH_CALLBACK_FAILED);
      return res.redirect(`http://localhost:5173/google-oauth-close.html?status=error&msg=${errorMsg}`);
    }
  },

  getGmailProfile: async (req, res) => {
    try {
      const recruiterId = req.query.recruiter_id;
      if (!recruiterId) return res.status(400).json({ error: messages.GOOGLE_OAUTH_MISSING_RECRUITER_ID });

      // Get tokens from DB and refresh if needed
      const accessToken = await googleService.getValidAccessToken(recruiterId);

      const oauth2Client = new google.auth.OAuth2();
      oauth2Client.setCredentials({ access_token: accessToken });

      const gmail = google.gmail({ version: 'v1', auth: oauth2Client });
      const profile = await gmail.users.getProfile({ userId: 'me' });

      res.json(profile.data);
    } catch (err) {
      res.status(500).json({ error: err.message || messages.GOOGLE_GMAIL_PROFILE_FAILED });
    }
  },

  getCalendarEvents: async (req, res) => {
    try {
      const recruiterId = req.query.recruiter_id;
      if (!recruiterId) return res.status(400).json({ error: messages.GOOGLE_OAUTH_MISSING_RECRUITER_ID });

      // Get a valid access token (refresh if needed)
      const accessToken = await googleService.getValidAccessToken(recruiterId);

      const oauth2Client = new google.auth.OAuth2();
      oauth2Client.setCredentials({ access_token: accessToken });

      const calendar = google.calendar({ version: 'v3', auth: oauth2Client });
      const now = new Date().toISOString();

      const eventsRes = await calendar.events.list({
        calendarId: 'primary',
        timeMin: now,
        maxResults: 10,
        singleEvents: true,
        orderBy: 'startTime',
      });

      res.json(eventsRes.data.items);
    } catch (err) {
      res.status(500).json({ error: err.message || messages.GOOGLE_CALENDAR_EVENTS_FAILED });
    }
  }
}; 