import express from 'express';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { feedbackController } from '../controllers/feedbackController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route   GET /api/feedback/:interviewId
 * @desc    Get feedback for a specific interview
 * @access  Private
 */
router.get('/:interviewId',
  authenticate,
  async<PERSON>andler(feedbackController.getFeedback)
);

/**
 * @route   POST /api/feedback/:interviewId
 * @desc    Submit feedback for an interview
 * @access  Private/Interviewer
 */
router.post('/:interviewId',
  authenticate,
  authorize(['admin', 'recruiter', 'interviewer']),
  asyncHandler(feedbackController.submitFeedback)
);

export default router;