
// Relations between data models to mimic foreign key relationships

import { Job, getJob<PERSON>yId } from './jobs';
import { 
  candidates,
  Candidate,
  getCandidateById, 
  getCandidatesByJobId 
} from './candidates';

export interface Interview {
  id: string;
  candidateId: string;
  jobId: number;
  interviewers: string[];
  type: 'phone' | 'video' | 'in-person';
  stage: string;
  date: string;
  startTime: string;
  endTime: string;
  location?: string;
  videoLink?: string;
  notes?: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  feedback?: string;
  selfScheduled?: boolean;
  instructions?: string;
}

// Mock interviews data
export const interviews: Interview[] = [
  {
    id: '1',
    candidateId: '1',
    jobId: 1,
    interviewers: ['interviewer1', 'interviewer2'],
    type: 'video',
    stage: 'Technical Round',
    date: '2023-05-20',
    startTime: '10:00',
    endTime: '11:00',
    videoLink: 'https://meet.google.com/abc-defg-hij',
    notes: 'Focus on frontend skills and React knowledge',
    status: 'scheduled',
    instructions: 'Please prepare to discuss your recent projects and be ready to share your screen for a coding challenge.'
  },
  {
    id: '2',
    candidateId: '5',
    jobId: 5,
    interviewers: ['interviewer3'],
    type: 'phone',
    stage: 'Initial Screening',
    date: '2023-05-15',
    startTime: '14:00',
    endTime: '14:30',
    notes: 'Basic technical assessment and cultural fit evaluation',
    status: 'completed',
    feedback: 'Strong technical skills, good cultural fit. Recommended for the next round.',
    instructions: 'Be ready to discuss your experience with Python and Django.'
  },
  {
    id: '3',
    candidateId: '10',
    jobId: 2,
    interviewers: ['interviewer4', 'interviewer5'],
    type: 'in-person',
    stage: 'Design Challenge',
    date: '2023-05-25',
    startTime: '11:00',
    endTime: '12:30',
    location: 'Main Office, Conference Room 3',
    notes: 'Portfolio review and design challenge discussion',
    status: 'scheduled',
    instructions: 'Please bring your portfolio and be prepared to discuss a design challenge we\'ll present during the interview.'
  },
  {
    id: '4',
    candidateId: '3',
    jobId: 3,
    interviewers: ['interviewer2'],
    type: 'video',
    stage: 'Technical Assessment',
    date: '2023-05-18',
    startTime: '15:00',
    endTime: '16:00',
    videoLink: 'https://zoom.us/j/123456789',
    status: 'scheduled',
    instructions: 'We\'ll be discussing your experience with AWS and Kubernetes. Please prepare to share specific examples.'
  }
];

// Helper functions to get related data

// Get the job for a specific candidate
export const getJobForCandidate = (candidateId: string): Job | undefined => {
  const candidate = getCandidateById(candidateId);
  if (candidate && candidate.jobId) {
    return getJobById(Number(candidate.jobId));
  }
  return undefined;
};

// Get interviews for a specific candidate
export const getInterviewsForCandidate = (candidateId: string): Interview[] => {
  return interviews.filter(interview => interview.candidateId === candidateId);
};

// Get interviews for a specific job
export const getInterviewsForJob = (jobId: number): Interview[] => {
  return interviews.filter(interview => interview.jobId === jobId);
};

// Get candidates for a specific job with applicant count
export const getCandidatesWithJobDetails = () => {
  return jobs.map(job => {
    const jobCandidates = getCandidatesByJobId(job.id.toString());
    return {
      ...job,
      applicants: jobCandidates.length,
      candidates: jobCandidates
    };
  });
};

// Get upcoming interviews
export const getUpcomingInterviews = (): Interview[] => {
  const today = new Date();
  return interviews.filter(interview => {
    const interviewDate = new Date(interview.date);
    return interviewDate >= today && interview.status === 'scheduled';
  }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
};

// Helper function to get interview by ID
export const getInterviewById = (id: string): Interview | undefined => {
  return interviews.find(interview => interview.id === id);
};

// Import jobs for getCandidatesWithJobDetails
import { jobs } from './jobs';

// Helper to get full interview details including candidate and job info
export const getFullInterviewDetails = (interviewId: string) => {
  const interview = getInterviewById(interviewId);
  if (!interview) return null;
  
  const candidate = getCandidateById(interview.candidateId);
  const job = getJobById(interview.jobId);
  
  return {
    interview,
    candidate,
    job
  };
};
