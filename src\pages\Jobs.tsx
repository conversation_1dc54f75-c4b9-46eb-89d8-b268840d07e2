import { useState, useMemo, useEffect } from "react";
import { Search, Filter, Briefcase, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { JobCard } from "@/components/jobs/JobCard";
import { useAuth } from "@/components/auth/AuthProvider";
import { JobDetailsModal } from "@/components/jobs/JobDetailsModal";
import { Link } from "react-router-dom";
import { useJobs, type Job } from "@/hooks/useJobs";
import { useCandidates } from "@/hooks/useCandidates";

const Jobs = () => {
  // Auth state
  const { user } = useAuth();
  const isAllowed = user?.role.name === "recruiter" || user?.role.name === "hr";

  // Jobs hook
  const { jobs, loading, error, pagination, fetchJobs } = useJobs();
  const { getCandidatesByJobId } = useCandidates();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [candidateCounts, setCandidateCounts] = useState<{ [jobId: string]: number }>({});
  const [countsLoading, setCountsLoading] = useState(false);

  // Filter jobs based on search term and status
  const filteredJobs = useMemo(() => {
    return jobs.filter(job => {
      const matchesSearch =
        job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.department.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === "all" || job.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [jobs, searchTerm, statusFilter]);

  // Fetch candidate counts for all jobs
  useEffect(() => {
    const fetchCounts = async () => {
      if (!jobs || jobs.length === 0) return;
      setCountsLoading(true);
      const counts: { [jobId: string]: number } = {};
      await Promise.all(jobs.map(async (job) => {
        try {
          const res = await getCandidatesByJobId(job.id, 1, 1);
          counts[job.id] = res?.pagination?.total || 0;
        } catch {
          counts[job.id] = 0;
        }
      }));
      setCandidateCounts(counts);
      setCountsLoading(false);
    };
    fetchCounts();
  }, [jobs]);

  // Event handlers
  const handleViewJob = (job: Job) => {
    setSelectedJob(job);
    setIsModalOpen(true);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  return (
    <div className="space-y-6">
      {/* Header section */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Job Listings</h1>
          <p className="text-muted-foreground">Manage job postings and view applications</p>
        </div>
        <div className="flex flex-wrap gap-2">
          {isAllowed && (
            <Button asChild>
              <Link to="/jobs/new">
                <Briefcase className="h-4 w-4 mr-2" />
                Post New Job
              </Link>
            </Button>
          )}
        </div>
      </div>

      {/* Search and filter section */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative w-full sm:flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search jobs..."
            className="pl-8 w-full"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>

        <div className="flex gap-2 items-center w-full sm:w-auto">
          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Jobs</SelectItem>
              <SelectItem value="draft">Drafts</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
              <SelectItem value="open">Open</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm" className="gap-1">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      {/* Error state */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <p className="text-destructive text-sm">{error}</p>
        </div>
      )}

      {/* Loading state */}
      {loading && jobs.length === 0 && (
        <div className="flex items-center justify-center py-20">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading jobs...</span>
          </div>
        </div>
      )}

      {/* Job cards grid */}
      {!loading && jobs.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-6 p-4">
          {filteredJobs.map(job => (
            <div key={job.id} className="flex justify-center p-1">
              <JobCard 
                job={job} 
                onView={handleViewJob} 
                candidateCount={candidateCounts[job.id] ?? 0}
              />
            </div>
          ))}
        </div>
      )}

      {/* Empty state */}
      {!loading && filteredJobs.length === 0 && jobs.length > 0 && (
        <div className="text-center py-10">
          <p className="text-muted-foreground">No jobs found matching your search</p>
        </div>
      )}

      {/* No jobs state */}
      {!loading && jobs.length === 0 && !error && (
        <div className="text-center py-20">
          <Briefcase className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No jobs yet</h3>
          <p className="text-muted-foreground mb-4">
            Get started by creating your first job posting
          </p>
          {isAllowed && (
            <Button asChild>
              <Link to="/jobs/new">
                <Briefcase className="h-4 w-4 mr-2" />
                Create First Job
              </Link>
            </Button>
          )}
        </div>
      )}

      {/* Pagination info */}
      {pagination && pagination.total > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          Showing {filteredJobs.length} of {pagination.total} jobs
          {pagination.pages > 1 && ` (Page ${pagination.page} of ${pagination.pages})`}
        </div>
      )}

      {/* Job details modal */}
      <JobDetailsModal job={selectedJob} open={isModalOpen} onOpenChange={setIsModalOpen} />
    </div>
  );
};

export default Jobs;
