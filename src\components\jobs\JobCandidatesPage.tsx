import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Users,
  Calendar,
  MapPin,
  Briefcase,
  FileText,
  Mail,
  MoreHorizontal,
  Globe,
  Building,
  Lock,
  PauseCircle,
  CheckCircle,
  Trash2,
  Check,
  DollarSign,
  Loader2,
  Pencil,
  PlusCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/sonner";
import { Job, useJobs } from "@/hooks/useJobs";
import { Candidate, useCandidates } from "@/hooks/useCandidates";
import { getStatusColorClass, formatWorkLocation, downloadAllResumes } from "@/lib/utils";
import { cn } from "@/lib/utils";
import {
  CandidateDrawer,
  CandidateDetail,
} from "@/components/candidates/CandidateDrawer/CandidateDrawer";
import { JobDescription } from "@/components/jobs/JobDescription";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import JobFormSheet from "./JobFormSheet";

// Status options with icons matching the reference image - updated to match API status values
const statusOptions = [
  {
    value: "published",
    label: "Published",
    description: "Visible on your career and employee portal",
    icon: <Globe className="h-4 w-4 text-blue-600" />,
  },
  {
    value: "draft",
    label: "Draft",
    description: "Visible only to your hiring team and admins",
    icon: <Lock className="h-4 w-4 text-slate-600" />,
  },
  {
    value: "open",
    label: "Open",
    description: "Active job posting accepting applications",
    icon: <CheckCircle className="h-4 w-4 text-green-600" />,
  },
  {
    value: "closed",
    label: "Closed",
    description: "The job posting has been filled",
    icon: <PauseCircle className="h-4 w-4 text-amber-600" />,
  },
];

// Helper function to get status badge
const getStatusBadge = (status: string) => {
  switch (status) {
    case "applied":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700">
          Applied
        </Badge>
      );
    case "screening":
      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700">
          Screening
        </Badge>
      );
    case "interview":
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700">
          Interview
        </Badge>
      );
    case "offer":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700">
          Offer
        </Badge>
      );
    case "hired":
      return (
        <Badge variant="outline" className="bg-emerald-50 text-emerald-700">
          Hired
        </Badge>
      );
    case "rejected":
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700">
          Rejected
        </Badge>
      );
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

export function JobCandidatesPage() {
  const { jobId } = useParams();
  const { getJobById, updateJob, loading: updateLoading } = useJobs();
  const { getCandidatesByJobId, loading: candidatesLoading } = useCandidates();
  const [job, setJob] = useState<Job | null>(null);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState(true);
  const [jobStatus, setJobStatus] = useState<string>("");

  // Add state for candidate drawer
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<CandidateDetail | null>(null);

  // Add state for active tabs
  const [activeTab, setActiveTab] = useState("description");
  const [activeFilterTab, setActiveFilterTab] = useState("all");

  const { deleteJob } = useJobs();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchJobAndCandidates = async () => {
      setLoading(true);
      try {
        if (!jobId) {
          throw new Error("Job ID is required");
        }
        // Fetch actual job data from API
        const fetchedJob = await getJobById(jobId);
        if (!fetchedJob) {
          throw new Error("Job not found");
        }
        setJob(fetchedJob);
        setJobStatus(fetchedJob.status || "published");
        // Fetch real candidates for this job
        const result = await getCandidatesByJobId(jobId);
        if (result && result.candidates) {
          setCandidates(result.candidates);
        } else {
          setCandidates([]);
        }
      } catch (error) {
        setJob(null);
        setJobStatus("");
        setCandidates([]);
        toast.error("Job not found", {
          description: "The requested job could not be found in the database.",
        });
      } finally {
        setLoading(false);
      }
    };
    fetchJobAndCandidates();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobId, getJobById]);

  const updateSelectedCandidate = (candidate: CandidateDetail) => {
    setSelectedCandidate(candidate);
  };

  // Filtering candidates by status for tabs
  const filteredCandidates =
    activeFilterTab === "all" ? candidates : candidates.filter(c => c.status === activeFilterTab);

  // Function to handle viewing a candidate profile
  const handleViewCandidate = async (candidateId: string) => {
    try {
      // Find the candidate in the current list
      const candidate = candidates.find(c => c.id === candidateId);

      if (!candidate) return;

      // Commented out Supabase integration for future use
      // In a real app, fetch additional candidate details from the database
      // const { data, error } = await supabase
      //   .from('candidate_details')
      //   .select('*')
      //   .eq('candidate_id', candidateId)
      //   .single();

      // if (error) throw error;

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Mock detailed candidate data
      const mockDetailedCandidate: CandidateDetail = {
        id: candidate.id,
        name: candidate.name,
        email: candidate.email,
        phone: candidate.phone || "",
        jobTitle: job?.title || "",
        location: candidate.location,
        status: candidate.status as any,
        matchScore: candidate.matchScore || 0,
        appliedDate: candidate.appliedDate,
        lastUpdated: new Date().toISOString(),
        skills: ["JavaScript", "React", "TypeScript", "Node.js"],
        experience: "5+ years of software development",
        education: ["Bachelor's in Computer Science, Stanford University"],
        resumeUrl: candidate.resume_url,
        interviews: [
          {
            round: 1,
            title: "Initial Screening",
            date: "2023-05-10",
            interviewer: "HR Manager",
            feedback: "Good communication skills, technical knowledge needs verification.",
            result: "pass",
          },
        ],
        notes: "Promising candidate with relevant experience.",
        source: "LinkedIn",
      };

      setSelectedCandidate(mockDetailedCandidate);
      setDrawerOpen(true);
    } catch (error) {
      console.error("Error fetching candidate details:", error);
      toast.error("Failed to load candidate details", {
        description: "Please try again later",
      });
    }
  };

  // Function to handle job status change - updated to use API
  const handleStatusChange = async (status: string) => {
    if (!jobId || !job) {
      toast.error("Job information not available");
      return;
    }

    try {
      // Call the updateJob function from the hook
      const success = await updateJob(jobId, {
        status: status as "draft" | "published" | "closed" | "open",
      });

      if (success) {
        // Update local state
        setJobStatus(status);
        setJob(prevJob => (prevJob ? { ...prevJob, status: status as any } : null));

        toast.success("Job status updated successfully", {
          description: `Job status changed to ${
            statusOptions.find(option => option.value === status)?.label || status
          }`,
        });
      } else {
        throw new Error("Failed to update job status");
      }
    } catch (error) {
      console.error("Error updating job status:", error);
      toast.error("Failed to update job status", {
        description: "Please try again later",
      });
    }
  };

  const [isFormSheetOpen, setIsFormSheetOpen] = useState(false);

  const handleEditForm = () => {
    setIsFormSheetOpen(true);
  };

  const handleDownloadCVs = async () => {
    await downloadAllResumes(candidates);
  };

  if (loading) {
    return <div className="p-8 text-center">Loading job details...</div>;
  }

  if (!job) {
    return <div className="p-8 text-center">Job not found</div>;
  }

  const isJobAssociated = candidates.length !== 0;

  const handleDeleteJob = async () => {
    if (jobId) {
      const success = await deleteJob(jobId);
      if (success) {
        navigate("/jobs");
      }
    }
  };

  const handleClose = async () => {
    try {
      const fetchedJob = await getJobById(jobId);
      if (!fetchedJob) {
        throw new Error("Job not found");
      }
      setJob(fetchedJob);
      setJobStatus(fetchedJob.status || "published");
    } catch {
      setJob(null);
      setJobStatus("");
      setCandidates([]);
      toast.error("Job could not be edited", {
        description: "The requested job could not be edited in the database.",
      });
    } finally {
      setIsFormSheetOpen(false);
    }
  };

  return (
    <div className="container py-6">
      <div className="flex flex-col gap-6">
        {/* Header with back button and job title */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={() => navigate("/jobs")}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold">{job.title}</h1>
          </div>

          <div className="flex items-center gap-2">
            {isJobAssociated && (
              <Button variant="outline" size="sm" onClick={handleDownloadCVs} className="gap-2">
                Bulk Download
              </Button>
            )}
            <Sheet open={isFormSheetOpen} onOpenChange={setIsFormSheetOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleEditForm} className="gap-2">
                  Edit Job
                  <Pencil className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent className="w-full min-w-[50vw] max-w-[90%] sm:max-w-[80%] lg:max-w-[1100px] p-0 overflow-y-auto">
                <JobFormSheet job={job} onClose={() => handleClose()} />
              </SheetContent>
            </Sheet>

            {!isJobAssociated && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" className="ml-2">
                    <Trash2 className="mr-2 h-4 w-4" /> Delete Job
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete this job and remove
                      its data from our servers.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDeleteJob}>Continue</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            {/* Job status dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="gap-2" disabled={updateLoading}>
                  {updateLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    statusOptions.find(option => option.value === (job.status || jobStatus))?.icon
                  )}
                  {updateLoading
                    ? "Updating..."
                    : statusOptions.find(option => option.value === (job.status || jobStatus))
                        ?.label || "Set Status"}
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {statusOptions.map(option => (
                  <DropdownMenuItem
                    key={option.value}
                    className="flex items-start gap-2 py-2 cursor-pointer"
                    onClick={() => handleStatusChange(option.value)}
                    disabled={updateLoading}
                  >
                    <div className="mt-0.5">{option.icon}</div>
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">{option.description}</div>
                    </div>
                    {(job.status || jobStatus) === option.value && (
                      <Check className="h-4 w-4 ml-auto text-green-600" />
                    )}
                  </DropdownMenuItem>
                ))}
                {/* Add separator before delete option */}
                <DropdownMenuSeparator />
                {/* Or if you want to conditionally render it only before delete: */}
                {statusOptions.some(option => option.value === "delete") && (
                  <DropdownMenuSeparator />
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Job details cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                Candidates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{candidates.length}</p>
              <p className="text-sm text-muted-foreground">Total applicants</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                Location
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg font-medium">{formatWorkLocation(job.location)}</p>
              <p className="text-sm text-muted-foreground">
                {job.employment_type &&
                  job.employment_type.charAt(0).toUpperCase() + job.employment_type.slice(1)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                Salary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg font-medium">
                {job.salary_min && job.salary_max
                  ? `$${job.salary_min.toLocaleString()} - $${job.salary_max.toLocaleString()}`
                  : job.salary_min
                  ? `$${job.salary_min.toLocaleString()}+`
                  : job.salary_max
                  ? `Up to $${job.salary_max.toLocaleString()}`
                  : "Competitive"}
              </p>
              <p className="text-sm text-muted-foreground">
                {job.employment_type &&
                  job.employment_type
                    .split("-")
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join("-")}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main content tabs with single line of tabs - Description is default */}
        <div className="border-b mb-4">
          <div className="flex justify-between items-center">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="border-0 bg-transparent h-auto p-0">
                <TabsTrigger
                  value="description"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
                >
                  Description
                </TabsTrigger>
                <TabsTrigger
                  value="candidates"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent pb-2"
                >
                  Candidates
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Conditional rendering of candidate filter tabs */}
            {activeTab === "candidates" && (
              <Tabs value={activeFilterTab} onValueChange={setActiveFilterTab}>
                <TabsList>
                  <TabsTrigger value="all" className="flex items-center gap-2">
                    All
                    <span className="flex items-center justify-center rounded-full bg-gray-200 text-gray-900 text-xs font-medium px-2 py-0.5">
                      {candidates.length}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger value="screening" className="flex items-center gap-2">
                    Screening
                    <span className="flex items-center justify-center rounded-full bg-purple-100 text-purple-900 text-xs font-medium px-2 py-0.5">
                      {candidates.filter(c => c.status === "screening").length}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger value="interview" className="flex items-center gap-2">
                    Interview
                    <span className="flex items-center justify-center rounded-full bg-amber-100 text-amber-900 text-xs font-medium px-2 py-0.5">
                      {candidates.filter(c => c.status === "interview").length}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger value="offer" className="flex items-center gap-2">
                    Offer
                    <span className="flex items-center justify-center rounded-full bg-green-100 text-green-900 text-xs font-medium px-2 py-0.5">
                      {candidates.filter(c => c.status === "offer").length}
                    </span>
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            )}
          </div>
        </div>

        {/* Content based on active tab */}
        {activeTab === "candidates" ? (
          <div className="mt-4">
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {candidatesLoading ? (
                    <div className="p-6 text-center text-muted-foreground">
                      Loading candidates...
                    </div>
                  ) : filteredCandidates.length > 0 ? (
                    filteredCandidates.map(candidate => (
                      <div
                        key={candidate.id}
                        className="p-4 flex flex-col md:flex-row md:items-center justify-between gap-4 hover:bg-slate-50 transition-colors duration-300"
                      >
                        <div>
                          <h3 className="font-medium">{candidate.name}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            {getStatusBadge(candidate.status)}
                            {candidate.matchScore && (
                              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                                {candidate.matchScore}% Match
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-2">
                            {candidate.location} • Applied{" "}
                            {new Date(candidate.appliedDate).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <FileText className="h-4 w-4 mr-1" />
                            Resume
                          </Button>
                          <Button variant="outline" size="sm">
                            <Mail className="h-4 w-4 mr-1" />
                            Contact
                          </Button>
                          <Button size="sm" onClick={() => handleViewCandidate(candidate.id)}>
                            View Profile
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-6 text-center text-muted-foreground">No candidates found</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <Card className="border-0 shadow-none mt-4">
            <CardContent className="p-0">
              {job ? (
                <JobDescription job={job} expanded={true} showStatus={false} />
              ) : (
                <div className="p-8 text-center">
                  <div className="text-lg font-semibold text-muted-foreground mb-2">
                    Job Not Found
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">
                    The requested job could not be found in the database.
                  </p>
                  <Button variant="outline" onClick={() => navigate("/jobs")}>
                    Back to Jobs
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
      {/* Add the candidate drawer component */}
      <CandidateDrawer
        candidates={candidates}
        setCandidates={setCandidates}
        setSelectedCandidate={updateSelectedCandidate}
        isOpen={drawerOpen}
        onOpenChange={setDrawerOpen}
        candidate={selectedCandidate}
      />
    </div>
  );
}
