import { logger } from '../utils/logger.js';
import { supabase } from '../config/db.js';

export const commonUtils = {
  async checkDataISExistByField(table, field, value) {
    try {
    const { data, error } = await supabase
    .from(table)
    .select(field)
    .eq(field, value)
    .eq("flag_deleted", false)

      if (error) {
        logger.error("Table error log: ", error);
        throw error;
      }

      return data && data.length > 0;
    } catch (error) {
      logger.error("Validation error log: ", error);
      throw error;
    }
  },
};