import { useState } from 'react';
import { toast } from '@/components/ui/use-toast';
import { API_CONFIG } from '@/utils/Constant';

interface AuthResponse {
  success: boolean;
  token?: string;
  message?: string;
  user?: {
    id: string;
    email: string;
    full_name: string;
    role_id: string;
    language_preference: string;
    flag_active: boolean;
    flag_deleted: boolean;
    role: {
      name: string;
    };
  };
}

interface RegisterData {
  email: string;
  password: string;
  full_name: string;
  role: string;
}

export const useAuth = () => {
  const [loading, setLoading] = useState(false);

  const login = async (email: string, password: string): Promise<AuthResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.AUTH.LOGIN}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Login failed');
      }

      // Store token in localStorage
      if (result.data?.token) {
        localStorage.setItem('token', result.data.token);
      }

      toast({
        title: "Success",
        description: "Logged in successfully!",
      });

      return {
        success: true,
        token: result.data?.token,
        user: result.data?.user,
      };

    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : 'Login failed',
      });
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Login failed',
      };
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterData): Promise<AuthResponse> => {
    setLoading(true);
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.AUTH.REGISTER}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.message || 'Registration failed');
      }

      toast({
        title: "Success",
        description: "Registration successful!",
      });

      return {
        success: true,
        user: responseData.user,
        message: 'Registration successful',
      };

    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : 'Registration failed',
      });
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Registration failed',
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    login,
    register,
    loading,
  };
};