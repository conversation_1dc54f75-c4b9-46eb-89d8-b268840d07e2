import { interviewerService } from '../services/interviewerService.js';
import { createInterviewerSchema, updateInterviewerSchema } from '../validation/interviewerValidation.js';

export const interviewerController = {
  async getInterviewers(req, res) {
    const result = await interviewerService.getInterviewers(req.query);
    res.json(result);
  },

  async getInterviewerById(req, res) {
    const result = await interviewerService.getInterviewerById(req.params.id);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  },

  async createInterviewer(req, res) {
    console.log('Received request body:', req.body);
    // Validate input using Zod
    const parsed = createInterviewerSchema.safeParse(req.body);
    if (!parsed.success) {
      console.error('Validation errors:', parsed.error.errors);
      return res.status(400).json({ success: false, message: 'Validation failed', errors: parsed.error.errors });
    }

    // Pass the raw validated data and the creator's user_id
    const createResult = await interviewerService.createInterviewer(parsed.data, req.user.id);
    if (!createResult.success) {
      return res.status(400).json(createResult);
    }
    res.status(201).json(createResult);
  },

  async updateInterviewer(req, res) {
    console.log('Received request body for update:', req.body);
    // Validate input using Zod
    const parsed = updateInterviewerSchema.safeParse(req.body);
    if (!parsed.success) {
      console.error('Validation errors:', parsed.error.errors);
      return res.status(400).json({ success: false, message: 'Validation failed', errors: parsed.error.errors });
    }
    const updateResult = await interviewerService.updateInterviewer(req.params.id, parsed.data);
    if (!updateResult.success) {
      return updateResult.status === 404 ? 
        res.status(404).json(updateResult) : 
        res.status(400).json(updateResult);
    }
    res.json(updateResult);
  },

  async deleteInterviewer(req, res) {
    const result = await interviewerService.deleteInterviewer(req.params.id);
    if (!result.success) {
      return res.status(404).json(result);
    }
    res.json(result);
  },

  async updateAvailability(req, res) {
    const result = await interviewerService.updateAvailability(req.params.id, req.body);
    if (!result.success) {
      return res.status(400).json(result);
    }
    res.json(result);
  }
}; 