import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { notificationService } from "../services/notificationService.js";

export const notificationController = {
  async getNotifications(req, res) {
    try {
      const { page = 1, limit = 10, ...filters } = req.query;
      const result = await notificationService.getNotifications(req.user._id, page, limit, filters);
      logger.info(`Notifications retrieved for user: ${req.user._id}`);
      return responseHandler.success(res, result);
    } catch (err) {
      logger.error("Failed to get notifications:", err);
      return responseHandler.error(res, "Failed to retrieve notifications");
    }
  },

  async sendNotification(req, res) {
    try {
      const result = await notificationService.sendNotification({
        ...req.body,
        created_by: req.user._id
      });
      
      if (!result.success) {
        logger.info(`Notification sending failed: ${result.message}`);
        return responseHandler.badRequest(res, result.message);
      }

      logger.info(`Notification sent to user: ${req.body.user_id}`);
      return responseHandler.success(res, { notification: result.notification }, 201);
    } catch (err) {
      logger.error("Failed to send notification:", err);
      return responseHandler.error(res, "Failed to send notification");
    }
  }
};