import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { reportService } from "../services/reportService.js";

export const reportController = {
  async getTimeToHireReport(req, res) {
    try {
      const filters = req.query;
      const result = await reportService.getTimeToHireReport(filters);
      logger.info("Time to hire report generated successfully");
      return responseHandler.success(res, result);
    } catch (err) {
      logger.error("Failed to generate time to hire report:", err);
      return responseHandler.error(res, "Failed to generate report");
    }
  },

  async getPanelistFeedbackReport(req, res) {
    try {
      const filters = req.query;
      const result = await reportService.getPanelistFeedbackReport(filters);
      logger.info("Panelist feedback report generated successfully");
      return responseHandler.success(res, result);
    } catch (err) {
      logger.error("Failed to generate panelist feedback report:", err);
      return responseHandler.error(res, "Failed to generate report");
    }
  },

  async getRecruiterDashboard(req, res) {
    try {
      const result = await reportService.getRecruiterDashboard(req.user._id);
      logger.info(`Dashboard data retrieved for recruiter: ${req.user._id}`);
      return responseHandler.success(res, result);
    } catch (err) {
      logger.error("Failed to get dashboard data:", err);
      return responseHandler.error(res, "Failed to retrieve dashboard data");
    }
  }
};