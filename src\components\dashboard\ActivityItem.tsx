
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

interface ActivityItemProps {
  avatar?: string;
  avatarFallback: string;
  title: React.ReactNode;
  description?: React.ReactNode;
  timestamp: string;
  className?: string;
  status?: "default" | "success" | "warning" | "error";
}

export function ActivityItem({
  avatar,
  avatarFallback,
  title,
  description,
  timestamp,
  className,
  status = "default",
}: ActivityItemProps) {
  return (
    <div className={cn("flex items-start gap-4 rounded-md p-3", className)}>
      <Avatar className={cn(
        status === "success" && "bg-green-100 text-green-700",
        status === "warning" && "bg-amber-100 text-amber-700",
        status === "error" && "bg-red-100 text-red-700",
        status === "default" && "bg-blue-100 text-blue-700",
      )}>
        <AvatarFallback>{avatarFallback}</AvatarFallback>
      </Avatar>
      <div className="grid gap-1">
        <p className="text-sm font-medium leading-none">{title}</p>
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
        <p className="text-xs text-muted-foreground">{timestamp}</p>
      </div>
    </div>
  );
}
