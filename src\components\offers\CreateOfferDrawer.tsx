import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from '@/components/ui/drawer';
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  She<PERSON><PERSON>itle,
  SheetFooter,
} from '@/components/ui/sheet';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/sonner';
import { Loader2, FileText, <PERSON>rkles } from 'lucide-react';

// Sample data for dropdowns
const candidateOptions = [
  { id: 'CAND-001', name: 'John Doe' },
  { id: 'CAND-002', name: 'Jane Smith' },
  { id: 'CAND-003', name: 'Michael Brown' },
  { id: 'CAND-004', name: 'Emily Wilson' },
];

const jobOptions = [
  { id: 'JOB-001', title: 'Frontend Developer' },
  { id: 'JOB-002', title: 'Backend Developer' },
  { id: 'JOB-003', title: 'UI/UX Designer' },
  { id: 'JOB-004', title: 'Project Manager' },
];

const departmentOptions = [
  'Engineering',
  'Design',
  'Product Management',
  'Marketing',
  'Sales',
  'Customer Support',
  'Human Resources',
];

const offerFormSchema = z.object({
  offer_id: z.string().optional(),
  candidate_id: z.string().min(1, 'Candidate Name is required'),
  job_id: z.string().min(1, 'Job is required'),
  recruiter_id: z.string().min(1, 'Recruiter ID is required'),
  department: z.string().min(1, 'Department is required'),
  designation: z.string().min(1, 'Designation is required'),
  employment_type: z.string().min(1, 'Employment type is required'),
  location: z.string().min(1, 'Location is required'),
  ctc_offered: z.coerce.number().min(0, 'CTC must be a positive number'),
  fixed_component: z.coerce
    .number()
    .min(0, 'Fixed component must be a positive number'),
  variable_component: z.coerce
    .number()
    .min(0, 'Variable component must be a positive number'),
  joining_bonus: z.coerce
    .number()
    .min(0, 'Joining bonus must be a positive number')
    .optional(),
  stock_options: z.string().optional(),
  benefits: z.string().optional(),
  expected_joining_date: z.string().optional(),
  offer_valid_till: z.string().optional(),
});

type OfferFormValues = z.infer<typeof offerFormSchema>;

interface CreateOfferDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateOfferDrawer({
  open,
  onOpenChange,
}: CreateOfferDrawerProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const isMobile = useIsMobile();

  const form = useForm<OfferFormValues>({
    resolver: zodResolver(offerFormSchema),
    defaultValues: {
      recruiter_id: 'REC-123', // auto-filled
      ctc_offered: 0,
      fixed_component: 0,
      variable_component: 0,
      joining_bonus: 0,
    },
  });

  const onSubmit = async (
    values: OfferFormValues,
    status: 'Draft' | 'Sent'
  ) => {
    setIsSubmitting(true);
    try {
      // Submit payload to backend API
      const payload = { ...values, offer_status: status };
      console.log('Submitting:', payload);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success(
        `Offer ${status === 'Draft' ? 'saved as draft' : 'sent successfully'}`,
        {
          description: `Offer for job ${values.job_id} has been ${
            status === 'Draft' ? 'saved' : 'sent'
          }.`,
        }
      );

      onOpenChange(false);
      form.reset();
    } catch (error) {
      toast.error('Failed to create offer', {
        description: 'There was an error creating the offer. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAIGenerate = () => {
    const values = form.getValues();
    console.log('AI Generating with form values:', values);
    toast.success('AI Offer Generation', {
      description: 'Generating offer letter with AI...',
    });
  };

  const Content = (
    <div className='space-y-6 px-1 py-4 overflow-y-auto'>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((values) => onSubmit(values, 'Sent'))}
        >
          <div className='space-y-6'>
            <Card>
              <CardContent className='space-y-4 p-6'>
                <h3 className='text-lg font-semibold'>Candidate & Job Info</h3>
                <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                  {/* Candidate ID Dropdown */}
                  <FormField
                    control={form.control}
                    name='candidate_id'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Candidate Name</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className='bg-white'>
                              <SelectValue placeholder='Select candidate' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {candidateOptions.map((candidate) => (
                              <SelectItem
                                key={candidate.id}
                                value={candidate.id}
                              >
                                {candidate.id} - {candidate.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Job ID Dropdown */}
                  <FormField
                    control={form.control}
                    name='job_id'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Offered Job</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className='bg-white'>
                              <SelectValue placeholder='Select job' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {jobOptions.map((job) => (
                              <SelectItem key={job.id} value={job.id}>
                                {job.id} - {job.title}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Department Dropdown */}
                  <FormField
                    control={form.control}
                    name='department'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Department</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className='bg-white'>
                              <SelectValue placeholder='Select department' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {departmentOptions.map((department) => (
                              <SelectItem key={department} value={department}>
                                {department}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='designation'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Designation</FormLabel>
                        <FormControl>
                          <Input
                            placeholder='Senior Frontend Developer'
                            {...field}
                            className='bg-white'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='employment_type'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Employment Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className='bg-white'>
                              <SelectValue placeholder='Select employment type' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value='Full-time'>Full-time</SelectItem>
                            <SelectItem value='Part-time'>Part-time</SelectItem>
                            <SelectItem value='Contract'>Contract</SelectItem>
                            <SelectItem value='Internship'>
                              Internship
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='location'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location</FormLabel>
                        <FormControl>
                          <Input
                            placeholder='Remote'
                            {...field}
                            className='bg-white'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Compensation & Benefits Section */}
            <Card>
              <CardContent className='space-y-4 p-6'>
                <h3 className='text-lg font-semibold'>
                  Compensation & Benefits
                </h3>
                <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                  <FormField
                    control={form.control}
                    name='ctc_offered'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CTC Offered</FormLabel>
                        <FormControl>
                          <Input
                            type='number'
                            {...field}
                            className='bg-white'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='fixed_component'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Fixed Component</FormLabel>
                        <FormControl>
                          <Input
                            type='number'
                            {...field}
                            className='bg-white'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='variable_component'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Variable Component</FormLabel>
                        <FormControl>
                          <Input
                            type='number'
                            {...field}
                            className='bg-white'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='joining_bonus'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Joining Bonus</FormLabel>
                        <FormControl>
                          <Input
                            type='number'
                            {...field}
                            className='bg-white'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='stock_options'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Stock Options</FormLabel>
                        <FormControl>
                          <Input
                            placeholder='RSUP or RSU details (if any)'
                            {...field}
                            className='bg-white'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='benefits'
                    render={({ field }) => (
                      <FormItem className='col-span-2'>
                        <FormLabel>Benefits</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder='Health insurance, work from home allowance, etc.'
                            className='min-h-[100px] bg-white'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Joining & Validity Section */}
            <Card>
              <CardContent className='space-y-4 p-6'>
                <h3 className='text-lg font-semibold'>Joining & Validity</h3>
                <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                  <FormField
                    control={form.control}
                    name='expected_joining_date'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Expected Joining Date</FormLabel>
                        <FormControl>
                          <Input type='date' {...field} className='bg-white' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='offer_valid_till'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Offer Valid Till</FormLabel>
                        <FormControl>
                          <Input type='date' {...field} className='bg-white' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className='flex justify-end gap-4 mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={() => onSubmit(form.getValues(), 'Draft')}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Saving...
                </>
              ) : (
                <>
                  <FileText className='mr-2 h-4 w-4' />
                  Save as Draft
                </>
              )}
            </Button>
            <Button type='submit' disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Creating...
                </>
              ) : (
                <>
                  <FileText className='mr-2 h-4 w-4' />
                  Create Offer
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );

  return (
    <>
      {isMobile ? (
        <Drawer open={open} onOpenChange={onOpenChange}>
          <DrawerContent>
            <DrawerHeader className='flex justify-between items-center'>
              <DrawerTitle>
                <div>Create Offer</div>
                <div className='mr-6'>
                  <Button
                    type='button'
                    variant='secondary'
                    onClick={handleAIGenerate}
                    disabled={isGenerating}
                  >
                    <Sparkles className='mr-2 h-4 w-4' />
                    {isGenerating ? (
                      <>
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                        Generating...
                      </>
                    ) : (
                      'AI Generate'
                    )}
                  </Button>
                </div>
              </DrawerTitle>
            </DrawerHeader>
            {Content}
          </DrawerContent>
        </Drawer>
      ) : (
        <Sheet open={open} onOpenChange={onOpenChange}>
          <SheetContent className='w-[90%] sm:max-w-3xl overflow-y-auto'>
            <SheetHeader>
              <SheetTitle className='flex justify-between items-center mb-2'>
                <div>Create Offer</div>
                <div className='mr-6'>
                  <Button
                    type='button'
                    variant='secondary'
                    onClick={handleAIGenerate}
                    disabled={isSubmitting}
                  >
                    <Sparkles className='mr-2 h-4 w-4' />
                    {isGenerating ? (
                      <>
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                        Generating...
                      </>
                    ) : (
                      'AI Generate'
                    )}
                  </Button>
                </div>
              </SheetTitle>
            </SheetHeader>
            {Content}
          </SheetContent>
        </Sheet>
      )}
    </>
  );
}
