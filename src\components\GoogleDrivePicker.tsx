import { useRef, useEffect, useState } from 'react';

const GOOGLE_CLIENT_ID = '1070096120961-7vdhuv4le4tfv04ud81ggad5h3cif8ks.apps.googleusercontent.com';
const GOOGLE_DEVELOPER_KEY = 'AIzaSyDqdTqkTOYJHHLQJOvWO6pKwzI7lI6TS5Q';

declare global {
  interface Window {
    gapi: any;
    google: any;
  }
}

export function GoogleDrivePicker({ onPicked }: { onPicked: (data: any) => void }) {
  const pickerApiLoaded = useRef(false);
  const oauthToken = useRef<string | null>(null);
  const [gapiReady, setGapiReady] = useState(false);

  useEffect(() => {
    const checkGapi = () => {
      if (window.gapi) setGapiReady(true);
      else setTimeout(checkGapi, 200);
    };
    checkGapi();
  }, []);

  // Load the Google Picker API
  const loadPicker = () => {
    if (!window.gapi) {
      alert('Google API script not loaded yet. Please wait a moment and try again.');
      return;
    }
    window.gapi.load('auth', { callback: onAuthApiLoad });
    window.gapi.load('picker', { callback: onPickerApiLoad });
  };

  const onAuthApiLoad = () => {
    window.gapi.auth.authorize(
      {
        client_id: GOOGLE_CLIENT_ID,
        scope: ['https://www.googleapis.com/auth/drive.file', 'https://www.googleapis.com/auth/drive.readonly'],
        immediate: false,
      },
      handleAuthResult
    );
  };

  const onPickerApiLoad = () => {
    pickerApiLoaded.current = true;
  };

  const handleAuthResult = (authResult: any) => {
    if (authResult && !authResult.error) {
      oauthToken.current = authResult.access_token;
      createPicker();
    }
  };

  const createPicker = () => {
    if (pickerApiLoaded.current && oauthToken.current) {
      const picker = new window.google.picker.PickerBuilder()
        .addView(window.google.picker.ViewId.DOCS)
        .addView(window.google.picker.ViewId.FOLDERS)
        .setOAuthToken(oauthToken.current)
        .setDeveloperKey(GOOGLE_DEVELOPER_KEY)
        .setCallback((data: any) => {
          if (data.action === window.google.picker.Action.PICKED) {
            onPicked(data.docs);
          }
        })
        .build();
      picker.setVisible(true);
    }
  };

  return (
    <button
      type="button"
      onClick={loadPicker}
      className="px-4 py-2 bg-blue-600 text-white rounded"
      disabled={!gapiReady}
    >
      {gapiReady ? 'Select from Google Drive' : 'Loading Google Drive...'}
    </button>
  );
} 