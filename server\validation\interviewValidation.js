import { z } from 'zod';

export const interviewValidation = {
  scheduleSchema: z.object({
    body: z.object({
      application_id: z.string().uuid('Invalid application ID'),
      interviewer_id: z.string().uuid('Invalid interviewer ID'),
      scheduled_time: z.string().datetime('Invalid date time format'),
      duration: z.number()
        .min(15, 'Interview must be at least 15 minutes')
        .max(180, 'Interview cannot exceed 3 hours'),
      location: z.string().optional(),
      interview_type: z.enum(['online', 'onsite']).default('online'),
      description: z.string().optional(),
      special_requirements: z.array(z.string()).optional()
    })
  }),

  feedbackSchema: z.object({
    params: z.object({
      interview_id: z.string().uuid('Invalid interview ID')
    }),
    body: z.object({
      rating: z.number()
        .min(1, 'Rating must be between 1 and 5')
        .max(5, 'Rating must be between 1 and 5'),
      skills_assessment: z.array(z.object({
        skill: z.string(),
        rating: z.number().min(1).max(5),
        comments: z.string().optional()
      })),
      strengths: z.array(z.string())
        .min(1, 'At least one strength must be provided'),
      weaknesses: z.array(z.string()).optional(),
      recommendation: z.enum(['hire', 'reject', 'consider'])
        .default('consider'),
      comments: z.string()
        .min(50, 'Comments must be at least 50 characters')
        .max(1000, 'Comments cannot exceed 1000 characters')
    })
  })
};