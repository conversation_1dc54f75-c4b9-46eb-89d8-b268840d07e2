import { userService } from '../services/userService.js';

export const userController = {
  async getUsers(req, res) {
    const result = await userService.getUsers(req.query);
    res.json(result);
  },

  async getUserById(req, res) {
    const result = await userService.getUserById(req.params.id);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  },

  async createUser(req, res) {
    const result = await userService.createUser(req.body);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.status(201).json(result);
  },

  async updateUser(req, res) {
    const result = await userService.updateUser(req.params.id, req.body);
    
    if (!result.success) {
      return result.status === 404 ? 
        res.status(404).json(result) : 
        res.status(400).json(result);
    }
    
    res.json(result);
  },

  async deleteUser(req, res) {
    const result = await userService.deleteUser(req.params.id);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  }
};