import { useState, useCallback } from 'react';
import { toast } from '@/components/ui/sonner';
import { USE_CANDIDATES_CONSTANTS, API_CONFIG } from '@/utils/Constant';

// Define the structure for interview scheduling data
export enum InterviewStage {
  Technical = 'Technical Round',
  FirstRound = 'First Round',
  SecondRound = 'Second Round',
  ThirdRound = 'Third Round',
  FinalRound = 'Final Round',
  HRRound = 'HR Round',
  PracticalRound = 'Practical Round',
}

export interface InterviewScheduleData {
  candidateId?: string;
  interviewType?: string;
  interviewStage?: InterviewStage;
  interviewers?: string[];
  duration?: string;
  date?: Date;
  time?: string;
  location?: string;
  videoLink?: string;
  selfScheduling?: boolean;
  availableFrom?: Date;
  availableTo?: Date;
  isRescheduled?: boolean;
  rescheduleNote?: string;
  sendEmail?: boolean;
  instructions?: string;
  userId?: string;
}

// Define the structure for generated meet link response
export interface MeetLinkResponse {
  success: boolean;
  meetLink?: string;
  interviewId?: string;
  message?: string;
}

// Define the structure for schedule details response
export interface ScheduleDetailsResponse {
  success: boolean;
  scheduleDetails?: {
    id: string;
    type: string;
    stage: string;
    video_link: string;
    special_requirements?: string;
    scheduled_date: string;
    scheduled_time: string;
    candidate: {
      id: string;
      name: string;
      email: string;
      can_status: string;
    };
    interviewer: {
      id: string;
      email: string;
      full_name: string;
    };
  };
  message?: string;
}

// Define the structure for final schedule response
export interface ScheduleResponse {
  success: boolean;
  interview?: {
    id: string;
    candidateId: string;
    interviewers: string[];
    date: string;
    time: string;
    duration: string;
    type: string;
    stage: string;
    location?: string;
    videoLink?: string;
    status: string;
  };
  message?: string;
}

export const useInterviews = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedMeetLink, setGeneratedMeetLink] = useState<string>('');
  const [scheduleDetails, setScheduleDetails] = useState<any>(null);
  const [generatedInterviewId, setGeneratedInterviewId] = useState<string>('');
  const [interviews, setInterviews] = useState<any[]>([]);
  const [interviewPagination, setInterviewPagination] = useState<any>(null);

  // Function to get the auth token
  const getAuthToken = useCallback(() => {
    const token = localStorage.getItem('token');
    return token;
  }, []);

  // Function to decode JWT token and get user ID
  const getUserIdFromToken = useCallback(() => {
    const token = getAuthToken();
    if (!token) {
      return null;
    }
    
    try {
      // Split the token and get the payload part
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }
      
      // Decode the payload
      const payload = JSON.parse(atob(parts[1]));
      
      // Try different possible user ID fields
      const userId = payload.user_id || payload.sub || payload.id || payload.userId;
      
      if (!userId) {
        return null;
      }
      
      return userId;
    } catch (error) {
      return null;
    }
  }, [getAuthToken]);

  // Function to get user ID from localStorage or token
  const getUserId = useCallback(() => {
    // First try to get from localStorage directly
    const storedUserId = localStorage.getItem('userId');
    if (storedUserId) {
      return storedUserId;
    }

    // Try to get from user data
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        if (user.id) {
          return user.id;
        }
      } catch (error) {
        return null;
      }
    }

    // Finally try to decode from token
    const tokenUserId = getUserIdFromToken();
    if (tokenUserId) {
      return tokenUserId;
    }

    return null;
  }, [getUserIdFromToken]);

  // Step 2: Generate Meet Link API
  const generateMeetLink = useCallback(async (scheduleData: InterviewScheduleData): Promise<MeetLinkResponse> => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return { success: false, message: 'Authentication token not found.' };
    }

    try {
      // The API expects different field names based on the service implementation
      const userId = getUserId();
      
      if (!userId) {
        // Fallback: use a default user ID for testing
        const fallbackUserId = '1'; // Default fallback
      }

      const requestBody = {
        user_id: userId || '1', // Use fallback if needed
        candidate_id: scheduleData.candidateId,
        interviewer_id: scheduleData.interviewers?.[0], // Take the first interviewer
        scheduled_time: scheduleData.date ? new Date(scheduleData.date).toISOString() : new Date().toISOString(),
        duration: scheduleData.duration,
        type: scheduleData.interviewType,
        stage: scheduleData.interviewStage,
        instructions: scheduleData.instructions,
      };

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWS}/generate-meet-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `Failed to generate meet link (${response.status})`);
      }

      // Extract meet link and interview ID from the response structure
      // Based on the actual API response format
      
      // Try multiple possible locations for the meet link
      const meetLink = data.data?.video_link || data.data?.meetLink || data.meetLink || data.video_link || '';
      const interviewId = data.data?.id || data.data?.interview_id || data.interview_id || data.id || '';
      
      if (!meetLink) {
        throw new Error('Failed to generate meet link. Please try again.');
      }
      
      setGeneratedMeetLink(meetLink);
      setGeneratedInterviewId(interviewId);
      toast.success('Meet Link Generated', {
        description: 'Video call link has been generated successfully.',
      });

      return { success: true, meetLink: meetLink, interviewId: interviewId };
    } catch (err: any) {
      setError(err.message);
      toast.error('Error', {
        description: `Failed to generate meet link: ${err.message}`,
      });
      return { success: false, message: err.message };
    } finally {
      setLoading(false);
    }
  }, [getAuthToken]);

  // Step 3: Schedule Details API (for manual scheduling)
  const saveScheduleDetails = useCallback(async (scheduleData: InterviewScheduleData): Promise<ScheduleDetailsResponse> => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return { success: false, message: 'Authentication token not found.' };
    }

    try {
      // Check if we have the generated interview ID
      if (!generatedInterviewId) {
        throw new Error('No interview ID found. Please generate meet link first.');
      }

      // Format the date and time for the API
      const scheduledDate = scheduleData.date?.toISOString().split('T')[0];
      const scheduledTime = scheduleData.time;
      const videoLink = scheduleData.videoLink || generatedMeetLink;

      if (!scheduledDate || !scheduledTime) {
        throw new Error('Date and time are required');
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWS}/${generatedInterviewId}/schedule-details`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          scheduled_date: scheduledDate,
          scheduled_time: scheduledTime,
          video_link: videoLink,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to save schedule details');
      }

      setScheduleDetails(data.data);
      toast.success('Schedule Details Saved', {
        description: 'Interview schedule details have been saved successfully.',
      });

      return { success: true, scheduleDetails: data.data };
    } catch (err: any) {
      setError(err.message);
      toast.error('Error', {
        description: `Failed to save schedule details: ${err.message}`,
      });
      return { success: false, message: err.message };
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, generatedMeetLink, generatedInterviewId]);

  // Step 4: Final Schedule API (send emails and calendar invites)
  const scheduleInterview = useCallback(async (scheduleData: InterviewScheduleData): Promise<ScheduleResponse> => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return { success: false, message: 'Authentication token not found.' };
    }

    try {
      // Use the interview ID from the generateMeetLink response
      if (!generatedInterviewId) {
        throw new Error('No interview ID found. Please generate meet link first.');
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWS}/schedule`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          interview_id: generatedInterviewId,
          can_status: 'interview', // Set candidate status to interview
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to schedule interview');
      }

      toast.success('Interview Scheduled', {
        description: 'Interview has been scheduled and notifications sent to all participants.',
      });

      return { success: true, interview: data.interview };
    } catch (err: any) {
      setError(err.message);
      toast.error('Error', {
        description: `Failed to schedule interview: ${err.message}`,
      });
      return { success: false, message: err.message };
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, generatedInterviewId]);

  // Self-scheduling API (for when candidate selects their own slot)
  const scheduleSelfScheduling = useCallback(async (scheduleData: InterviewScheduleData): Promise<ScheduleResponse> => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return { success: false, message: 'Authentication token not found.' };
    }

    try {
      // For self-scheduling, we need to create the interview first, then send the scheduling request
      // This is a simplified approach - in a real implementation, you might have a separate endpoint
      const userId = getUserId();
      if (!userId) {
        throw new Error('User ID not found');
      }

      // First, create the interview with self-scheduling flag
      const createResponse = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWS}/generate-meet-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          user_id: userId,
          candidate_id: scheduleData.candidateId,
          interviewer_id: scheduleData.interviewers?.[0],
          scheduled_time: new Date().toISOString(), // Placeholder time
          duration: scheduleData.duration,
          type: scheduleData.interviewType,
          stage: scheduleData.interviewStage,
          instructions: scheduleData.instructions,
          self_scheduling: true,
          available_from: scheduleData.availableFrom?.toISOString().split('T')[0],
          available_to: scheduleData.availableTo?.toISOString().split('T')[0],
        }),
      });

      const createData = await createResponse.json();

      if (!createResponse.ok) {
        throw new Error(createData.message || 'Failed to create self-scheduling interview');
      }

      toast.success('Scheduling Request Sent', {
        description: 'Scheduling request has been sent to the candidate.',
      });

      return { success: true, interview: createData.interview };
    } catch (err: any) {
      setError(err.message);
      toast.error('Error', {
        description: `Failed to send scheduling request: ${err.message}`,
      });
      return { success: false, message: err.message };
    } finally {
      setLoading(false);
    }
  }, [getAuthToken, generatedMeetLink, getUserIdFromToken]);

  // Reset function to clear state
  const reset = useCallback(() => {
    setGeneratedMeetLink('');
    setScheduleDetails(null);
    setGeneratedInterviewId('');
    setError(null);
  }, []);

  // Debug function to test authentication
  const fetchInterviews = useCallback(async (page = 1, limit = 10, filters = {}) => {
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...filters
  });

  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWS}?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data: any = await response.json(); // Adjust type as per your API response

  setInterviews(data.data.interviews || []);
  setInterviewPagination(data.data.pagination);
}
 catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch interviews';
      setError(errorMessage);
      if (page > 1) {
        toast.error('Failed to fetch interviews');
      }
    } finally {
      setLoading(false);
    }
  }, [getAuthToken]);

  const getInterviewById = useCallback(async (interviewId: string) => {
    setLoading(true);
    setError(null);
    const token = getAuthToken();
    if (!token) {
      setError('Authentication token not found.');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INTERVIEWS}/${interviewId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data.data.interview; // Assuming the API returns { interview: {...} }
    } catch (err: any) {
      setError(err.message);
      toast.error('Failed to fetch interview details');
      return null;
    } finally {
      setLoading(false);
    }
  }, [getAuthToken]);

  const debugAuth = useCallback(() => {
    const token = getAuthToken();
    const userId = getUserId();
  }, [getAuthToken, getUserId]);

  return {
    loading,
    error,
    generatedMeetLink,
    generatedInterviewId,
    scheduleDetails,
    interviews,
    interviewPagination,
    generateMeetLink,
    saveScheduleDetails,
    scheduleInterview,
    scheduleSelfScheduling,
    fetchInterviews,
    getInterviewById,
    reset,
    debugAuth, // Export debug function
  };
};

// Fetch all interviews for a candidate
export const getInterviewsByCandidateId = async (candidateId: string, token?: string) => {
  try {
    const authToken = token || localStorage.getItem('token');
    const API_BASE_URL = import.meta.env.VITE_API_BASE;
    if (!authToken) {
      throw new Error(USE_CANDIDATES_CONSTANTS.ERRORS.AUTH_TOKEN_NOT_FOUND);
    }
    const response = await fetch(`${API_BASE_URL}/interviews/candidate/${candidateId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });
    const result = await response.json();
    if (!response.ok || !result.success) {
      throw new Error(result.message || USE_CANDIDATES_CONSTANTS.ERRORS.FAILED_TO_FETCH_CANDIDATE);
    }
    // The data is nested as result.data.data
    return result.data?.data || [];
  } catch (error: any) {
    throw new Error(error.message || USE_CANDIDATES_CONSTANTS.ERRORS.FAILED_TO_FETCH_CANDIDATE);
  }
};