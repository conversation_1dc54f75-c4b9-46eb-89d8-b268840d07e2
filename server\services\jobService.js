import { supabase } from '../config/db.js';
import { logger } from '../utils/logger.js';
import { n8nIntegrator } from '../utils/n8nIntegrator.js';

const { N8N_JOB_CREATER_ID } = process.env;

export const jobService = {
  async getJobs({ page = 1, limit = 10, ...filters }) {
    try {
      const start = (page - 1) * limit;
      const end = start + limit - 1;

      let query = supabase
        .from('job_posts')
        .select(`
          *,
          creator:users!created_by(full_name, email)
        `, { count: 'exact' })
        .eq('flag_deleted', false)
        .order('created_at', { ascending: false })
        .range(start, end);

      // Apply filters
      if (filters.title) {
        query = query.ilike('title', `%${filters.title}%`);
      }
      if (filters.department) {
        query = query.eq('department', filters.department);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      const { data: jobs, error, count } = await query;

      if (error) throw error;

      return {
        jobs,
        pagination: {
          total: count,
          page: parseInt(page),
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to get jobs:', error);
      throw error;
    }
  },

  async getJobById(id) {
    try {
      const { data: job, error } = await supabase
        .from('job_posts')
        .select(`
          *,
          creator:users!created_by(full_name, email)
        `)
        .eq('id', id)
        .eq('flag_deleted', false)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Job not found' };
        }
        throw error;
      }

      return { success: true, job };
    } catch (error) {
      logger.error('Failed to get job by id:', error);
      throw error;
    }
  },

  async createJob(jobData) {
    try {
      let jobCreaterData = { ...jobData };
      
      // Call n8n workflow which will handle database insertion directly
      const n8nResponse = await n8nIntegrator.workflowTrigger(N8N_JOB_CREATER_ID, jobCreaterData);
      
      // Return the n8n response data
      return { 
        success: true, 
        message: 'Job created successfully via n8n workflow',
        n8nResponse: n8nResponse
      };
    } catch (error) {
      logger.error('Failed to create job:', error);
      throw error;
    }
  },

  async updateJob(id, updateData) {
    try {
      const { data: job, error } = await supabase
        .from('job_posts')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('flag_deleted', false)
        .select(`
          *,
          creator:users!created_by(full_name, email)
        `)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Job not found', status: 404 };
        }
        throw error;
      }

      return { success: true, job };
    } catch (error) {
      logger.error('Failed to update job:', error);
      throw error;
    }
  },

  async deleteJob(id, userId) {
    try {
      const { data: job, error } = await supabase
        .from('job_posts')
        .update({
          flag_deleted: true,
        })
        .eq('id', id)
        .eq('flag_deleted', false)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, message: 'Job not found', status: 404 };
        }
        throw error;
      }

      return { success: true };
    } catch (error) {
      logger.error('Failed to delete job:', error);
      throw error;
    }
  },

  async postToPlatform(id, platformData) {
    try {
      const { data: job, error: fetchError } = await supabase
        .from('job_posts')
        .select('*')
        .eq('id', id)
        .eq('flag_deleted', false)
        .single();

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          return { success: false, message: 'Job not found', status: 404 };
        }
        throw fetchError;
      }

      // Integrate with external job boards (LinkedIn, Indeed, etc.)
      const platforms = platformData.platforms || ['linkedin', 'indeed'];
      const postResults = await Promise.all(
        platforms.map(platform => 
          aiIntegrator.postJobToPlatform({
            platform,
            job
          })
        )
      );

      // Create job board posts in Supabase
      const { error: insertError } = await supabase
        .from('job_board_posts')
        .insert(
          postResults.map(result => ({
            job_post_id: id,
            platform: result.platform,
            external_url: result.post_url,
            posted_at: new Date().toISOString(),
            flag_active: true,
            flag_deleted: false
          }))
        );

      if (insertError) throw insertError;

      // Update job status
      const { error: updateError } = await supabase
        .from('job_posts')
        .update({
          status: 'published',
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (updateError) throw updateError;

      return { success: true, jobBoardPost: postResults };
    } catch (error) {
      logger.error('Failed to post job to platform:', error);
      throw error;
    }
  }
};