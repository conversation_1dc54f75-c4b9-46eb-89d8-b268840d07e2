
import { useState, ReactNode } from 'react';
import { Loader2, LucideIcon } from 'lucide-react';

import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import type { ButtonProps } from '@/components/ui/button';

interface FormModalProps {
  triggerText: string;
  title: string;
  description?: string;
  children: ReactNode;
  onSubmit: () => Promise<void> | void;
  submitText?: string;
  cancelText?: string;
  isSubmitting?: boolean;
  onOpenChange?: (open: boolean) => void;
  initialOpen?: boolean;
  icon?: LucideIcon;
  buttonVariant?: ButtonProps["variant"];
}

export function FormModal({
  triggerText,
  title,
  description,
  children,
  onSubmit,
  submitText = 'Submit',
  cancelText = 'Cancel',
  isSubmitting = false,
  onOpenChange,
  initialOpen = false,
  icon: Icon,
  buttonVariant = 'default',
}: FormModalProps) {
  const [open, setOpen] = useState(initialOpen);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (onOpenChange) {
      onOpenChange(newOpen);
    }
  };

  const handleSubmit = async () => {
    await onSubmit();
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant={buttonVariant}>
          {Icon && <Icon size={16} className="mr-2" />}
          {triggerText}
        </Button>
      </DialogTrigger>
      <DialogContent className='flex flex-col w-[90vw] max-h-[90vh] px-6 py-4'>
        <DialogHeader className='pb-2 border-b'>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <DialogBody>
          {children}
        </DialogBody>

        <DialogFooter className='gap-2 sm:gap-0 px-4 py-3 border-t bg-background'>
          <Button
            type='button'
            variant='outline'
            onClick={() => handleOpenChange(false)}
            className='hover:bg-muted transition-colors'
          >
            {cancelText}
          </Button>
          <Button
            type='button'
            disabled={isSubmitting}
            className='hover:bg-primary/90 transition-colors'
            onClick={handleSubmit}
          >
            {isSubmitting ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Processing...
              </>
            ) : (
              submitText
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
