import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from '@/components/ui/sonner';
import { LucideIcon } from 'lucide-react';

import { FormModal } from '@/components/common/FormModal';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import type { ButtonProps } from '@/components/ui/button';
import { useCandidates } from '@/hooks/useCandidates';
import { useJobs } from '@/hooks/useJobs';
import { ADD_CANDIDATE_CONSTANTS } from '@/utils/Constant';

const { VALIDATION, LABELS, PLACEHOLDERS, FILE_UPLOAD, MODAL, TOAST } = ADD_CANDIDATE_CONSTANTS;

// Form schema validation
const addCandidateSchema = z.object({
  name: z.string().min(2, { message: VALIDATION.NAME_MIN_LENGTH }),
  email: z.string().email({ message: VALIDATION.EMAIL_REQUIRED }),
  phone: z
    .string()
    .regex(/^[+]?\d{7,}$/,
      { message: VALIDATION.PHONE_INVALID })
    .optional()
    .or(z.literal('')),
  jobId: z.string({ required_error: VALIDATION.JOB_REQUIRED }),
  resume: z
    .instanceof(FileList)
    .refine((files) => files.length > 0, VALIDATION.RESUME_REQUIRED)
    .refine(
      (files) => files[0]?.size <= FILE_UPLOAD.MAX_SIZE,
      VALIDATION.FILE_SIZE_LIMIT
    )
    .refine(
      (files) => FILE_UPLOAD.ACCEPTED_TYPES.includes(files[0]?.type),
      VALIDATION.FILE_TYPE_RESTRICTION
    ),
  source: z.string().min(1, { message: VALIDATION.SOURCE_REQUIRED }),
});

type AddCandidateFormValues = z.infer<typeof addCandidateSchema>;

interface AddCandidateModalProps {
  onCandidateAdded?: () => void;
  icon?: LucideIcon;
  buttonText?: string;
  buttonVariant?: ButtonProps["variant"];
}

export function AddCandidateModal({
  onCandidateAdded,
  icon,
  buttonText = MODAL.DEFAULT_BUTTON_TEXT,
  buttonVariant = "default"
}: AddCandidateModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { createCandidate, loading: candidateLoading, error } = useCandidates();
  const { jobs, loading: jobsLoading } = useJobs();

  // Initialize form with default values
  const form = useForm<AddCandidateFormValues>({
    resolver: zodResolver(addCandidateSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      source: '',
    },
  });

  async function onSubmit(data: AddCandidateFormValues) {
    setIsSubmitting(true);
    try {
      const result = await createCandidate({
        name: data.name,
        email: data.email,
        phone: data.phone,
        jobId: data.jobId,
        resume: data.resume,
        source: data.source,
      });
      if (result && result.success) {
        toast.success(TOAST.SUCCESS.TITLE, {
          description: TOAST.SUCCESS.DESCRIPTION(data.name),
        });
        form.reset();
        if (onCandidateAdded) onCandidateAdded();
      } else {
        toast.error(TOAST.ERROR.TITLE, {
          description: result?.message || TOAST.ERROR.DESCRIPTION,
        });
      }
    } catch (error) {
      toast.error(TOAST.ERROR.TITLE, {
        description: error instanceof Error ? error.message : TOAST.ERROR.DESCRIPTION,
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <FormModal
      triggerText={buttonText}
      title={MODAL.TITLE}
      description={MODAL.DESCRIPTION}
      onSubmit={() => form.handleSubmit(onSubmit)()}
      submitText={MODAL.SUBMIT_TEXT}
      isSubmitting={isSubmitting || candidateLoading || jobsLoading}
      icon={icon}
      buttonVariant={buttonVariant}
    >
      <Form {...form}>
        <div className="space-y-6">
          <FormField
            control={form.control}
            name='name'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{LABELS.FULL_NAME}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={PLACEHOLDERS.FULL_NAME}
                    className='focus-visible:ring-2 focus-visible:ring-ring transition-colors'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{LABELS.EMAIL_ADDRESS}</FormLabel>
                <FormControl>
                  <Input
                    type='email'
                    placeholder={PLACEHOLDERS.EMAIL}
                    className='focus-visible:ring-2 focus-visible:ring-ring transition-colors'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='phone'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{LABELS.PHONE_NUMBER}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={PLACEHOLDERS.PHONE}
                    className='focus-visible:ring-2 focus-visible:ring-ring transition-colors'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='jobId'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{LABELS.POSITION}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={jobsLoading || jobs.length === 0}
                >
                  <FormControl>
                    <SelectTrigger className='focus-visible:ring-2 focus-visible:ring-ring transition-colors'>
                      <SelectValue placeholder={jobsLoading ? PLACEHOLDERS.LOADING_POSITIONS : (jobs.length === 0 ? PLACEHOLDERS.NO_POSITIONS : PLACEHOLDERS.SELECT_POSITION)} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {jobs.map((job) => (
                      <SelectItem key={job.id} value={job.id}>
                        {job.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='source'
            render={({ field }) => (
              <FormItem>
                <FormLabel>{LABELS.SOURCE}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={PLACEHOLDERS.SOURCE}
                    className='focus-visible:ring-2 focus-visible:ring-ring transition-colors'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='resume'
            render={({ field: { value, onChange, ...fieldProps } }) => (
              <FormItem>
                <FormLabel>{LABELS.RESUME_UPLOAD}</FormLabel>
                <FormControl>
                  <div className='flex flex-col gap-2'>
                    <Input
                      type='file'
                      accept='.pdf,.docx'
                      className='cursor-pointer focus-visible:ring-2 focus-visible:ring-ring transition-colors'
                      onChange={(e) => onChange(e.target.files)}
                      {...fieldProps}
                    />
                    <p className='text-xs text-muted-foreground'>
                      {FILE_UPLOAD.ACCEPTED_FORMATS}
                    </p>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </Form>
    </FormModal>
  );
}
