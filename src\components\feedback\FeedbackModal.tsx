
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import FeedbackPage from './FeedbackPage';

interface Interview {
  id: string;
  candidateName: string;
  position: string;
  date: string;
  interviewerId: string;
  type: string;
}

interface FeedbackModalProps {
  interview: Interview;
  trigger?: React.ReactNode;
  useFullPage?: boolean;
}

const FeedbackModal = ({ interview, trigger, useFullPage = false }: FeedbackModalProps) => {
  const [open, setOpen] = React.useState(false);
  const navigate = useNavigate();
  
  const handleTriggerClick = () => {
    if (useFullPage) {
      navigate(`/interviews/feedback/${interview.id}`);
    } else {
      setOpen(true);
    }
  };
  
  if (useFullPage) {
    return (
      <div onClick={handleTriggerClick}>
        {trigger || <Button>Submit Feedback</Button>}
      </div>
    );
  }
  
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        {trigger || <Button>Submit Feedback</Button>}
      </SheetTrigger>
      <SheetContent side="right" className="w-full sm:max-w-3xl overflow-y-auto">
        <FeedbackPage 
          interview={interview} 
          onClose={() => setOpen(false)}
        />
      </SheetContent>
    </Sheet>
  );
};

export default FeedbackModal;
