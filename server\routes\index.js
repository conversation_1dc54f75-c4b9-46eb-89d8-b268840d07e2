import authRoute from "./auth.js";
import usersRoute from "./users.js";
import rolesRoute from "./roles.js";
import jobsRoute from "./jobs.js";
import resumesRoute from "./resumes.js";
import interviewsRoute from "./interviews.js";
import offersRoute from "./offers.js";
import notificationsRoute from "./notifications.js";
import interviewersRoute from "./interviewers.js";
import candidateRoute from "./candidates.js";
import googleRoute from "./google.js";
import feedbackRoute from "./feedback.js";

const basePath = "/api";

export default function(app) {
    app.use(`${basePath}/auth`, authRoute);
    app.use(`${basePath}/users`, usersRoute);
    app.use(`${basePath}/roles`, rolesRoute);
    app.use(`${basePath}/jobs`, jobsRoute);
    app.use(`${basePath}/resumes`, resumesRoute);
    app.use(`${basePath}/interviews`, interviewsRoute);
    app.use(`${basePath}/offers`, offersRoute);
    app.use(`${basePath}/notifications`, notificationsRoute);
    app.use(`${basePath}/interviewers`, interviewersRoute);
    app.use(`${basePath}/feedback`, feedbackRoute);
    app.use(`${basePath}/candidates`, candidateRoute);
    app.use(`${basePath}/google`, googleRoute);
};