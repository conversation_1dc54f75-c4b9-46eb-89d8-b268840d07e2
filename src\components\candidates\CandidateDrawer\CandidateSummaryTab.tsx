import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";

interface CandidateSummaryTabProps {
  candidateDetail: any;
}

const CandidateSummaryTab: React.FC<CandidateSummaryTabProps> = ({ candidateDetail }) => {
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    return date.toLocaleDateString(undefined, options);
  };

  return (
    <>
      <Card className="border border-gray-100 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-base font-medium">Feedback Snapshot</h3>
            <Button variant="ghost" size="sm" className="h-8 gap-1">
              <Edit className="h-3.5 w-3.5" />
              <span className="text-xs">Edit</span>
            </Button>
          </div>
          {candidateDetail.notes ? (
            <p className="text-sm text-gray-600">{candidateDetail.notes}</p>
          ) : (
            <p className="text-sm text-gray-500 italic">No feedback provided yet.</p>
          )}
        </CardContent>
      </Card>

      <Card className="border border-gray-100 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-base font-medium">Experience</h3>
            <Button variant="ghost" size="sm" className="h-8 gap-1">
              <Edit className="h-3.5 w-3.5" />
              <span className="text-xs">Edit</span>
            </Button>
          </div>
          <div className="text-sm text-gray-600">
            <div className="mb-2">
              <span className="font-medium">Total Experience</span>
            </div>
            <p>{candidateDetail.experience}</p>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-100 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-base font-medium">Source</h3>
            <Button variant="ghost" size="sm" className="h-8 gap-1">
              <Edit className="h-3.5 w-3.5" />
              <span className="text-xs">Edit</span>
            </Button>
          </div>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Portal Owner</span>
              <div className="flex items-center gap-1">
                <span>{candidateDetail.source}</span>
                <span className="text-gray-500">
                  {formatDateTime(candidateDetail.appliedDate)}
                </span>
              </div>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Application</span>
              <div className="flex items-center gap-1">
                <span>{candidateDetail.source}</span>
                <span className="text-gray-500">
                  {formatDateTime(candidateDetail.lastUpdated)}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-100 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-base font-medium">Tags</h3>
            <Button variant="ghost" size="sm" className="h-8 gap-1">
              <Edit className="h-3.5 w-3.5" />
              <span className="text-xs">Edit</span>
            </Button>
          </div>
          <div className="flex flex-wrap gap-1 mt-2">
            {candidateDetail.skills.map((skill: string, index: number) => (
              <Badge key={index} variant="secondary">
                {skill}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default CandidateSummaryTab; 