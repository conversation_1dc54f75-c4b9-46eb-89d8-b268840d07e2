
import { useState } from 'react';
import { Check, AlertCircle, Search } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';

interface Interviewer {
  id: string;
  name: string;
  role: string;
  available: boolean;
  department?: string;
}

interface InterviewerAvailabilityProps {
  interviewers: Interviewer[];
  selectedInterviewers: string[];
  onChange: (selectedIds: string[]) => void;
}

const InterviewerAvailability = ({
  interviewers,
  selectedInterviewers,
  onChange,
}: InterviewerAvailabilityProps) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter interviewers based on search term
  const filteredInterviewers = interviewers.filter(interviewer => 
    interviewer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    interviewer.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Toggle interviewer selection
  const toggleInterviewer = (id: string) => {
    const isSelected = selectedInterviewers.includes(id);
    
    if (isSelected) {
      onChange(selectedInterviewers.filter(i => i !== id));
    } else {
      onChange([...selectedInterviewers, id]);
    }
  };

  // Selected interviewers display
  const selectedInterviewersCount = selectedInterviewers.length;
  const selectedInterviewerNames = selectedInterviewers
    .map(id => interviewers.find(i => i.id === id)?.name)
    .filter(Boolean)
    .join(', ');

  return (
    <div className="space-y-2">
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            className="w-full justify-between"
          >
            {selectedInterviewersCount > 0
              ? `${selectedInterviewersCount} interviewer${selectedInterviewersCount !== 1 ? 's' : ''} selected`
              : "Select interviewers"}
            <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0">
          <Command>
            <CommandInput 
              placeholder="Search interviewers..."
              value={searchTerm}
              onValueChange={setSearchTerm}
            />
            <CommandEmpty>No interviewers found.</CommandEmpty>
            <CommandGroup>
              {filteredInterviewers.map((interviewer) => (
                <CommandItem
                  key={interviewer.id}
                  value={interviewer.id}
                  onSelect={() => toggleInterviewer(interviewer.id)}
                  disabled={!interviewer.available}
                  className={cn(
                    "flex items-center gap-2 py-3",
                    !interviewer.available && "opacity-60"
                  )}
                >
                  <div className="flex items-center gap-3 flex-1">
                    <Avatar>
                      <AvatarFallback>
                        {interviewer.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <span className="font-medium">{interviewer.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {interviewer.role}
                        {interviewer.department && ` • ${interviewer.department}`}
                      </span>
                    </div>
                  </div>
                  <div className="ml-auto">
                    {interviewer.available ? (
                      <Badge 
                        variant="outline" 
                        className={cn(
                          selectedInterviewers.includes(interviewer.id) 
                            ? "bg-blue-100 text-blue-700 border-blue-200" 
                            : "bg-green-100 text-green-700 border-green-200"
                        )}
                      >
                        {selectedInterviewers.includes(interviewer.id) && (
                          <Check className="h-3 w-3 mr-1" />
                        )}
                        Available
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-gray-100 text-gray-600 border-gray-200">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Unavailable
                      </Badge>
                    )}
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Display selected interviewers as chips */}
      {selectedInterviewersCount > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedInterviewers.map(id => {
            const interviewer = interviewers.find(i => i.id === id);
            if (!interviewer) return null;
            
            return (
              <Badge key={id} variant="secondary" className="py-1">
                {interviewer.name}
                <button 
                  className="ml-1 hover:text-destructive" 
                  onClick={() => toggleInterviewer(id)}
                >
                  ×
                </button>
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default InterviewerAvailability;
