import winston from 'winston';
import 'winston-daily-rotate-file';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxFiles: '30d'
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxFiles: '30d'
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

export const loggerUtils = {
  logInfo(message, meta = {}) {
    logger.info(message, meta);
  },

  logError(message, error = {}) {
    logger.error(message, { error: error.message, stack: error.stack });
  },

  logAudit(action, userId, details = {}) {
    logger.info('AUDIT', {
      action,
      userId,
      ...details,
      timestamp: new Date().toISOString()
    });
  }
};

export { logger };