import { logger } from "../utils/logger.js";
import { responseHandler } from "../utils/responseHandler.js";
import { interviewService } from "../services/interviewService.js";
import { messages } from "../utils/messages.utils.js";
import { addInterviewStatus } from "../utils/interview.js";

export const interviewController = {
  async getInterviews(req, res) {
    try {
      const result = await interviewService.getInterviews(req.query);
      const interviewsWithStatus = (result.interviews || []).map(addInterviewStatus);
      const data = {
        ...result,
        interviews: interviewsWithStatus,
      };
      logger.info(messages.INTERVIEWS_RETRIEVED);
      return responseHandler.success(res, { ...data, message: messages.INTERVIEWS_RETRIEVED });
    } catch (err) {
      logger.error(messages.INTERVIEWS_RETRIEVE_FAILED, err);
      return responseHandler.error(res, messages.INTERVIEWS_RETRIEVE_FAILED);
    }
  },

  async scheduleInterview(req, res) {
    try {
      const { interview_id, can_status } = req.body;
      const result = await interviewService.scheduleInterview({
        interview_id,
        can_status,
        supabase: req.supabase,
      });
      if (!result.success) {
        logger.info(`Interview scheduling failed: ${result.message}`);
        return responseHandler.badRequest(res, result.message);
      }
      logger.info(`${messages.INTERVIEW_SCHEDULED} for interview: ${interview_id}`);
      return responseHandler.success(
        res,
        { interview: result.interview, message: messages.INTERVIEW_SCHEDULED },
        200
      );
    } catch (err) {
      logger.error(messages.INTERVIEW_SCHEDULE_FAILED, err);
      return responseHandler.error(res, messages.INTERVIEW_SCHEDULE_FAILED);
    }
  },

  async rescheduleInterview(req, res) {
    try {
      const result = await interviewService.rescheduleInterview(req.params.id, {
        ...req.body,
        updated_by: req.user._id,
      });

      if (!result.success) {
        logger.info(`Interview rescheduling failed: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`${messages.INTERVIEW_RESCHEDULED}: ${req.params.id}`);
      return responseHandler.success(res, {
        interview: result.interview,
        message: messages.INTERVIEW_RESCHEDULED,
      });
    } catch (err) {
      logger.error(messages.INTERVIEW_RESCHEDULE_FAILED, err);
      return responseHandler.error(res, messages.INTERVIEW_RESCHEDULE_FAILED);
    }
  },

  async submitFeedback(req, res) {
    try {
      const result = await interviewService.submitFeedback(req.params.id, {
        ...req.body,
        reviewer_id: req.user._id,
        created_by: req.user._id,
      });

      if (!result.success) {
        logger.info(`Feedback submission failed: ${result.message}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }

      logger.info(`${messages.INTERVIEW_FEEDBACK_UPDATED} for interview: ${req.params.id}`);
      return responseHandler.success(res, {
        feedback: result.feedback,
        message: messages.INTERVIEW_FEEDBACK_UPDATED,
      });
    } catch (err) {
      logger.error(messages.INTERVIEW_FEEDBACK_UPDATE_FAILED, err);
      return responseHandler.error(res, messages.INTERVIEW_FEEDBACK_UPDATE_FAILED);
    }
  },

  async approveCandidate(req, res) {
    try {
      const result = await interviewService.approveCandidate(req.params.id, req.body);
      if (!result.success) {
        logger.info(`Candidate approval failed: ${result.message}`);
        return responseHandler.badRequest(res, result.message);
      }
      logger.info(`${messages.INTERVIEW_APPROVE_SUCCESS} for interview: ${req.params.id}`);
      return responseHandler.success(res, {
        message: result.message || messages.INTERVIEW_APPROVE_SUCCESS,
      });
    } catch (err) {
      logger.error(messages.INTERVIEW_APPROVE_FAILED, err);
      return responseHandler.error(res, messages.INTERVIEW_APPROVE_FAILED);
    }
  },

  async declineCandidate(req, res) {
    try {
      const result = await interviewService.declineCandidate(req.params.id, req.body);
      if (!result.success) {
        logger.info(`Candidate decline failed: ${result.message}`);
        return responseHandler.badRequest(res, result.message);
      }
      logger.info(`${messages.INTERVIEW_DECLINE_SUCCESS} for interview: ${req.params.id}`);
      return responseHandler.success(res, {
        message: result.message || messages.INTERVIEW_DECLINE_SUCCESS,
      });
    } catch (err) {
      logger.error(messages.INTERVIEW_DECLINE_FAILED, err);
      return responseHandler.error(res, messages.INTERVIEW_DECLINE_FAILED);
    }
  },

  async confirmSlot(req, res) {
    try {
      const { candidateId, recruiterId, slot } = req.body;
      logger.info("Confirm slot request:", { candidateId, recruiterId, slot });
      if (!candidateId || !recruiterId || !slot) {
        return res.status(400).send("<h2>Invalid request. Missing required fields.</h2>");
      }
      const result = await interviewService.confirmSlot(candidateId, recruiterId, slot);
      if (!result.success) {
        return res.status(500).send(`<h2>Failed to confirm slot: ${result.message}</h2>`);
      }
      return res.send(
        "<h2>Your interview slot has been confirmed! You will receive a confirmation email soon.</h2>"
      );
    } catch (err) {
      logger.error("Failed to confirm slot:", err);
      return res.status(500).send("<h2>Failed to confirm slot. Please try again later.</h2>");
    }
  },

  async generateMeetLink(req, res) {
    try {
      const result = await interviewService.generateMeetLinkService({
        ...req.body,
        supabase: req.supabase,
      });
      if (!result.success) {
        logger.info(`Meet link generation failed: ${result.message}`);
        return responseHandler.badRequest(res, result.message);
      }
      logger.info(
        `${messages.GOOGLE_CALENDAR_EVENT_SUCCESS} and interview created with ID: ${result.interview_id}`
      );
      return responseHandler.success(res, result, 200);
    } catch (error) {
      logger.error(messages.GOOGLE_CALENDAR_EVENT_FAILED, error);
      return responseHandler.error(res, error.message || messages.GOOGLE_CALENDAR_EVENT_FAILED);
    }
  },

  async updateScheduleDetails(req, res) {
    try {
      const interviewId = req.params.id;
      const { scheduled_date, scheduled_time, video_link } = req.body;
      const result = await interviewService.updateScheduleDetails(interviewId, {
        scheduled_date,
        scheduled_time,
        video_link,
        supabase: req.supabase,
      });

      if (!result.success) {
        logger.info(`Schedule update failed: ${result.message}`);
        return responseHandler.badRequest(res, result.message);
      }
      logger.info(`${messages.INTERVIEW_SCHEDULED} for interview: ${interviewId}`);
      return responseHandler.success(res, result.interview, 200);
    } catch (error) {
      logger.error(messages.INTERVIEW_SCHEDULE_FAILED, error);
      return responseHandler.error(res, error.message || messages.INTERVIEW_SCHEDULE_FAILED);
    }
  },

  async getInterviewById(req, res) {
    try {
      const interviewId = req.params.id;
      const result = await interviewService.getInterviewById(interviewId);
      if (!result.success) {
        logger.info(`Failed to get interview by ID: ${interviewId}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }
      logger.info(`${messages.INTERVIEW_DETAILS_RETRIEVED} for interview: ${interviewId}`);
      return responseHandler.success(res, {
        interview: addInterviewStatus(result.interview),
        message: messages.INTERVIEW_DETAILS_RETRIEVED,
      });
    } catch (err) {
      logger.error(messages.INTERVIEW_RETRIEVE_FAILED, err);
      return responseHandler.error(res, messages.INTERVIEW_RETRIEVE_FAILED);
    }
  },

  async getInterviewByCandidateId(req, res) {
    try {
      const candidateId = req.params.candidateId;
      const result = await interviewService.getInterviewByCandidateId(candidateId);
      if (!result.success) {
        logger.info(`Failed to get interview for candidate: ${candidateId}`);
        return responseHandler.error(res, result.message, result.status || 400);
      }
      logger.info(
        `${messages.INTERVIEW_DETAILS_BY_CANDIDATE_RETRIEVED} for candidate: ${candidateId}`
      );
      // Map all interviews to required fields
      const data = result.interviews.map(interview => ({
        candidateName: interview.candidate?.name || "",
        meetLink: interview.video_link || "",
        scheduled_date: interview.scheduled_date || "",
        scheduled_time: interview.scheduled_time || "",
        interviewerName: interview.interviewer?.full_name || "",
        type: interview.type || "",
        stage: interview.stage || "",
      }));
      return responseHandler.success(res, {
        data,
        message: messages.INTERVIEW_DETAILS_BY_CANDIDATE_RETRIEVED,
      });
    } catch (err) {
      logger.error(messages.INTERVIEW_RETRIEVE_FAILED, err);
      return responseHandler.error(res, messages.INTERVIEW_RETRIEVE_FAILED);
    }
  },
};
