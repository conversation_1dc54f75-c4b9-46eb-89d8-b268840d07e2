import { logger } from '../utils/logger.js';

export const retryHandler = (retries = 3, delay = 1000) => {
  return async (req, res, next) => {
    let attempts = 0;

    const executeRequest = async () => {
      try {
        await next();
      } catch (error) {
        attempts++;
        
        if (attempts >= retries) {
          logger.error(`Request failed after ${retries} attempts:`, error);
          throw error;
        }

        logger.warn(`Retry attempt ${attempts} of ${retries}`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return executeRequest();
      }
    };

    return executeRequest();
  };
};