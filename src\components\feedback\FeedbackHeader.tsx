
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetClose } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import { Maximize2, X } from 'lucide-react';

interface FeedbackHeaderProps {
  candidateName: string;
  position: string;
  interviewDate: string;
  interviewType: string;
  candidateAvatar?: string;
  onClose: () => void;
  onSubmit: () => void;
}

const FeedbackHeader = ({
  candidateName,
  position,
  interviewDate,
  interviewType,
  candidateAvatar,
  onClose,
  onSubmit,
}: FeedbackHeaderProps) => {
  // Calculate how long ago the interview was
  const timeAgo = formatDistanceToNow(new Date(interviewDate), { addSuffix: true });
  
  // Get candidate initials for avatar fallback
  const initials = candidateName
    .split(' ')
    .map(name => name[0])
    .join('')
    .toUpperCase();
    
  return (
    <div className="border-b pb-4 mb-6">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <h2 className="text-2xl font-bold">Add Feedback - {position}</h2>
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            {interviewType}
          </Badge>
        </div>
        
      </div>
      
      <div className="flex items-center gap-3">
        <Avatar className="h-12 w-12">
          <AvatarImage src={candidateAvatar} alt={candidateName} />
          <AvatarFallback>{initials}</AvatarFallback>
        </Avatar>
        <div>
          <h3 className="text-lg font-medium">{candidateName}</h3>
          <div className="flex items-center gap-2">
            <p className="text-sm text-muted-foreground">{position}</p>
            <span className="text-muted-foreground">•</span>
            <p className="text-sm text-muted-foreground">Interviewed {timeAgo}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeedbackHeader;
